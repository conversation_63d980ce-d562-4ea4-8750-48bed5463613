"""
认证
"""
import base64
from datetime import datetime, timedelta

import jwt

SECRET: str = 'f8029d0782983c0411b672dad3f3999b5dba177cbab03fd6ff86f65cab3798af'
ALGORITHM: str = 'HS256'
ACCESS_TOKEN_EXPIRE_DAY = 7
TOKEN_REFRESH_THRESHOLD_DAYS = 5  # Refresh token if it expires within this many days

def create_access_token(
        user_biz_id: str,
        expires_delta: timedelta = timedelta(days=ACCESS_TOKEN_EXPIRE_DAY)
) -> (str, str):
    payload = {
        "exp": datetime.utcnow() + expires_delta,
        "sub": base64.b64encode(user_biz_id.encode('utf-8')).decode('utf-8')
    }
    encoded_jwt = jwt.encode(payload, SECRET, algorithm=ALGORITHM)
    return encoded_jwt, expires_delta.total_seconds()


def decode_access_token(encoded_jwt: str) -> str | None:
    try:
        payload: dict = jwt.decode(encoded_jwt, SECRET, algorithms=[ALGORITHM])
        b64_data: str = payload['sub']
        user_id: str = base64.b64decode(b64_data).decode('utf-8')
        return user_id
    except Exception:
        return None