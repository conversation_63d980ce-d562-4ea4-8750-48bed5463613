"""
授权
"""
from typing import Annotated

import redis.asyncio as aioredis
from fastapi import Depends
from fastapi.security import HTT<PERSON><PERSON>earer
from sqlalchemy.ext.asyncio import AsyncSession

from src.database import session_maker, myredis

# 实例化 HTTPBearer 对象
http_bearer = HTTPBearer()
DBSessionDep = Annotated[AsyncSession, Depends(session_maker.async_db)]
RedisSessionDep = Annotated[aioredis.Redis, Depends(myredis.async_redis)]

