from math import ceil

import redis.asyncio as redis
from fastapi import HTT<PERSON><PERSON>x<PERSON>
from fastapi_limiter import <PERSON><PERSON><PERSON><PERSON><PERSON>
from starlette.requests import Request
from starlette.responses import Response
from starlette.status import HTTP_429_TOO_MANY_REQUESTS
from src.util.TokenUtil import limit_current_user
from src.database import myredis


async def custom_http_callback(request: Request, response: Response, pexpire: int):
    route = request.url.path
    expire = ceil(pexpire / 1000)

    raise HTTPException(
        HTTP_429_TOO_MANY_REQUESTS,
        f"您的请求过于频繁, {expire}秒后重试", headers={"Retry-After": str(expire)}
    )


async def user_aware_identifier(request: Request):
    try:
        user = await limit_current_user(request)
        if user:
            return f"{user.biz_id}"
        return request.client.host
    except HTTPException:
        return request.client.host