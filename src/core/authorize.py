"""
授权
"""
from typing import Annotated

from fastapi import (
    <PERSON><PERSON>,
    Query,
    WebSocket,
    WebSocketException
)
from fastapi import HTTPException, status, Security, Depends
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials, SecurityScopes
from starlette.requests import Request

from config import settings
from src.core.security_token import decode_access_token
from src.database import enums
from src.services import user_service
from src.util.TokenUtil import get_current_user
from src.util.cacheUtil import ttl_cache
from src.util.redis_keys_util import key_open_user

# 实例化 HTTPBearer 对象
http_bearer = HTTPBearer()


async def get_current_user_id(
        credentials: HTTPAuthorizationCredentials = Security(http_bearer)
) -> str:
    user_id: str | None = decode_access_token(credentials.credentials)
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"Authorization": "Bear<PERSON>"},
        )
    return user_id


async def get_platform_user(
        security_scopes: SecurityScopes,
        user_id: str = Depends(get_current_user_id)
):
    redis_key = key_open_user.format(biz_id=user_id)
    account = ttl_cache.get(redis_key)
    if not account:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"Authorization": "Bearer"},
        )
    if security_scopes.scopes:
        code = enums.PlatformLevelEnum.get_code(security_scopes.scope_str)
        if code is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials",
                headers={"Authorization": "Bearer"},
            )
        if account.level < code:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials",
                headers={"Authorization": "Bearer"},
            )
    return account


async def get_backend_user(
        security_scopes: SecurityScopes,
        request: Request
):
    user = await get_current_user(request)

    all_flag = await user_service.system_user(user)
    if not all_flag:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="没有权限"
        )
    return user


async def web_socket_user(token):
    user_id: str | None = decode_access_token(token)
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"Authorization": "Bearer"},
        )
    account = ttl_cache.get(user_id)
    if not account:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"Authorization": "Bearer"},
        )

    return account


async def get_cookie_or_token(
        websocket: WebSocket,
        session: Annotated[str | None, Cookie()] = None,
        token: Annotated[str | None, Query()] = None,
):
    check_token = session or token
    if check_token is None:
        raise WebSocketException(code=status.WS_1008_POLICY_VIOLATION)
    if check_token != settings.LLM_SECRET_KEY:
        raise WebSocketException(code=status.WS_1008_POLICY_VIOLATION)
    return check_token
