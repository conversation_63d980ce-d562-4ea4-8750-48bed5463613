import json
from datetime import datetime, timezone

from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Request
from starlette.datastructures import QueryParams
from dataclasses import dataclass
from src.database import session_maker, models
from src.logger.logUtil import get_logger, LoggerName
from src.services import platform_user_service, folder_service
from src.util import MD5Util
from src.util.MD5Util import encode_map_to_string
from src.util.cacheUtil import open_api_cache
from src.util.commonutil import common_error
from src.util.dateUtil import get_current_time_in_beijing
from config import settings
from typing import ClassVar

logger = get_logger(LoggerName.openapi)


async def get_open_key_with_auth(request: Request, app_key: str, t: int, sign: str):
    # 获取时间年月日参数，以yyyyMMdd格式
    datestr = get_current_time_in_beijing().strftime("%Y%m%d")
    metadata = open_api_cache.get(app_key + "-" + datestr)
    if not metadata:
        async with session_maker.get_db_manager() as async_db:
            platform, user = await platform_user_service.get_key(async_db, app_key)
            if platform is None:
                return common_error("app_key 不存在")
            if user is None:
                return common_error("需要绑定用户信息")
            platform = await platform.serialize_data(platform)
            user = await user.serialize_data(user)
            platform["user"] = user
        open_api_cache.set(app_key + "-" + datestr, {"platform": platform})
        metadata = open_api_cache.get(app_key + "-" + datestr)

    current_timestamp = datetime.now(timezone.utc).timestamp()
    # 如果当前时间超过5分钟，签名失效
    if t + 300000 < current_timestamp:
        raise HTTPException(status_code=403, detail="签名时间超过5min")
    params: QueryParams = request.query_params
    data = {}
    data.update(params)
    data.pop("sign")
    if await request.body():
        try:
            data.update(await request.json())
        except json.JSONDecodeError:
            pass
    cal_sign = MD5Util.get_md5_hash(encode_map_to_string(data), metadata["platform"]["secret_key"])
    if cal_sign != sign:
        raise HTTPException(status_code=403, detail="签名错误")
    return metadata["platform"]


class OpenUser(models.PlatformUserModel):
    all_flag: bool = False
    keys: list[str] = []
    current_key_id: ClassVar[str] = None


async def get_open_user(
        request: Request, app_key: str, t: int, sign: str):
    platform = await get_open_key_with_auth(request, app_key, t, sign)
    if platform is None:
        return common_error("app_key 不存在")
    user_dict = platform.get("user")
    if user_dict is None:
        return common_error("无法识别用户信息")

    open_key_id = platform.get("biz_id")
    folders = user_dict.get("folders")
    keys = list()
    for item in user_dict.get("platform_keys", []):
        keys.append(item.get("biz_id"))
    if not folders:
        try:
            async with session_maker.get_db_manager() as async_db:
                await folder_service.init_folder(async_db, user_dict.get("biz_id"), open_key_id)
        except Exception:
            logger.exception(f"用户笔记本初始化失败：{open_key_id}")
        datestr = get_current_time_in_beijing().strftime("%Y%m%d")
        open_api_cache.delete(app_key + "-" + datestr)

    user = OpenUser(**user_dict)
    customers = settings.CUSTOMER_SERVICE_SEND.split(",")
    all_flag = customers and user.biz_id in customers
    user.all_flag = all_flag
    user.keys = keys
    user.current_key_id = open_key_id
    return user
