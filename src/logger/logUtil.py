import logging
from logging.handlers import RotatingFileHandler

from config import settings

class LoggerName:
    """
    日志命名
    日志拆分会根据日志命名拆分，如果命名过多，文件拆分后，容易丢失日志
    """
    backend = "backend"
    client = "client"
    openapi = "openapi"
    market = "market"



class FlushRotatingFileHandler(RotatingFileHandler):
    # 确保写入时立即打开文件且每条日志都 flush
    def emit(self, record):
        super().emit(record)
        self.flush()


def _create_formatter():
    return logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")


def _add_file_handler(logger: logging.Logger, path_filename: str):
    # 文件大小轮转，单位为字节，例如 50MB：
    max_bytes = 20 * 1024 * 1024
    backup_count = 30  # 最多保留 10 个备份文件

    file_handler = FlushRotatingFileHandler(
        filename=path_filename, maxBytes=max_bytes, backupCount=backup_count, encoding="utf-8"
    )
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(_create_formatter())
    logger.addHandler(file_handler)


def _add_stream_handler(logger: logging.Logger):
    if settings.LOG_TO_STREAM:
        stream_handler = logging.StreamHandler()
        stream_handler.setLevel(logging.INFO)
        stream_handler.setFormatter(_create_formatter())
        logger.addHandler(stream_handler)


def get_logger(name: str) -> logging.Logger:
    logger = logging.getLogger(name)
    if not logger.handlers:
        logger.setLevel(logging.INFO)
        _add_file_handler(logger, settings.LOG_TO_PATH)
        _add_stream_handler(logger)
    return logger


def get_celery_logger(name: str) -> logging.Logger:
    logger = logging.getLogger(name)
    if not logger.handlers:
        logger.setLevel(logging.INFO)
        _add_file_handler(logger, settings.LOG_TO_PATH.replace("web-", "celery-"))
        _add_stream_handler(logger)
    return logger
