import os
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText

from config import settings
from src.logger.logUtil import get_logger

logger = get_logger(__name__)


def send_email(
        receiver_email,
        subject,
        body,
        cc=None,
        smtp_server=settings.STMP_SERVER,
        port=settings.STMP_PORT,
        sender_email=settings.STMP_SENDER,
        sender_password=settings.STMP_PASSWORD,
):
    # 创建一个MIMEMultipart对象
    msg = MIMEMultipart()
    msg["From"] = sender_email
    msg["To"] = receiver_email
    if cc:
        msg["Cc"] = cc
    msg["Subject"] = subject

    # 添加邮件正文
    msg.attach(MIMEText(body, "html"))

    # 连接到SMTP服务器
    server = smtplib.SMTP(smtp_server, port, timeout=3)
    server.starttls()
    # 开启DEBUG模式
    server.set_debuglevel(0)

    # 登录SMTP服务器
    server.login(sender_email, sender_password)

    # 发送邮件
    text = msg.as_string()
    server.sendmail(sender_email, receiver_email, text)

    # 断开与SMTP服务器的连接
    server.quit()


def send_warn_mail(
        receiver_email,
):
    subject = "【AihaoJi】告警通知"
    body = "有任务执行失败或者等待任务超过2条，请及时关注。"
    try:
        if not settings.APP_DEBUG:
            send_email(receiver_email, subject, body, cc="<EMAIL>")
    except smtplib.SMTPConnectError as e:
        logger.exception(f"邮件发送失败，连接失败: {e.smtp_code}, {e.smtp_error}")
    except smtplib.SMTPAuthenticationError as e:
        logger.exception(f"邮件发送失败，认证错误: {e.smtp_code}, {e.smtp_error}")
    except smtplib.SMTPSenderRefused as e:
        logger.exception(f"邮件发送失败，发件人被拒绝: {e.smtp_code}, {e.smtp_error}")
    except smtplib.SMTPRecipientsRefused:
        logger.exception("邮件发送失败，收件人被拒绝:")
    except smtplib.SMTPDataError:
        logger.exception("邮件发送失败，数据接收拒绝:")
    except smtplib.SMTPException:
        logger.exception("邮件发送失败")
    except Exception:
        logger.exception("send verify email error")


def send_verify_code_mail(receiver_email, name: str, code: str):
    subject = "【AihaoJi】验证码"
    body = f"尊敬的{name}，您的验证码是{code}，请在10分钟内完成验证。"
    send_finish = False
    try:
        send_email(receiver_email, subject, body)
        send_finish = True
    except smtplib.SMTPConnectError as e:
        logger.exception(f"邮件发送失败，连接失败: {e.smtp_code}, {e.smtp_error}")
    except smtplib.SMTPAuthenticationError as e:
        logger.exception(f"邮件发送失败，认证错误: {e.smtp_code}, {e.smtp_error}")
    except smtplib.SMTPSenderRefused as e:
        logger.exception(f"邮件发送失败，发件人被拒绝: {e.smtp_code}, {e.smtp_error}")
    except smtplib.SMTPRecipientsRefused:
        logger.exception("邮件发送失败，收件人被拒绝:")
    except smtplib.SMTPDataError:
        logger.exception("邮件发送失败，数据接收拒绝:")
    except smtplib.SMTPException:
        logger.exception("邮件发送失败")
    except Exception:
        logger.exception("send verify email error")
    return send_finish


def send_verify_link_mail(receiver_email, name: str, verify_link: str):
    subject = "【AihaoJi】邮箱验证"
    try:
        path = os.path.dirname(os.path.abspath(__file__))
        with open(f"{path}/../../static/verify_email.html", "r", encoding="utf-8") as f:
            # 替换body中的{{verify_link}}为实际的验证链接
            body = f.read()
    except Exception:
        logger.exception("Error reading HTML file")
    else:
        body = body.replace("{{user_name}}", name)
        body = body.replace("{{verify_link}}", verify_link)
        try:
            send_email(receiver_email, subject, body)
        except smtplib.SMTPConnectError as e:
            logger.exception(f"邮件发送失败，连接失败: {e.smtp_code}, {e.smtp_error}")
        except smtplib.SMTPAuthenticationError as e:
            logger.exception(f"邮件发送失败，认证错误: {e.smtp_code}, {e.smtp_error}")
        except smtplib.SMTPSenderRefused as e:
            logger.exception(f"邮件发送失败，发件人被拒绝: {e.smtp_code}, {e.smtp_error}")
        except smtplib.SMTPRecipientsRefused:
            logger.exception("邮件发送失败，收件人被拒绝:")
        except smtplib.SMTPDataError:
            logger.exception("邮件发送失败，数据接收拒绝:")
        except smtplib.SMTPException:
            logger.exception("邮件发送失败")
        except Exception:
            logger.exception("send verify email error")


def send_finished_mail(receiver_email, task_name, task_id, user_name):
    subject = "【AihaoJi】任务完成通知"
    try:
        path = os.path.dirname(os.path.abspath(__file__))
        with open(f"{path}/../../static/task_finish.html", "r", encoding="utf-8") as f:
            # 替换body中的{{verify_link}}为实际的验证链接
            body = f.read()
    except Exception:
        logger.exception("Error reading HTML file")
    else:
        body = body.replace("{{task_name}}", task_name)
        body = body.replace("{{user_name}}", user_name)
        body = body.replace("{{task_id}}", task_id)
        try:
            send_email(receiver_email, subject, body)
        except smtplib.SMTPConnectError as e:
            logger.exception(f"邮件发送失败，连接失败: {e.smtp_code}, {e.smtp_error}")
        except smtplib.SMTPAuthenticationError as e:
            logger.exception(f"邮件发送失败，认证错误: {e.smtp_code}, {e.smtp_error}")
        except smtplib.SMTPSenderRefused as e:
            logger.exception(f"邮件发送失败，发件人被拒绝: {e.smtp_code}, {e.smtp_error}")
        except smtplib.SMTPRecipientsRefused:
            logger.exception("邮件发送失败，收件人被拒绝:")
        except smtplib.SMTPDataError:
            logger.exception("邮件发送失败，数据接收拒绝:")
        except smtplib.SMTPException:
            logger.exception("邮件发送失败")
        except Exception:
            logger.exception("send verify email error")


if __name__ == "__main__":
    send_warn_mail("<EMAIL>", "【AihaoJi】任务处理超时，请及时处理")
