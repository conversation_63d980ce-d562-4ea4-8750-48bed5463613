import aiohttp
from urllib.parse import urljoin

API_BASE_URL = 'https://api.weixin.qq.com/'
OPEN_BASE_URL = 'https://open.weixin.qq.com/connect/'


def get_key(source, code=None):
    redis_key = key_pc_wechat_access_token if code is None else key_pc_wechat_access_token_by_code.format(code=code)
    app_id = settings.WECHAT_APP_ID
    secret = settings.WECHAT_APP_SECRET
    if source.lower() in ['android', 'ios']:
        redis_key = key_app_wechat_access_token if code is None else key_app_wechat_access_token_by_code.format(
            code=code)
        app_id = settings.WECHAT_MOBILE_APP_ID
        secret = settings.WECHAT_MOBILE_APP_SECRET
    return redis_key, app_id, secret

async def cgi_bin_token():
    url = urljoin(
        API_BASE_URL,
        f'cgi-bin/token?grant_type=client_credential&appid={app_id}&secret={secret}'
    )
    timeout = aiohttp.ClientTimeout(total=10)
    async with aiohttp.ClientSession(timeout=timeout) as session:
        last_exception = None
        for _ in range(3):
            logger.warning(f'提交生图:第{_}次\n{prompt}')
            try:
                async with session.post(f'{endpoint}{url}', data=payload) as response:
                    if response.status != 200:
                        raise Exception(f'提交生图-错误:{url}-status:{response.status}-txt:{response.reason}')
                    json_data = await response.json()
                    code = json_data.get('code')
                    if code == 1:
                        return json_data
                    elif code == 22:
                        logger.warning(f'提交生图-队列中:{url}-status:{response.status}-txt:{json_data}')
                        return json_data
                    else:
                        msg = json_data.get('description')
                        raise Exception(f'提交生图结果-错误:{url}-status:{code}-txt:{msg}')
            except asyncio.TimeoutError as e:
                last_exception = e
                continue
            except Exception as ex:
                logger.error(ex)
                raise ex
        logger.error(last_exception)
        raise last_exception
