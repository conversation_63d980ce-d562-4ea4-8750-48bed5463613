import httpx

from src.constant.wanpan import ALI_WANPAN_DOWNLOAD_URL_HOST, ALI_WANPAN_REDIS_PREFIX
from src.util.cacheUtil import open_api_cache


async def get_user_drive(access_token):
    drive_url = "https://openapi.alipan.com/adrive/v1.0/user/getDriveInfo"
    headers = {"Authorization": f"Bearer {access_token}", "Content-Type": "application/json"}
    async with httpx.AsyncClient() as client:
        drive_resp = await client.post(drive_url, headers=headers)
    if drive_resp.status_code != 200:
        return False, drive_resp.status_code
    drive_info = drive_resp.json()
    if drive_info.get("code"):
        return False, drive_info.get("message")
    return True, drive_info


async def get_directory_file_list(access_token, drive_id, parent_file_id: str = "root", marker: str = ""):
    list_url = "https://openapi.alipan.com/adrive/v1.0/openFile/list"
    headers = {"Authorization": f"Bearer {access_token}", "Content-Type": "application/json"}
    data = {
        "drive_id": drive_id,
        "parent_file_id": parent_file_id,
        "category": "video,audio",
    }
    if marker:
        data["marker"] = marker
    async with httpx.AsyncClient() as client:
        list_resp = await client.post(list_url, headers=headers, json=data)
    if list_resp.status_code != 200:
        return False, "获取 list file 失败"
    list_info = list_resp.json()
    if list_info.get("code"):
        return False, list_info.get("message")
    return True, list_info


async def file_download_url(access_token: str, file_list):
    baseinfo_url = "https://openapi.alipan.com/adrive/v1.0/openFile/batch/get"
    headers = {"Authorization": f"Bearer {access_token}", "Content-Type": "application/json"}
    data = {"file_list": file_list}
    async with httpx.AsyncClient() as client:
        base = await client.post(baseinfo_url, headers=headers, json=data)
    if base.status_code != 200:
        return False, "获取 file_download_url 失败"
    base_info = base.json()
    if base_info.get("code"):
        return False, base_info.get("message")
    return True, base_info


async def get_download_url(access_token: str, drive_id: str, file_id: str):
    url = "https://openapi.alipan.com/adrive/v1.0/openFile/getDownloadUrl"
    headers = {"Authorization": f"Bearer {access_token}", "Content-Type": "application/json"}
    data = {"drive_id": drive_id, "file_id": file_id}
    async with httpx.AsyncClient() as client:
        resp = await client.post(url, headers=headers, json=data, timeout=10)
    if resp.status_code != 200:
        return False, "获取 file_download_url 失败"
    file_data = resp.json()
    if file_data.get("code"):
        return False, file_data.get("message")
    download_url = file_data.get("url")
    return download_url is not None, file_data


async def get_download_url_list(access_token: str, file_list):
    success, base_info = await file_download_url(access_token, file_list)
    if not success:
        return False, base_info

    vi, vip = await get_user_vip(access_token)
    response_data = list()
    for item in base_info.get("items"):
        drive_id = item.get("drive_id")
        file_id = item.get("file_id")
        try:
            success, download_data = await get_download_url(access_token, drive_id, file_id)
            if success:
                item["download_url"] = download_data.get("url")
                item["method"] = download_data.get("method")
                item["expiration"] = download_data.get("expiration")
                item["description"] = download_data.get("description")
                item["vip"] = vip if vi else {}

                open_api_cache.set(f"{ALI_WANPAN_REDIS_PREFIX}{drive_id}_{file_id}", item)
                response_data.append({
                    "url": f"{ALI_WANPAN_DOWNLOAD_URL_HOST}?drive_id={drive_id}&file_id={file_id}",
                    "download_url": download_data.get("url"),
                    "success": True,
                    "fs_id": file_id
                })
            else:
                raise Exception("生成 download_url 失败")
        except Exception:
            response_data.append({
                "url": f"{ALI_WANPAN_DOWNLOAD_URL_HOST}?drive_id={drive_id}&file_id={file_id}",
                "download_url": None,
                "message": "没有下载链接",
                "success": False,
                "fs_id": file_id
            })
    return True, response_data


async def get_user_vip(access_token):
    drive_url = "https://openapi.alipan.com/business/v1.0/user/getVipInfo"
    headers = {"Authorization": f"Bearer {access_token}", "Content-Type": "application/json"}
    async with httpx.AsyncClient() as client:
        resp = await client.post(drive_url, headers=headers)

    if resp.status_code != 200:
        return False, "获取vip信息失败"
    vip_data = resp.json()
    if vip_data.get("code"):
        return False, vip_data.get("message")
    return True, vip_data
