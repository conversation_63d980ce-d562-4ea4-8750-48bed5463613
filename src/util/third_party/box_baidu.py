from urllib.parse import urlencode

import httpx

from src.constant.wanpan import BAIDU_WANPAN_DOWNLOAD_URL_HOST, BAIDU_WANPAN_REDIS_PREFIX
from src.util.cacheUtil import open_api_cache


def query_param(access_token, fs_id_list: list[str]):
    str_list = list()
    for item in fs_id_list:
        str_list.append(str(item))
    fs_ids = ",".join(str_list)
    params = {
        "method": "filemetas",
        "fsids": f"[{fs_ids}]",
        "access_token": access_token,
        "thumb": 1,
        "dlink": 1,
        "extra": 1,
        "needmedia": 1,
        "detail": 1,
    }
    query_string = urlencode(params, doseq=True)
    return query_string


async def file_download_url_list(access_token: str, fs_id_list: list[str]):
    query_string = query_param(access_token, fs_id_list)
    url = f"https://pan.baidu.com/rest/2.0/xpan/multimedia?{query_string}"
    async with httpx.AsyncClient() as client:
        resp = await client.get(url)
    if resp.status_code != 200:
        return False, "获取 file_download_url 失败"
    data = resp.json()
    if data.get("errno") != 0:
        return False, data.get("errmsg")

    vi, vip = await get_user_info(access_token)
    # 0普通用户、1会员VIP用户、2超级会员SVIP用户
    response_data = list()
    for file_info in data.get("list"):
        dlink = file_info.get("dlink")
        fs_id = file_info.get("fs_id")
        if dlink:
            file_info["download_url"] = dlink
            file_info["access_token"] = access_token
            file_info["vip"] = vip if vi else {}
            open_api_cache.set(f"{BAIDU_WANPAN_REDIS_PREFIX}{fs_id}", file_info)
            response_data.append({
                "url": f"{BAIDU_WANPAN_DOWNLOAD_URL_HOST}?fs_id={fs_id}",
                "download_url": dlink,
                "success": True,
                "fs_id": fs_id
            })
        else:
            response_data.append({
                "url": f"{BAIDU_WANPAN_DOWNLOAD_URL_HOST}?fs_id={fs_id}",
                "success": False,
                "download_url": None,
                "message": "没有下载链接",
                "fs_id": fs_id
            })
    return True, response_data


async def get_user_info(access_token):
    uinfo_url = f"https://pan.baidu.com/rest/2.0/xpan/nas?access_token={access_token}&method=uinfo&vip_version=v2"
    async with httpx.AsyncClient() as client:
        resp = await client.get(uinfo_url)
    if resp.status_code != 200:
        return False, "获取 user_info 失败"
    uinfo = resp.json()
    if uinfo.get("errno") != 0:
        return False, uinfo.get("errmsg")
    return True, uinfo
