from datetime import datetime, timedelta

from appstoreserverlibrary.models.AccountTenure import AccountTenure
from appstoreserverlibrary.models.LifetimeDollarsPurchased import LifetimeDollarsPurchased
from appstoreserverlibrary.models.LifetimeDollarsRefunded import LifetimeDollarsRefunded
from appstoreserverlibrary.models.PlayTime import PlayTime


def get_account_age_category(create_time) -> AccountTenure:
    """
    根据账户创建时间计算账户年龄类别。

    Args:
        create_time (datetime): 账户的创建时间。

    Returns:
        int: 对应的账户年龄类别 (0-7)。
             0 表示未声明或 create_time 无效。
    """
    # 如果 create_time 为 None 或无效，返回 0 (未声明)
    if not create_time:
        return AccountTenure.ZERO_TO_THREE_DAYS

    # 获取当前时间
    now = datetime.now()

    # 计算账户年龄（天数）
    age_timedelta = now - create_time
    age_days = age_timedelta.days

    # 根据天数判断类别
    if age_days < 0:
        return AccountTenure.ZERO_TO_THREE_DAYS
    elif age_days <= 3:
        return AccountTenure.ZERO_TO_THREE_DAYS
    elif age_days <= 10:
        return AccountTenure.THREE_DAYS_TO_TEN_DAYS
    elif age_days <= 30:
        return AccountTenure.TEN_DAYS_TO_THIRTY_DAYS
    elif age_days <= 90:
        return AccountTenure.THIRTY_DAYS_TO_NINETY_DAYS
    elif age_days <= 180:
        return AccountTenure.NINETY_DAYS_TO_ONE_HUNDRED_EIGHTY_DAYS
    elif age_days <= 365:
        return AccountTenure.ONE_HUNDRED_EIGHTY_DAYS_TO_THREE_HUNDRED_SIXTY_FIVE_DAYS
    else:  # age_days > 365
        return AccountTenure.GREATER_THAN_THREE_HUNDRED_SIXTY_FIVE_DAYS


#
def get_account_play_time(account_age: AccountTenure) -> PlayTime:
    """
    根据账户创建时间计算账户年龄类别。

    Args:
        create_time (datetime): 账户的创建时间。

    Returns:
        int: 对应的账户年龄类别 (0-7)。
             0 表示未声明或 create_time 无效。
    """

    if account_age == AccountTenure.ZERO_TO_THREE_DAYS:
        return PlayTime.FIVE_TO_SIXTY_MINUTES
    elif account_age == AccountTenure.THREE_DAYS_TO_TEN_DAYS:
        return PlayTime.ONE_TO_SIX_HOURS
    elif account_age == AccountTenure.TEN_DAYS_TO_THIRTY_DAYS:
        return PlayTime.SIX_HOURS_TO_TWENTY_FOUR_HOURS
    elif account_age == AccountTenure.THIRTY_DAYS_TO_NINETY_DAYS:
        return PlayTime.ONE_DAY_TO_FOUR_DAYS
    elif account_age == AccountTenure.NINETY_DAYS_TO_ONE_HUNDRED_EIGHTY_DAYS:
        return PlayTime.FOUR_DAYS_TO_SIXTEEN_DAYS
    elif account_age == AccountTenure.ONE_HUNDRED_EIGHTY_DAYS_TO_THREE_HUNDRED_SIXTY_FIVE_DAYS:
        return PlayTime.OVER_SIXTEEN_DAYS
    else:  # age_days > 365
        return PlayTime.OVER_SIXTEEN_DAYS


def get_purchased(total_amount: float) -> LifetimeDollarsPurchased:
    """
    购买总额，将人民币金额转换为美元并返回对应的 LifetimeDollarsPurchased 枚举值

    Args:
        total_amount (float): 人民币金额

    Returns:
        LifetimeDollarsPurchased: 对应的美元消费等级
    """

    # 人民币转美元汇率
    CNY_TO_USD_RATE = 0.14  # 1 CNY ≈ 0.14 USD

    # 转换为美元
    usd_amount = total_amount * CNY_TO_USD_RATE


    if usd_amount < 0:
        return LifetimeDollarsPurchased.ZERO_DOLLARS
    elif usd_amount == 0:
        return LifetimeDollarsPurchased.ZERO_DOLLARS
    elif usd_amount < 50:
        return LifetimeDollarsPurchased.ONE_CENT_TO_FORTY_NINE_DOLLARS_AND_NINETY_NINE_CENTS
    elif usd_amount < 100:
        return LifetimeDollarsPurchased.FIFTY_DOLLARS_TO_NINETY_NINE_DOLLARS_AND_NINETY_NINE_CENTS
    elif usd_amount < 500:
        return LifetimeDollarsPurchased.ONE_HUNDRED_DOLLARS_TO_FOUR_HUNDRED_NINETY_NINE_DOLLARS_AND_NINETY_NINE_CENTS
    elif usd_amount < 1000:
        return LifetimeDollarsPurchased.FIVE_HUNDRED_DOLLARS_TO_NINE_HUNDRED_NINETY_NINE_DOLLARS_AND_NINETY_NINE_CENTS
    elif usd_amount < 2000:
        return LifetimeDollarsPurchased.ONE_THOUSAND_DOLLARS_TO_ONE_THOUSAND_NINE_HUNDRED_NINETY_NINE_DOLLARS_AND_NINETY_NINE_CENTS
    else:
        return LifetimeDollarsPurchased.TWO_THOUSAND_DOLLARS_OR_GREATER

def get_refunded(refund_amount: float) -> LifetimeDollarsRefunded:
    """
   将人民币退款金额转换为美元并返回对应的 LifetimeDollarsRefunded 枚举值

   Args:
       refund_amount (float): 人民币退款金额

   Returns:
       LifetimeDollarsRefunded: 对应的美元退款等级
   """
    try:
        # 获取实时汇率（复用 get_purchased 中的汇率获取逻辑）
        CNY_TO_USD_RATE = 0.14

        # 转换为美元
        usd_amount = refund_amount * CNY_TO_USD_RATE



        if usd_amount < 0:
            return LifetimeDollarsRefunded.ZERO_DOLLARS
        elif usd_amount == 0:
            return LifetimeDollarsRefunded.ZERO_DOLLARS
        elif usd_amount < 50:
            return LifetimeDollarsRefunded.ONE_CENT_TO_FORTY_NINE_DOLLARS_AND_NINETY_NINE_CENTS
        elif usd_amount < 100:
            return LifetimeDollarsRefunded.FIFTY_DOLLARS_TO_NINETY_NINE_DOLLARS_AND_NINETY_NINE_CENTS
        elif usd_amount < 500:
            return LifetimeDollarsRefunded.ONE_HUNDRED_DOLLARS_TO_FOUR_HUNDRED_NINETY_NINE_DOLLARS_AND_NINETY_NINE_CENTS
        elif usd_amount < 1000:
            return LifetimeDollarsRefunded.FIVE_HUNDRED_DOLLARS_TO_NINE_HUNDRED_NINETY_NINE_DOLLARS_AND_NINETY_NINE_CENTS
        elif usd_amount < 2000:
            return LifetimeDollarsRefunded.ONE_THOUSAND_DOLLARS_TO_ONE_THOUSAND_NINE_HUNDRED_NINETY_NINE_DOLLARS_AND_NINETY_NINE_CENTS
        else:
            return LifetimeDollarsRefunded.TWO_THOUSAND_DOLLARS_OR_GREATER

    except Exception as e:

        return LifetimeDollarsRefunded.ZERO_DOLLARS
