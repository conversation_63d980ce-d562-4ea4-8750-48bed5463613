from datetime import datetime, timedelta

from appstoreserverlibrary.models.AccountTenure import AccountTenure
from appstoreserverlibrary.models.PlayTime import PlayTime


def get_account_age_category(create_time) -> AccountTenure:
    """
    根据账户创建时间计算账户年龄类别。

    Args:
        create_time (datetime): 账户的创建时间。

    Returns:
        int: 对应的账户年龄类别 (0-7)。
             0 表示未声明或 create_time 无效。
    """
    # 如果 create_time 为 None 或无效，返回 0 (未声明)
    if not create_time:
        return AccountTenure.ZERO_TO_THREE_DAYS

    # 获取当前时间
    now = datetime.now()

    # 计算账户年龄（天数）
    age_timedelta = now - create_time
    age_days = age_timedelta.days

    # 根据天数判断类别
    if age_days < 0:
        return AccountTenure.ZERO_TO_THREE_DAYS
    elif age_days <= 3:
        return AccountTenure.ZERO_TO_THREE_DAYS
    elif age_days <= 10:
        return AccountTenure.THREE_DAYS_TO_TEN_DAYS
    elif age_days <= 30:
        return AccountTenure.TEN_DAYS_TO_THIRTY_DAYS
    elif age_days <= 90:
        return AccountTenure.THIRTY_DAYS_TO_NINETY_DAYS
    elif age_days <= 180:
        return AccountTenure.NINETY_DAYS_TO_ONE_HUNDRED_EIGHTY_DAYS
    elif age_days <= 365:
        return AccountTenure.ONE_HUNDRED_EIGHTY_DAYS_TO_THREE_HUNDRED_SIXTY_FIVE_DAYS
    else:  # age_days > 365
        return AccountTenure.GREATER_THAN_THREE_HUNDRED_SIXTY_FIVE_DAYS


#
def get_account_play_time(account_age:AccountTenure) -> PlayTime:
    """
    根据账户创建时间计算账户年龄类别。

    Args:
        create_time (datetime): 账户的创建时间。

    Returns:
        int: 对应的账户年龄类别 (0-7)。
             0 表示未声明或 create_time 无效。
    """

    if account_age == AccountTenure.ZERO_TO_THREE_DAYS:
        return PlayTime.FIVE_TO_SIXTY_MINUTES
    elif account_age == AccountTenure.THREE_DAYS_TO_TEN_DAYS:
        return PlayTime.ONE_TO_SIX_HOURS
    elif account_age == AccountTenure.TEN_DAYS_TO_THIRTY_DAYS:
        return PlayTime.SIX_HOURS_TO_TWENTY_FOUR_HOURS
    elif account_age == AccountTenure.THIRTY_DAYS_TO_NINETY_DAYS:
        return PlayTime.ONE_DAY_TO_FOUR_DAYS
    elif account_age == AccountTenure.NINETY_DAYS_TO_ONE_HUNDRED_EIGHTY_DAYS:
        return PlayTime.FOUR_DAYS_TO_SIXTEEN_DAYS
    elif account_age == AccountTenure.ONE_HUNDRED_EIGHTY_DAYS_TO_THREE_HUNDRED_SIXTY_FIVE_DAYS:
        return PlayTime.OVER_SIXTEEN_DAYS
    else:  # age_days > 365
        return PlayTime.OVER_SIXTEEN_DAYS


def get_Purchased(purchased:float)
