from datetime import datetime, timedelta

from appstoreserverlibrary.models.AccountTenure import AccountTenure
from appstoreserverlibrary.models.ConsumptionStatus import ConsumptionStatus
from appstoreserverlibrary.models.LifetimeDollarsPurchased import LifetimeDollarsPurchased
from appstoreserverlibrary.models.LifetimeDollarsRefunded import LifetimeDollarsRefunded
from appstoreserverlibrary.models.PlayTime import PlayTime
from appstoreserverlibrary.models.RefundPreference import RefundPreference

from src.model.order_manager import OrderStatus


def get_account_age_category(create_time) -> AccountTenure:
    """
    根据账户创建时间计算账户年龄类别。

    Args:
        create_time (datetime): 账户的创建时间。

    Returns:
        int: 对应的账户年龄类别 (0-7)。
             0 表示未声明或 create_time 无效。
    """
    # 如果 create_time 为 None 或无效，返回 0 (未声明)
    if not create_time:
        return AccountTenure.ZERO_TO_THREE_DAYS

    # 获取当前时间
    now = datetime.now()

    # 计算账户年龄（天数）
    age_timedelta = now - create_time
    age_days = age_timedelta.days

    # 根据天数判断类别
    if age_days < 0:
        return AccountTenure.ZERO_TO_THREE_DAYS
    elif age_days <= 3:
        return AccountTenure.ZERO_TO_THREE_DAYS
    elif age_days <= 10:
        return AccountTenure.THREE_DAYS_TO_TEN_DAYS
    elif age_days <= 30:
        return AccountTenure.TEN_DAYS_TO_THIRTY_DAYS
    elif age_days <= 90:
        return AccountTenure.THIRTY_DAYS_TO_NINETY_DAYS
    elif age_days <= 180:
        return AccountTenure.NINETY_DAYS_TO_ONE_HUNDRED_EIGHTY_DAYS
    elif age_days <= 365:
        return AccountTenure.ONE_HUNDRED_EIGHTY_DAYS_TO_THREE_HUNDRED_SIXTY_FIVE_DAYS
    else:  # age_days > 365
        return AccountTenure.GREATER_THAN_THREE_HUNDRED_SIXTY_FIVE_DAYS


#
def get_account_play_time(account_age: AccountTenure) -> PlayTime:
    """
    根据账户创建时间计算账户年龄类别。

    Args:
        create_time (datetime): 账户的创建时间。

    Returns:
        int: 对应的账户年龄类别 (0-7)。
             0 表示未声明或 create_time 无效。
    """

    if account_age == AccountTenure.ZERO_TO_THREE_DAYS:
        return PlayTime.FIVE_TO_SIXTY_MINUTES
    elif account_age == AccountTenure.THREE_DAYS_TO_TEN_DAYS:
        return PlayTime.ONE_TO_SIX_HOURS
    elif account_age == AccountTenure.TEN_DAYS_TO_THIRTY_DAYS:
        return PlayTime.SIX_HOURS_TO_TWENTY_FOUR_HOURS
    elif account_age == AccountTenure.THIRTY_DAYS_TO_NINETY_DAYS:
        return PlayTime.ONE_DAY_TO_FOUR_DAYS
    elif account_age == AccountTenure.NINETY_DAYS_TO_ONE_HUNDRED_EIGHTY_DAYS:
        return PlayTime.FOUR_DAYS_TO_SIXTEEN_DAYS
    elif account_age == AccountTenure.ONE_HUNDRED_EIGHTY_DAYS_TO_THREE_HUNDRED_SIXTY_FIVE_DAYS:
        return PlayTime.OVER_SIXTEEN_DAYS
    else:  # age_days > 365
        return PlayTime.OVER_SIXTEEN_DAYS


def get_purchased(total_amount: float) -> LifetimeDollarsPurchased:
    """
    购买总额，将人民币金额转换为美元并返回对应的 LifetimeDollarsPurchased 枚举值

    Args:
        total_amount (float): 人民币金额

    Returns:
        LifetimeDollarsPurchased: 对应的美元消费等级
    """

    # 人民币转美元汇率
    CNY_TO_USD_RATE = 0.14  # 1 CNY ≈ 0.14 USD

    # 转换为美元
    usd_amount = total_amount * CNY_TO_USD_RATE


    if usd_amount < 0:
        return LifetimeDollarsPurchased.ZERO_DOLLARS
    elif usd_amount == 0:
        return LifetimeDollarsPurchased.ZERO_DOLLARS
    elif usd_amount < 50:
        return LifetimeDollarsPurchased.ONE_CENT_TO_FORTY_NINE_DOLLARS_AND_NINETY_NINE_CENTS
    elif usd_amount < 100:
        return LifetimeDollarsPurchased.FIFTY_DOLLARS_TO_NINETY_NINE_DOLLARS_AND_NINETY_NINE_CENTS
    elif usd_amount < 500:
        return LifetimeDollarsPurchased.ONE_HUNDRED_DOLLARS_TO_FOUR_HUNDRED_NINETY_NINE_DOLLARS_AND_NINETY_NINE_CENTS
    elif usd_amount < 1000:
        return LifetimeDollarsPurchased.FIVE_HUNDRED_DOLLARS_TO_NINE_HUNDRED_NINETY_NINE_DOLLARS_AND_NINETY_NINE_CENTS
    elif usd_amount < 2000:
        return LifetimeDollarsPurchased.ONE_THOUSAND_DOLLARS_TO_ONE_THOUSAND_NINE_HUNDRED_NINETY_NINE_DOLLARS_AND_NINETY_NINE_CENTS
    else:
        return LifetimeDollarsPurchased.TWO_THOUSAND_DOLLARS_OR_GREATER

def get_refunded(refund_amount: float) -> LifetimeDollarsRefunded:
    """
   将人民币退款金额转换为美元并返回对应的 LifetimeDollarsRefunded 枚举值

   Args:
       refund_amount (float): 人民币退款金额

   Returns:
       LifetimeDollarsRefunded: 对应的美元退款等级
   """
    try:
        # 获取实时汇率（复用 get_purchased 中的汇率获取逻辑）
        CNY_TO_USD_RATE = 0.14

        # 转换为美元
        usd_amount = refund_amount * CNY_TO_USD_RATE



        if usd_amount < 0:
            return LifetimeDollarsRefunded.ZERO_DOLLARS
        elif usd_amount == 0:
            return LifetimeDollarsRefunded.ZERO_DOLLARS
        elif usd_amount < 50:
            return LifetimeDollarsRefunded.ONE_CENT_TO_FORTY_NINE_DOLLARS_AND_NINETY_NINE_CENTS
        elif usd_amount < 100:
            return LifetimeDollarsRefunded.FIFTY_DOLLARS_TO_NINETY_NINE_DOLLARS_AND_NINETY_NINE_CENTS
        elif usd_amount < 500:
            return LifetimeDollarsRefunded.ONE_HUNDRED_DOLLARS_TO_FOUR_HUNDRED_NINETY_NINE_DOLLARS_AND_NINETY_NINE_CENTS
        elif usd_amount < 1000:
            return LifetimeDollarsRefunded.FIVE_HUNDRED_DOLLARS_TO_NINE_HUNDRED_NINETY_NINE_DOLLARS_AND_NINETY_NINE_CENTS
        elif usd_amount < 2000:
            return LifetimeDollarsRefunded.ONE_THOUSAND_DOLLARS_TO_ONE_THOUSAND_NINE_HUNDRED_NINETY_NINE_DOLLARS_AND_NINETY_NINE_CENTS
        else:
            return LifetimeDollarsRefunded.TWO_THOUSAND_DOLLARS_OR_GREATER

    except Exception as e:

        return LifetimeDollarsRefunded.ZERO_DOLLARS

def get_consumption_status(duration_minutes: int,plan_point:int) -> ConsumptionStatus:
    """
    根据消费状态获取对应的 ConsumptionStatus 枚举值

    Args:
        consumption_status (int): 消费状态

    Returns:
        ConsumptionStatus: 对应的消费状态枚举值
    """
    try:
        if duration_minutes == 0:
            return ConsumptionStatus.NOT_CONSUMED
        elif duration_minutes >= plan_point:
            return ConsumptionStatus.FULLY_CONSUMED
        else:
            return ConsumptionStatus.PARTIALLY_CONSUMED
    except Exception as e:
        return ConsumptionStatus.PARTIALLY_CONSUMED


def get_refund_preference(order) -> RefundPreference:
    """获取退款偏好设置的原因说明"""
    try:


        if not order or not order.created_at:
            print('订单信息不完整')
            return RefundPreference.NO_PREFERENCE

        current_time = datetime.now()
        order_age_days = (current_time - order.created_at).days

        if order_age_days > 7:
            #f"订单已创建{order_age_days}天，超过7天退款期限"
            return RefundPreference.PREFER_DECLINE

        if order.status == OrderStatus.PENDING:
            #已经向苹果支付了订单，但是我们的后台解析订单失败
            return RefundPreference.PREFER_GRANT

        if plan.

        if is_vip_product(order.product_id):
            member = get_member_by_user_id(order.user_id)
            if member and member.is_active:
                return "VIP产品已激活使用"
            else:
                return "VIP产品未激活，支持退款"

        if is_consumable_product(order.product_id):
            if has_consumed_product(order.user_id, order.product_id):
                return "消耗品已使用"
            else:
                return "消耗品未使用，支持退款"

        return "订单在7天内且未使用，支持退款"

    except Exception as e:
        return f"判断失败: {str(e)}"