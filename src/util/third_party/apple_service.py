"""
Apple Pay 业务逻辑服务
"""
from appstoreserverlibrary.models.ConsumptionRequest import ConsumptionRequest
from appstoreserverlibrary.models.DeliveryStatus import DeliveryStatus
from appstoreserverlibrary.models.Platform import Platform
from appstoreserverlibrary.models.UserStatus import UserStatus
from src.util.third_party.apple_client import apple_client_manager
from src.util.third_party.apple_value import get_purchased, get_refunded, get_account_age_category, get_account_play_time
from src.model.order_manager import get_order_from_status, OrderStatus
from src.logger.logUtil import get_logger

logger = get_logger(__name__)

class ApplePayService:
    """Apple Pay 业务服务"""

    def __init__(self):
        self.client_manager = apple_client_manager

    def verify_transaction(self, transaction_id, environment=None):
        """验证交易"""
        api_client = self.client_manager.get_api_client(environment)
        if not api_client:
            raise Exception("Apple API 客户端未初始化")

        return api_client.get_transaction_info(transaction_id)

    def parse_transaction_info(self, signed_transaction_info, environment=None):
        """解析交易信息"""
        data_verifier = self.client_manager.get_data_verifier(environment)
        if not data_verifier:
            raise Exception("数据验证器未初始化")

        return data_verifier.verify_and_decode_signed_transaction(signed_transaction_info)

    def parse_notification(self, signed_payload, environment=None):
        """解析通知"""
        data_verifier = self.client_manager.get_data_verifier(environment)
        if not data_verifier:
            raise Exception("数据验证器未初始化")

        return data_verifier.verify_and_decode_notification(signed_payload)

    def send_consumption_data(self, transaction_id, user_id, environment=None):
        """发送消费数据"""
        api_client = self.client_manager.get_api_client(environment)
        if not api_client:
            raise Exception("Apple API 客户端未初始化")

        # 获取用户订单统计
        order_list = get_order_from_status(user_id, [OrderStatus.SUCCESS, OrderStatus.REFUNDED])

        # 计算统计信息
        total_amount = sum(float(order.amount) for order in order_list if order.status == OrderStatus.SUCCESS)
        refund_amount = sum(float(order.amount) for order in order_list if order.status == OrderStatus.REFUNDED)

        # 构建消费请求
        consumption_request = ConsumptionRequest()
        consumption_request.customerConsented = True
        consumption_request.consumptionStatus = 1  # 已消费
        consumption_request.platform = Platform.IOS
        consumption_request.sampleContentProvided = False
        consumption_request.deliveryStatus = DeliveryStatus.DELIVERED_TO_CUSTOMER
        consumption_request.userStatus = UserStatus.ACTIVE
        consumption_request.accountTenure = get_account_age_category()
        consumption_request.playTime = get_account_play_time()
        consumption_request.lifetimeDollarsPurchased = get_purchased(total_amount)
        consumption_request.lifetimeDollarsRefunded = get_refunded(refund_amount)

        # 发送到苹果
        api_client.send_consumption_data(transaction_id, consumption_request)
        logger.info(f"✅ 消费数据发送成功: {transaction_id}")

# 全局服务实例
apple_pay_service = ApplePayService()