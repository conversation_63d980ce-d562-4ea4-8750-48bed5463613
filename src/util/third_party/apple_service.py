"""
Apple Pay 业务服务层
"""
from typing import Optional, Dict, Any
from dataclasses import dataclass
from appstoreserverlibrary.models.ConsumptionRequest import ConsumptionRequest
from appstoreserverlibrary.models.DeliveryStatus import DeliveryStatus
from appstoreserverlibrary.models.Platform import Platform
from appstoreserverlibrary.models.UserStatus import UserStatus

from src.util.third_party.apple_client import apple_client_manager, AppleEnvironment
from src.model.order_manager import get_order_from_status, OrderStatus
from src.util.third_party.apple_value import get_purchased, get_refunded, get_account_age_category, get_account_play_time
from src.logger.logUtil import get_logger

logger = get_logger(__name__)

@dataclass
class TransactionResult:
    """交易结果"""
    success: bool
    transaction_info: Optional[Any] = None
    parsed_transaction: Optional[Any] = None
    error_message: Optional[str] = None

@dataclass
class NotificationResult:
    """通知结果"""
    success: bool
    notification_data: Optional[Any] = None
    error_message: Optional[str] = None

class ApplePayService:
    """Apple Pay 业务服务"""

    def __init__(self):
        self.client_manager = apple_client_manager

    def _parse_environment(self, environment_param: Optional[str]) -> Optional[AppleEnvironment]:
        """解析环境参数"""
        if environment_param is None:
            return None

        env_map = {
            "Sandbox": AppleEnvironment.SANDBOX,
            "sandbox": AppleEnvironment.SANDBOX,
            "Production": AppleEnvironment.PRODUCTION,
            "production": AppleEnvironment.PRODUCTION,
        }

        return env_map.get(environment_param)

    async def verify_transaction(self, transaction_id: str, environment: Optional[str] = None) -> TransactionResult:
        """验证交易"""
        try:
            env = self._parse_environment(environment)
            api_client = self.client_manager.get_api_client(env)
            data_verifier = self.client_manager.get_data_verifier(env)

            logger.info(f"🔍 验证交易: {transaction_id} (环境: {env.value if env else '默认'})")

            # 获取交易信息
            transaction_info = api_client.get_transaction_info(transaction_id)
            if not transaction_info:
                return TransactionResult(success=False, error_message="无法获取交易信息")

            # 解析交易信息
            parsed_transaction = data_verifier.verify_and_decode_signed_transaction(
                transaction_info.signedTransactionInfo
            )
            if not parsed_transaction:
                return TransactionResult(success=False, error_message="交易信息解析失败")

            logger.info(f"✅ 交易验证成功: {transaction_id}")
            return TransactionResult(
                success=True,
                transaction_info=transaction_info,
                parsed_transaction=parsed_transaction
            )

        except Exception as e:
            logger.error(f"❌ 交易验证失败: {e}")
            return TransactionResult(success=False, error_message=str(e))

    async def parse_notification(self, signed_payload: str, environment: Optional[str] = None) -> NotificationResult:
        """解析通知"""
        try:
            env = self._parse_environment(environment)
            data_verifier = self.client_manager.get_data_verifier(env)

            logger.info(f"📨 解析通知 (环境: {env.value if env else '默认'})")

            notification_data = data_verifier.verify_and_decode_notification(signed_payload)
            if not notification_data:
                return NotificationResult(success=False, error_message="通知解析失败")

            logger.info("✅ 通知解析成功")
            return NotificationResult(success=True, notification_data=notification_data)

        except Exception as e:
            logger.error(f"❌ 通知解析失败: {e}")
            return NotificationResult(success=False, error_message=str(e))

    async def send_consumption_data(self, transaction_id: str, user_id: str, environment: Optional[str] = None):
        """发送消费数据"""
        try:
            env = self._parse_environment(environment)
            api_client = self.client_manager.get_api_client(env)

            # 获取用户订单统计
            order_list = get_order_from_status(user_id, [OrderStatus.SUCCESS, OrderStatus.REFUNDED])
            total_amount = sum(float(order.amount) for order in order_list if order.status == OrderStatus.SUCCESS)
            refund_amount = sum(float(order.amount) for order in order_list if order.status == OrderStatus.REFUNDED)

            # 构建消费请求
            consumption_request = ConsumptionRequest()
            consumption_request.customerConsented = True
            consumption_request.consumptionStatus = 1
            consumption_request.platform = Platform.IOS
            consumption_request.sampleContentProvided = False
            consumption_request.deliveryStatus = DeliveryStatus.DELIVERED_TO_CUSTOMER
            consumption_request.userStatus = UserStatus.ACTIVE
            consumption_request.accountTenure = get_account_age_category()
            consumption_request.playTime = get_account_play_time()
            consumption_request.lifetimeDollarsPurchased = get_purchased(total_amount)
            consumption_request.lifetimeDollarsRefunded = get_refunded(refund_amount)

            # 发送到苹果
            api_client.send_consumption_data(transaction_id, consumption_request)

            logger.info(f"✅ 消费数据发送成功: {transaction_id}")
            logger.info(f"   总购买: {total_amount} CNY, 总退款: {refund_amount} CNY")

        except Exception as e:
            logger.error(f"❌ 消费数据发送失败: {e}")
            raise

# 全局服务实例
apple_pay_service = ApplePayService()