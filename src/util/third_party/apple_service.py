"""
Apple Pay 业务服务层（简化版）
"""
from typing import Optional
from appstoreserverlibrary.models.Environment import Environment
from appstoreserverlibrary.models.ConsumptionRequest import ConsumptionRequest
from appstoreserverlibrary.models.DeliveryStatus import DeliveryStatus
from appstoreserverlibrary.models.Platform import Platform
from appstoreserverlibrary.models.UserStatus import UserStatus
from sqlalchemy.ext.asyncio import AsyncSession

from src.model.lecture_plan_manager import get_lecture_plan_by_biz_id
from src.model.user_manager import get_user_by_id
from src.services.tasks_service import get_task_duration_minutes
from src.util.third_party.apple_client import apple_client_manager
from src.model.order_manager import get_order_from_status, OrderStatus, get_order_from_id
from src.util.third_party.apple_conversion_util import get_purchased, get_refunded, get_account_age_category, \
    get_account_play_time, get_consumption_status, get_refund_preference
from src.logger.logUtil import get_logger

logger = get_logger(__name__)

class ApplePayService:
    """Apple Pay 业务服务"""

    def __init__(self):
        self.client_manager = apple_client_manager

    def _parse_environment(self, environment_str: Optional[str]) -> Optional[Environment]:
        """解析环境字符串"""
        if environment_str == "Sandbox":
            return Environment.SANDBOX
        elif environment_str == "Production":
            return Environment.PRODUCTION
        else:
            return None

    def verify_transaction(self, transaction_id: str, environment: Optional[str] = None):
        """验证交易"""
        try:
            env = self._parse_environment(environment)
            api_client = self.client_manager.get_api_client(env)
            data_verifier = self.client_manager.get_data_verifier(env)

            if not api_client or not data_verifier:
                raise Exception("客户端初始化失败")

            # logger.info(f"🔍 验证交易: {transaction_id} (环境: {environment or '默认'})")

            # 获取交易信息
            transaction_info = api_client.get_transaction_info(transaction_id)
            if not transaction_info:
                return None, None, "无法获取交易信息"

            # 解析交易信息
            parsed_transaction = data_verifier.verify_and_decode_signed_transaction(transaction_info.signedTransactionInfo)
            if not parsed_transaction:
                return None, None, "交易信息解析失败"

            logger.info(f"✅ 交易验证成功: {transaction_id}")
            return transaction_info, parsed_transaction, None

        except Exception as e:
            logger.error(f"❌ 交易验证失败: {e}")
            return None, None, str(e)

    def parse_notification(self, signed_payload: str, environment: Optional[str] = None):
        """解析通知"""
        try:
            env = self._parse_environment(environment)
            data_verifier = self.client_manager.get_data_verifier(env)

            if not data_verifier:
                raise Exception("数据验证器初始化失败")

            # logger.info(f"📨 解析通知 (环境: {environment or '默认'})")

            notification_data = data_verifier.verify_and_decode_notification(signed_payload)
            if not notification_data:
                return None, "通知解析失败"

            logger.info("✅ 通知解析成功")
            return notification_data, None

        except Exception as e:
            logger.error(f"❌ 通知解析失败: {e}")
            return None, str(e)

    async def send_consumption_data(self,   async_db: AsyncSession, transaction_id: str, order_id: str, environment: Optional[str] = None):
        """发送消费数据"""
        try:
            env = self._parse_environment(environment)
            api_client = self.client_manager.get_api_client(env)

            if not api_client:
                raise Exception("API客户端初始化失败")

            # 1. 获取订单信息
            order = get_order_from_id(order_id)
            if not order:
                logger.warning(f"❌ 订单不存在: {order_id}")
                return

            # 2. 获取用户信息
            user = get_user_by_id(order.user_id)
            if not user:
                logger.warning(f"❌ 用户不存在: {order.user_id}")
                return

            plan = get_lecture_plan_by_biz_id(order.product_id)
            if not plan:
                logger.error(f"Plan not found for order {order}")
                return

            #3. 获取用户消耗时长信息
            total_duration, task_count = await get_task_duration_minutes(async_db, order.user_id)

            # 获取用户订单统计
            order_list = get_order_from_status(order_id, [OrderStatus.SUCCESS, OrderStatus.REFUNDED])
            total_amount = sum(float(order.amount) for order in order_list if order.status == OrderStatus.SUCCESS)
            refund_amount = sum(float(order.amount) for order in order_list if order.status == OrderStatus.REFUNDED)


            # 构建消费请求
            consumption_request = ConsumptionRequest()
            consumption_request.accountTenure = get_account_age_category(user.create_time)
            consumption_request.appAccountToken=order_id
            consumption_request.consumptionStatus = get_consumption_status(total_duration,plan.point)
            consumption_request.customerConsented = True
            consumption_request.deliveryStatus = DeliveryStatus.DELIVERED_AND_WORKING_PROPERLY
            consumption_request.lifetimeDollarsPurchased = get_purchased(total_amount)
            consumption_request.lifetimeDollarsRefunded = get_refunded(refund_amount)
            consumption_request.platform = Platform.APPLE
            consumption_request.playTime = get_account_play_time(consumption_request.accountTenure)
            consumption_request.refundPreference=get_refund_preference(order,plan,refund_amount, total_duration, task_count )#根据运营逻辑决定是否需要退款
            consumption_request.sampleContentProvided = True
            consumption_request.userStatus = UserStatus.ACTIVE# 账户活跃状态




            # 发送到苹果
            api_client.send_consumption_data(transaction_id, consumption_request)

            logger.info(f"✅ 消费数据发送成功: {transaction_id}")
            logger.info(f"   总购买: {total_amount} CNY, 总退款: {refund_amount} CNY")

        except Exception as e:
            logger.error(f"❌ 消费数据发送失败: {e}")
            raise

# 全局服务实例
apple_pay_service = ApplePayService()