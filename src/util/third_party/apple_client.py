"""
Apple Store Server API 客户端管理（简化版）
"""
from typing import Dict, Optional
from appstoreserverlibrary.api_client import AppStoreServerAPIClient
from appstoreserverlibrary.models.Environment import Environment
from appstoreserverlibrary.signed_data_verifier import SignedDataVerifier
from config import settings
from src.logger.logUtil import get_logger

logger = get_logger(__name__)

def load_root_certificates():
    """加载苹果根证书"""
    cert_paths = [
        'static/pay/AppleRootCA-G3.cer',
        'static/pay/AppleRootCA-G2.cer',
        'static/pay/AppleIncRootCertificate.cer',
    ]

    root_certs = []
    for cert_path in cert_paths:
        try:
            with open(cert_path, 'rb') as file:
                root_certs.append(file.read())
            logger.info(f"✅ 成功加载证书: {cert_path}")
        except FileNotFoundError:
            logger.error(f"❌ 找不到证书文件: {cert_path}")
        except Exception as e:
            logger.error(f"❌ 加载证书失败 {cert_path}: {str(e)}")

    return root_certs

def create_apple_api_client(environment=None):
    """
    创建 App Store Server API 客户端

    Args:
        environment: 环境选择，可选值：
                    - Environment.SANDBOX: 沙盒环境
                    - Environment.PRODUCTION: 生产环境
                    - None: 使用默认环境（根据 settings.APP_DEBUG 判断）
    """
    try:
        with open("static/pay/AuthKey_K2FBY6A2CU.p8", "r") as f:
            private_key = f.read()

        # 确定使用的环境
        if environment is None:
            target_environment = Environment.SANDBOX if settings.APP_DEBUG else Environment.PRODUCTION
        else:
            target_environment = environment

        # 使用官方库创建客户端
        client = AppStoreServerAPIClient(
            signing_key=private_key.encode('utf-8'),
            key_id=settings.APPLE_SECRET_KEY_ID,
            issuer_id=settings.APPLE_ISSUER_ID,
            bundle_id=settings.APPLE_BUNDLE_ID,
            environment=target_environment
        )

        logger.info(f"✅ Apple API 客户端创建成功 (环境: {target_environment})")
        return client

    except Exception as e:
        logger.error(f"❌ Apple API 客户端创建失败: {str(e)}")
        return None

def create_signed_data_verifier(environment=None):
    """创建 SignedDataVerifier 用于验证和解析 JWS"""
    try:
        # 加载苹果根证书
        root_certs = load_root_certificates()

        # 确定使用的环境
        if environment is None:
            target_environment = Environment.SANDBOX if settings.APP_DEBUG else Environment.PRODUCTION
        else:
            target_environment = environment

        # 根据官方文档，SignedDataVerifier 需要苹果的根证书
        verifier = SignedDataVerifier(
            root_certificates=root_certs,
            enable_online_checks=True,
            environment=target_environment,
            bundle_id=settings.APPLE_BUNDLE_ID,
            app_apple_id=settings.APPLE_APP_ID
        )

        logger.info(f"✅ SignedDataVerifier 创建成功 (环境: {target_environment})")
        return verifier

    except Exception as e:
        logger.error(f"❌ SignedDataVerifier 创建失败: {str(e)}")
        logger.error(f"详细错误信息: {repr(e)}")
        import traceback
        logger.error(f"堆栈跟踪: {traceback.format_exc()}")
        return None

class AppleClientManager:
    """简单的客户端管理器 - 支持多环境缓存"""

    def __init__(self):
        self._api_clients: Dict[Environment, AppStoreServerAPIClient] = {}
        self._data_verifiers: Dict[Environment, SignedDataVerifier] = {}

    def get_api_client(self, environment=None):
        """获取 API 客户端（带缓存）"""
        env = environment or (Environment.SANDBOX if settings.APP_DEBUG else Environment.PRODUCTION)

        if env not in self._api_clients:
            self._api_clients[env] = create_apple_api_client(env)

        return self._api_clients[env]

    def get_data_verifier(self, environment=None):
        """获取数据验证器（带缓存）"""
        env = environment or (Environment.SANDBOX if settings.APP_DEBUG else Environment.PRODUCTION)

        if env not in self._data_verifiers:
            self._data_verifiers[env] = create_signed_data_verifier(env)

        return self._data_verifiers[env]

    def clear_cache(self):
        """清除缓存"""
        self._api_clients.clear()
        self._data_verifiers.clear()
        logger.info("🧹 客户端缓存已清除")

# 全局实例（保持与原有代码兼容）
apple_api_client = create_apple_api_client(Environment.PRODUCTION)
signed_data_verifier = create_signed_data_verifier(Environment.PRODUCTION)

# 客户端管理器实例
apple_client_manager = AppleClientManager()