"""
Apple Store Server API 客户端管理
"""
import requests
from appstoreserverlibrary.api_client import AppStoreServerAPIClient
from appstoreserverlibrary.models.Environment import Environment
from appstoreserverlibrary.signed_data_verifier import SignedDataVerifier
from config import settings
from src.logger.logUtil import get_logger

logger = get_logger(__name__)

class AppleClientManager:
    """Apple 客户端管理器"""

    def __init__(self):
        self._api_client = None
        self._data_verifier = None
        self._root_certificates = None

    def load_root_certificates(self):
        """加载苹果根证书"""
        if self._root_certificates is not None:
            return self._root_certificates

        cert_paths = [
            'static/pay/AppleRootCA-G3.cer',
            'static/pay/AppleRootCA-G2.cer',
            'static/pay/AppleIncRootCertificate.cer',
        ]
        certs = []
        for path in cert_paths:
            try:
                with open(path, 'rb') as file:
                    certs.append(file.read())
                logger.info(f"✅ 成功加载证书: {path}")
            except FileNotFoundError:
                logger.error(f"❌ 找不到证书文件: {path}")
            except Exception as e:
                logger.error(f"❌ 加载证书失败 {path}: {str(e)}")

        self._root_certificates = certs
        return certs

    def create_api_client(self, environment=None):
        """创建 Apple API 客户端"""
        try:
            with open("static/pay/AuthKey_K2FBY6A2CU.p8", "r") as f:
                private_key = f.read()

            target_environment = environment or (
                Environment.SANDBOX if settings.APP_DEBUG else Environment.PRODUCTION
            )

            client = AppStoreServerAPIClient(
                signing_key=private_key.encode('utf-8'),
                key_id=settings.APPLE_SECRET_KEY_ID,
                issuer_id=settings.APPLE_ISSUER_ID,
                bundle_id=settings.APPLE_BUNDLE_ID,
                environment=target_environment
            )

            logger.info(f"✅ Apple API 客户端创建成功 (环境: {target_environment})")
            return client

        except Exception as e:
            logger.error(f"❌ Apple API 客户端创建失败: {str(e)}")
            return None

    def create_data_verifier(self, environment=None):
        """创建数据验证器"""
        try:
            root_certs = self.load_root_certificates()

            target_environment = environment or (
                Environment.SANDBOX if settings.APP_DEBUG else Environment.PRODUCTION
            )

            verifier = SignedDataVerifier(
                root_certificates=root_certs,
                enable_online_checks=True,
                environment=target_environment,
                bundle_id=settings.APPLE_BUNDLE_ID,
                app_apple_id=settings.APPLE_APP_ID
            )

            logger.info(f"✅ SignedDataVerifier 创建成功 (环境: {target_environment})")
            return verifier

        except Exception as e:
            logger.error(f"❌ SignedDataVerifier 创建失败: {str(e)}")
            return None

    def get_api_client(self, environment=None):
        """获取 API 客户端（单例模式）"""
        if self._api_client is None:
            self._api_client = self.create_api_client(environment)
        return self._api_client

    def get_data_verifier(self, environment=None):
        """获取数据验证器（单例模式）"""
        if self._data_verifier is None:
            self._data_verifier = self.create_data_verifier(environment)
        return self._data_verifier

# 全局实例
apple_client_manager = AppleClientManager()