"""
Apple Store Server API 客户端基础设施（包含配置管理）
"""
from enum import Enum
from dataclasses import dataclass
from typing import Dict, Optional, List
from appstoreserverlibrary.api_client import AppStoreServerAPIClient
from appstoreserverlibrary.models.Environment import Environment
from appstoreserverlibrary.signed_data_verifier import SignedDataVerifier
from config import settings
from src.logger.logUtil import get_logger

logger = get_logger(__name__)

class AppleEnvironment(Enum):
    """Apple 环境枚举"""
    SANDBOX = "sandbox"
    PRODUCTION = "production"

    def to_apple_env(self) -> Environment:
        """转换为苹果官方环境枚举"""
        return Environment.SANDBOX if self == AppleEnvironment.SANDBOX else Environment.PRODUCTION

@dataclass
class AppleConfig:
    """Apple 配置类"""
    bundle_id: str
    app_apple_id: int
    key_id: str
    issuer_id: str
    private_key_path: str
    cert_paths: List[str]
    default_environment: AppleEnvironment

    @classmethod
    def from_settings(cls) -> 'AppleConfig':
        """从配置文件创建"""
        return cls(
            bundle_id=settings.APPLE_BUNDLE_ID,
            app_apple_id=settings.APPLE_APP_ID,
            key_id=settings.APPLE_SECRET_KEY_ID,
            issuer_id=settings.APPLE_ISSUER_ID,
            private_key_path="static/pay/AuthKey_K2FBY6A2CU.p8",
            cert_paths=[
                'static/pay/AppleRootCA-G3.cer',
                'static/pay/AppleRootCA-G2.cer',
                'static/pay/AppleIncRootCertificate.cer',
            ],
            default_environment=AppleEnvironment.SANDBOX if settings.APP_DEBUG else AppleEnvironment.PRODUCTION
        )

class AppleClientFactory:
    """Apple 客户端工厂"""

    def __init__(self, config: AppleConfig):
        self.config = config
        self._root_certificates: Optional[List[bytes]] = None

    def _load_certificates(self) -> List[bytes]:
        """加载根证书"""
        if self._root_certificates is not None:
            return self._root_certificates

        certificates = []
        for cert_path in self.config.cert_paths:
            try:
                with open(cert_path, 'rb') as f:
                    certificates.append(f.read())
                logger.debug(f"✅ 证书加载成功: {cert_path}")
            except FileNotFoundError:
                logger.error(f"❌ 证书文件不存在: {cert_path}")
            except Exception as e:
                logger.error(f"❌ 证书加载失败 {cert_path}: {e}")

        self._root_certificates = certificates
        return certificates

    def _load_private_key(self) -> str:
        """加载私钥"""
        try:
            with open(self.config.private_key_path, 'r') as f:
                return f.read()
        except Exception as e:
            logger.error(f"❌ 私钥加载失败: {e}")
            raise

    def create_api_client(self, environment: AppleEnvironment) -> AppStoreServerAPIClient:
        """创建 API 客户端"""
        try:
            private_key = self._load_private_key()

            client = AppStoreServerAPIClient(
                signing_key=private_key.encode('utf-8'),
                key_id=self.config.key_id,
                issuer_id=self.config.issuer_id,
                bundle_id=self.config.bundle_id,
                environment=environment.to_apple_env()
            )

            logger.info(f"✅ API客户端创建成功: {environment.value}")
            return client

        except Exception as e:
            logger.error(f"❌ API客户端创建失败: {e}")
            raise

    def create_data_verifier(self, environment: AppleEnvironment) -> SignedDataVerifier:
        """创建数据验证器"""
        try:
            certificates = self._load_certificates()
            if not certificates:
                raise ValueError("无法加载根证书")

            verifier = SignedDataVerifier(
                root_certificates=certificates,
                enable_online_checks=True,
                environment=environment.to_apple_env(),
                bundle_id=self.config.bundle_id,
                app_apple_id=self.config.app_apple_id
            )

            logger.info(f"✅ 数据验证器创建成功: {environment.value}")
            return verifier

        except Exception as e:
            logger.error(f"❌ 数据验证器创建失败: {e}")
            raise

class AppleClientManager:
    """Apple 客户端管理器 - 支持多环境缓存"""

    def __init__(self, config: Optional[AppleConfig] = None):
        self.config = config or AppleConfig.from_settings()
        self.factory = AppleClientFactory(self.config)
        self._api_clients: Dict[AppleEnvironment, AppStoreServerAPIClient] = {}
        self._data_verifiers: Dict[AppleEnvironment, SignedDataVerifier] = {}

    def get_api_client(self, environment: Optional[AppleEnvironment] = None) -> AppStoreServerAPIClient:
        """获取 API 客户端"""
        env = environment or self.config.default_environment

        if env not in self._api_clients:
            self._api_clients[env] = self.factory.create_api_client(env)

        return self._api_clients[env]

    def get_data_verifier(self, environment: Optional[AppleEnvironment] = None) -> SignedDataVerifier:
        """获取数据验证器"""
        env = environment or self.config.default_environment

        if env not in self._data_verifiers:
            self._data_verifiers[env] = self.factory.create_data_verifier(env)

        return self._data_verifiers[env]

    def get_sandbox_client(self) -> AppStoreServerAPIClient:
        """获取沙盒环境客户端"""
        return self.get_api_client(AppleEnvironment.SANDBOX)

    def get_production_client(self) -> AppStoreServerAPIClient:
        """获取生产环境客户端"""
        return self.get_api_client(AppleEnvironment.PRODUCTION)

    def get_sandbox_verifier(self) -> SignedDataVerifier:
        """获取沙盒环境验证器"""
        return self.get_data_verifier(AppleEnvironment.SANDBOX)

    def get_production_verifier(self) -> SignedDataVerifier:
        """获取生产环境验证器"""
        return self.get_data_verifier(AppleEnvironment.PRODUCTION)

    def clear_cache(self):
        """清除缓存"""
        self._api_clients.clear()
        self._data_verifiers.clear()
        logger.info("🧹 客户端缓存已清除")

    def reload_config(self):
        """重新加载配置"""
        self.config = AppleConfig.from_settings()
        self.factory = AppleClientFactory(self.config)
        self.clear_cache()
        logger.info("🔄 配置已重新加载")

# 全局实例
apple_client_manager = AppleClientManager()

# 便捷函数（保持向后兼容）
def get_apple_api_client(environment: Optional[AppleEnvironment] = None) -> AppStoreServerAPIClient:
    """获取 Apple API 客户端"""
    return apple_client_manager.get_api_client(environment)

def get_apple_data_verifier(environment: Optional[AppleEnvironment] = None) -> SignedDataVerifier:
    """获取 Apple 数据验证器"""
    return apple_client_manager.get_data_verifier(environment)

def create_apple_api_client(environment: Optional[AppleEnvironment] = None) -> AppStoreServerAPIClient:
    """创建新的 Apple API 客户端（不使用缓存）"""
    config = AppleConfig.from_settings()
    factory = AppleClientFactory(config)
    env = environment or config.default_environment
    return factory.create_api_client(env)

def create_signed_data_verifier(environment: Optional[AppleEnvironment] = None) -> SignedDataVerifier:
    """创建新的数据验证器（不使用缓存）"""
    config = AppleConfig.from_settings()
    factory = AppleClientFactory(config)
    env = environment or config.default_environment
    return factory.create_data_verifier(env)

# 向后兼容函数（支持原有的 Environment 枚举）
def create_apple_api_client_legacy(environment: Optional[Environment] = None) -> AppStoreServerAPIClient:
    """创建 Apple API 客户端（兼容原有接口）"""
    if environment is None:
        apple_env = None
    elif environment == Environment.SANDBOX:
        apple_env = AppleEnvironment.SANDBOX
    elif environment == Environment.PRODUCTION:
        apple_env = AppleEnvironment.PRODUCTION
    else:
        apple_env = None

    return create_apple_api_client(apple_env)

def create_signed_data_verifier_legacy(environment: Optional[Environment] = None) -> SignedDataVerifier:
    """创建数据验证器（兼容原有接口）"""
    if environment is None:
        apple_env = None
    elif environment == Environment.SANDBOX:
        apple_env = AppleEnvironment.SANDBOX
    elif environment == Environment.PRODUCTION:
        apple_env = AppleEnvironment.PRODUCTION
    else:
        apple_env = None

    return create_signed_data_verifier(apple_env)