import hashlib
import json
import urllib
from urllib.parse import parse_qs, urlparse

import isodate
import requests
from bs4 import BeautifulSoup

from config import settings
from src.logger.logUtil import get_logger

logger = get_logger(__name__)


class UrlResult:
    def __init__(self, url, flag=False, source="", duration=0, title="", author=""):
        self.flag = flag
        self.source = source
        self.duration = duration
        self.url = url
        self.author = author
        self.title = title

    # to json
    def to_json(self):
        return {
            "source": self.source,
            "duration_minutes": self.duration,
            "url": self.url,
            "title": self.title,
            "author": self.author,
        }


def handle_url(url, handler, source) -> UrlResult:
    result = urlparse(url)
    ret = UrlResult(url, source=source)
    if not handler.isHttp:
        ret.flag = True
        return ret
    if handler.valid(result):
        ret.flag = True
        query_params = parse_qs(result.query)
        query_params, ret.duration, ret.title, ret.author = handler.process(result, query_params)
        path = result.path.rstrip("/")
        # 如果value 是数组，则取第一个，如果value 是空，则不加参数，如果value是字符串，则加参数
        ret.url = result._replace(
            path=path,
            query="&".join([f"{k}={v[0] if isinstance(v, list) and v else v}" for k, v in query_params.items()]),
        ).geturl()
    return ret


class YoutubeHandler:
    isHttp = True

    @staticmethod
    def valid(result):
        return result.netloc == "www.youtube.com" or result.netloc == "m.youtube.com"

    @staticmethod
    def process(result, query_params):
        duration = 0
        title = ""
        author = ""
        if "v" in query_params:
            # 阿里云服务调不通youtube接口，直接返回0
            duration, title, author = get_youtube_details(query_params["v"][0])
        if "t" in query_params:
            query_params.pop("t")
        return query_params, duration, title, author


class BilibiliHandler:
    isHttp = True

    @staticmethod
    def valid(result):
        return result.hostname == "www.bilibili.com"

    @staticmethod
    def process(result, query_params):
        if "p" in query_params:
            query_params = {"p": query_params["p"]}
        else:
            query_params = {}
        bv_id = result.path.split("/")[2]
        p_value = int(query_params.get("p", [1])[0])
        duration, title, author = get_bilibili_details(bv_id, p_value)
        query_params["p"] = str(p_value)
        return query_params, duration, title, author


class UploadHandler:
    isHttp = False

    @staticmethod
    def valid(result):
        return True

    @staticmethod
    def process(result, query_params):
        return query_params, 0, "", ""


class XyzHandler:
    isHttp = True

    @staticmethod
    def valid(result):
        return result.hostname == "www.xiaoyuzhoufm.com"

    @staticmethod
    def process(result, query_params):
        duration, title, author = get_xiaoyuzhou_info(result.geturl())
        return query_params, duration, title, author


def get_xiaoyuzhou_info(url: str):
    response = requests.get(
        url,
        headers={
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36"
        },
    )
    soup = BeautifulSoup(response.content, "html.parser")
    # 从<script name="schema:podcast-show" type="application/ld+json">  和 </script>中获取json数据
    script = soup.find("script", attrs={"name": "schema:podcast-show"})
    if not script:
        return {}
    # json
    datas = json.loads(script.string)
    duration = datas.get("timeRequired")
    if duration:
        duration = int(duration.split("T")[1].split("M")[0])
    return duration, datas.get("name"), datas.get("partOfSeries")["name"]


class SourceHandler:
    handlers = {
        "youtube": YoutubeHandler,
        "bilibili": BilibiliHandler,
        "upload": UploadHandler,
        "xiaoyuzhou": XyzHandler,
    }

    @classmethod
    def handle_source(cls, url, source) -> UrlResult:
        # Simplify the check for 'Short' URLs and handle them accordingly
        if "Short" in source:
            source = source.replace("Short", "")
            # Remove 'Short' from the source to get the original source name
            try:
                # Attempt to get the full link
                url = get_full_link(url)
            except Exception as e:
                # Log the exception with the URL for better debugging
                logger.exception(f"handle full link error, url={url}, error={e}")
                return UrlResult(url, source=source)

        # Attempt to find and use the appropriate handler based on the source
        handler = cls.handlers.get(source)
        if handler:
            return handle_url(url, handler, source)
        else:
            # Return a default UrlResult if no handler is found
            return UrlResult(url, source=source)


def appsign(params, appkey, appsec):
    "为请求参数进行 APP 签名"
    params.update({"appkey": appkey})
    params = dict(sorted(params.items()))  # 按照 key 重排参数
    query = urllib.parse.urlencode(params)  # 序列化参数
    sign = hashlib.md5((query + appsec).encode()).hexdigest()  # 计算 api 签名
    params.update({"sign": sign})
    return params


def get_proxy(url):
    if "youtu.be" in url:
        return "https://youtube.zimuzu.org"
    return None


def get_full_link(url: str):
    """
    设置最多跳转次数，超过则抛出异常
    :param url:
    :param max_redirects:
    :return:
    """
    result = urlparse(url)
    if result.netloc == "youtu.be":
        return local_get_youtube_full_link(result)
    response = requests.head(url, allow_redirects=True, timeout=3)
    return response.url


def local_get_youtube_full_link(result):
    """
    本地获取youtube跳转链接
    :param result
    :return:
    """
    # 如果url有带？，则去掉后面的参数
    url = result.path
    if "?" in url:
        url = url.split("?")[0]
    return f"https://www.youtube.com/watch?v={url[1:]}"


def get_bilibili_details(bv_id: str, p):
    """
    获取bilibili视频时长,单位分钟
    :param p: 分p
    :param bv_id: 视频id
    :return:
    """
    try:
        # b战视频api访问会多次会被ban Ip，需要登录?
        # 获取视频信息
        # '获取最新的 img_key 和 sub_key'
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3",
            "Referer": "https://www.bilibili.com/",
        }
        params = appsign({"bvid": bv_id}, "1d8b6e7d45233436", "560c52ccd288fed045859ed18bffd973")
        query = urllib.parse.urlencode(params)
        # 控制3s超时时间
        resp = requests.get("https://api.bilibili.com/x/web-interface/view?" + query, headers=headers, timeout=3)
        data = resp.json()
        if data["code"] == 0:
            # 如果视频数量大于1
            duration = int(data["data"]["duration"] / 60)
            title = data["data"]["title"]
            author = data["data"]["owner"]["name"]
            if data["data"]["videos"] > 1:
                # 获取视频信息
                for item in data["data"]["pages"]:
                    if p and item["page"] == int(p):
                        duration = int(item["duration"] / 60)
                        title = item["part"]
                        break
            # 获取分钟，向上取整数
            if duration == 0:
                duration = 1
            return duration, title, author
        else:
            # 如果获取失败
            return 0, "", ""
    except Exception:
        logger.exception(f"get bilibili duration error ,bv_id={bv_id}")
    return 0, "", ""


def get_youtube_details(video_id):
    """
    Fetches YouTube video details including duration and title.

    :param video_id: YouTube video ID
    :return: Tuple containing duration in minutes and video title
    """
    try:
        api_url = "https://googleapis.zimuzu.org/youtube/v3/videos"
        params = {
            "id": video_id,
            "key": settings.GOOGLE_YOUTUBE_API,
            "part": "contentDetails,snippet",
        }
        response = requests.get(api_url, params=params, timeout=3)

        if response.status_code != 200:
            logger.error(f"Failed to fetch YouTube details, status code: {response.status_code}")
            return 0, ""

        data = response.json()
        items = data.get("items", [])

        if not items:
            logger.warning(f"No details found for video ID: {video_id}")
            return 0, ""

        content_details = items[0]["contentDetails"]
        snippet = items[0]["snippet"]

        duration = isodate.parse_duration(content_details["duration"])
        minutes = int(duration.total_seconds() / 60)
        title = snippet["title"]
        author = snippet["channelTitle"]
        if minutes == 0:
            minutes = 1
        return minutes, title, author
    except requests.exceptions.RequestException as e:
        logger.exception(f"Request exception for video ID: {video_id} - {e}")
    except Exception as e:
        logger.exception(f"Unexpected error for video ID: {video_id} - {e}")
    return 0, None, None


if __name__ == "__main__":
    print(
        SourceHandler.handle_source(
            "https://www.xiaoyuzhoufm.com/episode/67166835db2cf82757e38127", "xiaoyuzhou"
        ).to_json()
    )
    # print(get_youtube_details("1BRebNUbqaI"))
