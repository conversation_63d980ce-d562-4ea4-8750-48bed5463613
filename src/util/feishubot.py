import json

import requests


class FeishuBot:
    def __init__(self, access_token):
        self.access_token = access_token
        self.api_url = "https://open.feishu.cn/open-apis/bot/v2/hook/{}".format(access_token)

    def send_message(self, content):
        headers = {"Content-Type": "application/json"}
        data = {"msg_type": "text", "content": {"text": content}}
        response = requests.post(self.api_url, headers=headers, data=json.dumps(data))
        if response.status_code == 200:
            print("Message sent successfully")
        else:
            print("Failed to send message: ", response.text)


# Example usage:
# bot = FeishuBot("your_access_token_here")
# bot.send_message("Hello, Feishu!")
