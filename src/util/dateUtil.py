import time
from datetime import datetime, timedelta


def timezone(zone):
    """Try to get timezone using pytz or python-dateutil

    :param zone: timezone str
    :return: timezone tzinfo or None
    """
    try:
        import pytz

        return pytz.timezone(zone)
    except ImportError:
        pass
    try:
        from dateutil.tz import gettz

        return gettz(zone)
    except ImportError:
        return None


def get_current_time_in_beijing() -> datetime:
    """
    获取当前时间： 北京时间
    :return:
    """
    now_utc = datetime.utcnow()
    now_beijing = now_utc + timedelta(hours=8)
    return now_beijing


# 获取当前北京时间
def get_beijing_time():
    now = datetime.fromtimestamp(time.time(), tz=timezone("Asia/Shanghai"))
    return now


def getTimeBySeconds(param):
    # 将秒数转换为 时分秒格式的time
    param = int(param)
    try:
        m, s = divmod(param, 60)
        h, m = divmod(m, 60)
        return f"{h:02d}:{m:02d}:{s:02d}"
    except Exception:
        return param


def pretty_timedelta(time_delta: float) -> str:
    seconds_in_a_day = 60 * 60 * 24
    seconds_in_an_hour = 60 * 60
    seconds_in_a_minute = 60

    if time_delta >= seconds_in_a_day:
        days = time_delta / seconds_in_a_day
        spent_time = f"{days:.2f}d"
    elif time_delta >= seconds_in_an_hour:
        hours = time_delta / seconds_in_an_hour
        spent_time = f"{hours:.2f}h"
    elif time_delta >= seconds_in_a_minute:
        minutes = time_delta / seconds_in_a_minute
        spent_time = f"{minutes:.2f}min"
    elif time_delta >= 1:
        seconds = time_delta
        spent_time = f"{seconds:.2f}s"
    else:
        milliseconds = time_delta * 1000
        spent_time = f"{milliseconds:.3f}ms"

    return spent_time
