import ast
import asyncio
import json
from typing import Dict

import json_repair

from src.constant.prompt import outline_prompt_without_timestamp, total_summary_prompt_json
from src.logger.logUtil import get_logger
from src.model.article_detail_manager import LectureArticleDetail
from src.model.article_manager import LectureArticle

from .llm_interface import async_llm_chat
from .translate_util import lang_code_to_en_name

logger = get_logger(__name__)


async def get_article_outline(article_text, language):
    if language:
        language = lang_code_to_en_name(language)
    prompt = {
        "system": outline_prompt_without_timestamp["system"].format(language=language),
        "user": outline_prompt_without_timestamp["user"].format(text=article_text),
    }
    outline, _, _ = await async_llm_chat(prompt=prompt, json=False)
    return outline


async def get_article_summary_json(article_text, language):
    if language:
        language = lang_code_to_en_name(language)
    prompt = {
        "system": total_summary_prompt_json["system"].format(language=language),
        "user": total_summary_prompt_json["user"].format(text=article_text),
    }
    # 这里失败进行重试 3 次
    count = 1
    while count < 3:
        total_summary_json, _, _ = await async_llm_chat(prompt=prompt, json=True)
        total_summary_json = json_repair.repair_json(total_summary_json)
        try:
            result = json.loads(total_summary_json)
            if result:
                return result
        except Exception:
            count += 1
        else:
            break
    raise ValueError(f"重试 {count} 次后，返回的 total_summary_json 不是 JSON 数据")


def get_article_for_quick_read(detail_arr: list[LectureArticleDetail]) -> str:
    text = ""
    # for section in detail_arr:
    #     time_range = "*" + str(section.start_time) + " - " + str(section.end_time) + "*\n\n"
    #     has_img = bool(ast.literal_eval(section.oss_pic_path))

    #     if has_img:
    #         oss_pic_path = insert_img(section.oss_pic_path) + "\n\n"
    #         text += oss_pic_path + time_range
    #     else:
    #         text += time_range

    #     if section.out_language_summary_note:
    #         text += section.out_language_summary_note + "\n\n"
    #     else:
    #         text += section.out_language_speaker_text + "\n\n"
    return text


def get_article_for_deep_read(detail_arr: list[LectureArticleDetail], out_language) -> str:
    text = ""
    for section in detail_arr:
        time_range = "*" + str(section.start_time) + " - " + str(section.end_time) + "*\n\n"
        has_img = bool(ast.literal_eval(section.oss_pic_path))

        if has_img:
            oss_pic_path = insert_img(section.oss_pic_path) + "\n\n"
            text += oss_pic_path + time_range
        else:
            text += time_range
        text += (section.translated_modified_text_json or {}).get(out_language) or "" + "\n\n"
        "---\n\n"
    return text


def get_article_head(article: LectureArticle, summary=True) -> str:
    video_name = article.name
    url = article.video_url
    video_source_text = f"## 视频来源\n\n{url}\n\n"
    outline = (article.translated_outline_json or {}).get(article.out_language or "zh")
    total_summary = parse_total_summary(
        (article.translated_total_summary_json or {}).get(article.out_language or "zh") or {},
        article.out_language or "zh",
    )
    text = f"# {video_name}\n" + video_source_text + f"## 大纲\n\n{outline}\n\n"
    if total_summary:
        text += f"## 总结\n\n{total_summary}\n\n"
    return text


def parse_total_summary(data: Dict, in_language: str) -> str:
    """将总结文本转换为markdown格式。"""
    markdown = ""
    lang_dict = {
        "one_sentence_summary": {"en": "One-sentence Summary", "zh": "一句话总结"},
        "takeaways": {"en": "Takeaways", "zh": "要点"},
        "in_depth_qa": {"en": "In-depth Q&A", "zh": "深度问答"},
        "key_words_tags": {"en": "Key Words and Tags", "zh": "关键词标签"},
        "target_audience": {"en": "Target Audience", "zh": "目标受众"},
        "terminology_explanation": {"en": "Terminology Explanation", "zh": "术语解释"},
    }

    # 处理 one_sentence_summary
    if "one_sentence_summary" in data:
        markdown += f"### {lang_dict['one_sentence_summary'].get(in_language, 'en')}\n"
        markdown += f"- {data['one_sentence_summary']}\n\n"

    # 处理 takeaways
    if "takeaways" in data:
        markdown += f"### {lang_dict['takeaways'].get(in_language, 'en')}\n"
        for item in data["takeaways"]:
            markdown += f"- {item}\n"
        markdown += "\n"

    # 处理 in_depth_qa
    if "in_depth_qa" in data:
        markdown += f"### {lang_dict['in_depth_qa'].get(in_language, 'en')}\n"
        for qa in data["in_depth_qa"]:
            if isinstance(qa, list) and len(qa) >= 2:
                markdown += f"- {qa[0]}\n"
                markdown += f"    - {qa[1]}\n\n"

    # 处理 key_words_tags
    if "key_words_tags" in data:
        markdown += f"### {lang_dict['key_words_tags'].get(in_language, 'en')}\n"
        for tag in data["key_words_tags"]:
            markdown += f"- {tag}\n"
        markdown += "\n"

    # 处理 target_audience
    if "target_audience" in data:
        markdown += f"### {lang_dict['target_audience'].get(in_language, 'en')}\n"
        for audience in data["target_audience"]:
            markdown += f"- {audience}\n"
        markdown += "\n"

    # 处理 terminology_explanation
    if "terminology_explanation" in data:
        markdown += f"### {lang_dict['terminology_explanation'].get(in_language, 'en')}\n"
        for term in data["terminology_explanation"]:
            markdown += f"- {term}\n"

    return markdown


def insert_img(img_path):
    # Convert the string representation of the list to an actual list
    try:
        img_path = ast.literal_eval(img_path)
    except (ValueError, SyntaxError):
        pass

    if isinstance(img_path, list):
        string = ""
        for path in img_path:
            string += f"![]({path})\n\n"
    else:
        string = "![]({})".format(img_path)
    return string


def only_return_body(html):
    return html.split("<body>")[1].split("</body>")[0]


async def do_outline_summary_tasks(article, out_language="zh"):
    try:
        # 调用 open llm 得到 origin: outline, total_summary, out_langauge_outline, out_language_total_summary
        article_text = get_article_origin_text(article.biz_id)
        do_tasks = [
            get_article_outline(article_text, out_language),
            get_article_summary_json(article_text, out_language),
        ]
        outline, total_summary_json = await asyncio.gather(*do_tasks, return_exceptions=True)
        if isinstance(outline, Exception):
            logger.error(f"article_biz_id: {article.biz_id} outline 失败")
        else:
            translated_outline_json = article.translated_outline_json or {}
            translated_outline_json[out_language] = outline
            article.translated_outline_json = translated_outline_json
        if isinstance(total_summary_json, Exception):
            logger.error(f"article_biz_id: {article.biz_id} total_summary_json 失败")
        else:
            translated_total_summary_json = article.translated_total_summary_json or {}
            translated_total_summary_json[out_language] = total_summary_json
            article.translated_total_summary_json = translated_total_summary_json
        if article.translated_outline_json and article.translated_total_summary_json:
            LectureArticle.update(
                translated_outline_json=article.translated_outline_json,
                translated_total_summary_json=article.translated_total_summary_json,
            ).where(LectureArticle.biz_id == article.biz_id).execute()
        else:
            logger.error(f"article_biz_id: {article.biz_id} outline 或 total_summary_json 为空")
    except Exception:
        logger.exception("do_outline_summary_tasks error, article_biz_id=%s", article.biz_id)


def get_article_origin_text(article_biz_id):
    sections = (
        LectureArticleDetail.select(LectureArticleDetail.origin_text)
        .where((LectureArticleDetail.delete_flag == 0) & (LectureArticleDetail.article_id == article_biz_id))
        .order_by(LectureArticleDetail.start_time)
    )
    if not sections:
        raise ValueError("当前 article biz id: {article_biz_id} 没有内容")
    return ", ".join(section.origin_text for section in sections)


def get_article_lan_modified_text(out_language, article_biz_id, with_speaker=False):
    sections = (
        LectureArticleDetail.select(
            LectureArticleDetail.translated_modified_text_json,
            LectureArticleDetail.origin_text,
            LectureArticleDetail.speaker,
            LectureArticleDetail.start_time,
        )
        .where((LectureArticleDetail.delete_flag == 0) & (LectureArticleDetail.article_id == article_biz_id))
        .order_by(LectureArticleDetail.start_time)
    )
    if not sections:
        raise ValueError("当前 article biz id: {article_biz_id} 没有内容")
    text = []
    for section in sections:
        if modified_text := (section.translated_modified_text_json or {}).get(out_language):
            if with_speaker and (section.speaker and section.start_time):
                modified_text = f"{section.start_time} {section.speaker}: {modified_text}"
            text.append(modified_text)
        elif section.origin_text:
            origin_text = section.origin_text
            if with_speaker and (section.speaker and section.start_time):
                origin_text = f"{section.start_time} {section.speaker}: {origin_text}"
            text.append(origin_text)
    return ", ".join(text)
