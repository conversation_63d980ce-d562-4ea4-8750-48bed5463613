import json
from traceback import format_exc

from alibabacloud_captcha20230305 import models as captcha_20230305_models
from alibabacloud_captcha20230305.client import Client as Captcha20230305Client
from alibabacloud_tea_openapi import models as open_api_models
from aliyunsdkcore.client import AcsClient
from aliyunsdkcore.request import CommonRequest

from src.logger.logUtil import get_logger

logger = get_logger(__name__)

# 阿里云短信配置
ALIYUN_ACCESS_KEY_ID = "LTAI5tJ7EvW6LMaRna7NqxzW"
ALIYUN_ACCESS_KEY_SECRET = "******************************"
# ALIYUN_SMS_SIGN_NAME = "叮当好记"
ALIYUN_SMS_SIGN_NAME = "合肥知澜跃境科技"
ALIYUN_SMS_TEMPLATE_CODE = "SMS_483895401"

client = AcsClient(ALIYUN_ACCESS_KEY_ID, ALIYUN_ACCESS_KEY_SECRET, "cn-hangzhou")


# 短信发送逻辑
def send_sms_by_aliyun(phone: str, code: str):
    # 目前还只支持中国区的电话
    request = CommonRequest()
    request.set_accept_format("json")
    request.set_domain("dysmsapi.aliyuncs.com")
    request.set_method("POST")
    request.set_version("2017-05-25")
    request.set_action_name("SendSms")
    request.add_query_param("PhoneNumbers", phone)
    request.add_query_param("SignName", ALIYUN_SMS_SIGN_NAME)
    request.add_query_param("TemplateCode", ALIYUN_SMS_TEMPLATE_CODE)
    request.add_query_param("TemplateParam", f'{{"code":"{code}"}}')

    response = client.do_action_with_exception(request)
    return response


captcha_verifyer_client = Captcha20230305Client(
    open_api_models.Config(
        access_key_id=ALIYUN_ACCESS_KEY_ID,
        access_key_secret=ALIYUN_ACCESS_KEY_SECRET,
        endpoint="captcha.cn-shanghai.aliyuncs.com",
        connect_timeout=5000,
        read_timeout=5000,
    )
)


class CaptchaVerifyer:
    @staticmethod
    async def verify(captcha_verify_param: dict) -> bool:
        # 如果带有这些参数, 先让验证码通过, 会以日志的形式再看具体原因
        if not (
            isinstance(captcha_verify_param, dict)
            and captcha_verify_param.get("sceneId")
            and captcha_verify_param.get("certifyId")
            and captcha_verify_param.get("deviceToken")
            and captcha_verify_param.get("data")
        ):
            return False

        # 前端传来的验证参数 CaptchaVerifyParam, 这里需要成 JSON 字符串
        request = captcha_20230305_models.VerifyCaptchaRequest(captcha_verify_param=json.dumps(captcha_verify_param))
        try:
            # 这里根据官方文档使用异步会报错，先使用同步
            resp = await captcha_verifyer_client.verify_captcha_async(request)
            logger.error(f"captcha_verify_result, {resp.body}")
            captcha_verify_result = resp.body.result.verify_result


            # return False
        except Exception:
            logger.error(format_exc())
            # 先让验证码通过
            captcha_verify_result = True
        return captcha_verify_result


if __name__ == "__main__":
    captcha_verify_result = CaptchaVerifyer.verify(
        {
            "sceneId": "19uolmao",
            "certifyId": "pKVpKOsRHI",
            "deviceToken": "V0VCI2FiMDM0ZWMwNjQzZjkxMzk5ZWIzM2UwNjJkYzdmYWUxLWgtMTc0MjU0NTgxMjE1Ny0wMTljOTU3ZmJkMTI0NzMxODNiNmFjZjM4MjdhMzcxOCNUMFJrdFpKTUd0d2Y0VkZBVEExdDBXczlCbWVlVnZkdlI1L01QY1RwTTVzZ1hvWXBkOURIYjJRVmdVUU93eTBhRHVpV1lGa0w4cFU3UDRxdmIySXFsYzg0MCt1VUNXOG91eUl6QjdQbkMyR0MrOVAxTzYrUGJQM3hnemNpTVZqNEdlTnpnREtLcWdvN1JvSmJDME5MUUFkM3IzVGtNTUdOdjVMMmMyMzFRaUdFTGZCaklSMFJBOUIvbldDTlFYeXFXVGJyb25yem1yTnpKZm9FdW85QkNaZWFkSWE5MC8yZWJ2MUpEVHYvMG5jNnJiUWxXM1hyekF5ZkJQMy9EendQaWErQXIvT1d1eVdFS1dxTEtMaVhFYlBrOFMzSG93dTF2WmttSFVCeGFBeFNGM05WdHlFODY1dnBlMlRiTEVENEp4aVROMEFJUEVzRzQxUmMycGNHUFIrcGhhK2Nyc1grVnpjNmZ4VFFBUUpTV1hQbWNwMHVzQWFlVWkrM3RuZ1FLRXlzRG9adlIzc3dtbkhMbWtqMlFHbXdoYUorc2JVbk1ZUDlVU0xMbjQzR1lGY1U2dXk2OGs2NWpsb3I2QnF1bzhLZGs1NWcrOFBFNFpUa2tpNjZ5ZThubmhxcXN6L0JGYVRaam9NNU5rNlZBUm5hTFZxM0JEeE96UjdCU1Bva1NpQWlQbnU0djkzVzdrTlBrVVpWTWlUUGszaXlXOTdIbWo0cGZPU3JvTmRQUm1zUUl3MXZlZ1E0M29mcEVFbUpaNzBIOFdtZHJNN2JLdVBuZ0d4c0s0M3EzeDlLY2IxdTlPem1pYnJHU01qVm1Ub2RnRlAxZXVVUEMrY2tEVFR6MGExdU0vbVRUV1dlb2RQd2VJenE5UT09Izg1NSM5NTI5ZTU3Y2Y0YjM4ZDRlMzYwNGI3NzYwZjUyYjhiNQ==",
            "data": "JRMlmkseDQU7Xw4Lam9YLxnotkdDMiB7AT0CCnO3L+BbTkKbDk16RigpZ6td2ftY1KgFehqL6m69jeQKVfY4AHU2AmMPPAJvAkkLdx5RFUMiRUcxWQFoVuR4HmRseyfiZtibKccVFukaWmhBd0gZ7CW+mmNaxhIXJUVKT7zQmo90pA9QbAw7RQdzYT9MTlU72Ht1HQNMJVwBC2lAxCNzZHdukTwLgg9vIIMFcAgLdj55bFlLcTGmSP+ZvxYGUmUCQXJ7EkFyIHqVRx8WAlJ+PDE6I+AHeAMvhTUaVjZASi9dskV7kQgfvHRxMQWQbVJZFTEsgHQ/JloOLx5xCVB3dVjHbC22EQc6jxVEfVRfI0YJeFxXT1sIKQQnIw54UTFXEFtEH30sZCEBCN+A6u80bi4m/vEeMJXDlYMsHQ==",
        }
    )
    print("captcha_verify_result: ", captcha_verify_result)
