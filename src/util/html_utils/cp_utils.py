# -*- coding: utf-8 -*-
import os
import shutil
from pathlib import Path


def delete_file(file_path):
    del_list = os.listdir(file_path)
    for f in del_list:
        file_path = os.path.join(file_path, f)
        if os.path.isfile(file_path):
            os.remove(file_path)
        elif os.path.isdir(file_path):
            shutil.rmtree(file_path)


# 判断目录是否存在，不存在则创建
def is_dir_existed(file_path, mkdir=True, is_recreate=False):
    if mkdir:
        if not os.path.exists(file_path):
            os.makedirs(file_path)
        else:
            if is_recreate:
                delete_file(file_path)
                os.makedirs(file_path)
    else:
        return os.path.exists(file_path)


# 获取目录下的所有文件路径
def fetch_all_file(file_dir):
    return list(map(lambda x: os.path.join(file_dir, x), os.listdir(file_dir)))


# 获取目录下特定后缀的文件路径列表
def filter_file_type(file_dir, file_suffix):
    result_list = []
    file_path_list = fetch_all_file(file_dir)
    for file_path in file_path_list:
        if file_path.endswith(file_suffix):
            result_list.append(file_path)
    return result_list


# 读取文件内容
def read_file_content(file_path):
    if not os.path.exists(file_path):
        return "文件不存在"
    else:
        with open(file_path, "r+", encoding="utf-8") as f:
            return f.read()


# 写入文件
def write_file(content, file_path):
    with open(file_path, "w+", encoding="utf-8") as f:
        f.write(content)


# 追加文件
def write_file_append(content, file_path):
    with open(file_path, "a+", encoding="utf-8") as f:
        f.write(content)


def get_static_file_path(file_name):
    """
    Get the absolute path of a file in the static directory.
    :param file_name: Name of the file in the static directory.
    :return: Absolute path of the file.
    """
    # Assuming the script is located in the src directory
    base_dir = Path(__file__).resolve().parent.parent.parent.parent
    static_dir = base_dir / "static"
    return Path(str(static_dir / file_name))


def read_static_file(file_name):
    """
    Read the content of a file in the static directory.
    :param file_name: Name of the file in the static directory.
    :return: Content of the file.
    """
    file_path = get_static_file_path(file_name)
    with open(file_path, "r", encoding="utf-8") as file:
        return file.read()
