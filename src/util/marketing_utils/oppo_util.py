import asyncio
import base64
import hashlib
import json
from datetime import datetime, timezone
from pydantic import BaseModel, Field
import aiohttp
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import padding
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes

from src.logger import logUtil
from src.util import stringify

logger = logUtil.get_logger(logUtil.LoggerName.market)


def aes_ecb_encrypt(plaintext: str, base64_key: str = "XGAXicVG5GMBsx5bueOe4w==") -> str:
    """
    AES-128-ECB 加密
    :param plaintext: 要加密的字符串
    :param base64_key: Base64编码的密钥（解码后必须为16字节）
    :return: Base64编码的加密结果
    """
    # 解码密钥
    key = base64.b64decode(base64_key)
    if len(key) != 16:
        raise ValueError("密钥必须为16字节（AES-128）")

    # 初始化加密器
    backend = default_backend()
    cipher = Cipher(algorithms.AES(key), modes.ECB(), backend=backend)
    encryptor = cipher.encryptor()

    # PKCS7填充
    pad_worker = padding.PKCS7(128).padder()  # 128-bit block size for AES
    padded_data = pad_worker.update(plaintext.encode('utf-8')) + pad_worker.finalize()

    # 加密并Base64编码
    ciphertext = encryptor.update(padded_data) + encryptor.finalize()
    return base64.b64encode(ciphertext).decode('utf-8')


def generate_signature(body: dict, t):
    salt = "e0u6fnlag06lc3pl"
    contents = f"{json.dumps(body).replace(' ', '')}{t}{salt}"
    md5_hash = hashlib.md5(contents.encode('utf-8')).hexdigest()
    return md5_hash


class OppoSubmitSchema(BaseModel):
    # 二选一
    imei: str = Field(None, description="客户端 imei 经过 AES加密后的值")
    ouId: str = Field(None, description="开发者 ID 经过 AES加密后的值")
    requestId: str = Field(None, description="请求 id")
    mac: str = Field(None, description="客户端 mac 经过 AES加密后的值")
    clientIp: str = Field(None, description="事件发生时的客户端ip")
    pkg: str = Field(..., description="包名，如 com.xxx如果是快应用，要填快应用 id，如 100137")
    dataType: int = Field(..., description="转化数据类型：")
    payId: str = Field(None, description="付费交易 Id 否（但dataType=22/25必填）")
    customType: int = Field(None, description="自定义目标类型：dataType填了8之后补充，枚举值与客户沟通后补充")
    channel: int = Field(..., description="渠道：1、OPPO，2、一加，0、其他")
    type: int = Field(..., description="0： 无 md5 加密、原值（默认为 0）1：imei md5 加密 2：oaid md5 加密")

    """ 1、激活，2、注册，3、游戏付费，4、次留，5、应用内授信，6、应用内下单（电商），7、应用付费 8、自定义目标，
        9、第 3 日留存 10、第4日留存， 11、第 5 日留存 12、第 6 日留存，13、第 7 日留存，14、第 8 日留存，15、拉活，
        16、快应用付费，17、次留（DP）18、卸载激活 19、快应用关键行为 20、游戏关键行为 21、长留高质量人群（废弃）
        22、应用付费次数 23、长留自定义 24、快应用 付费次数 25、付费流水 26、质量分 27、完件 28、应用关键行为"""

async def submit(param):
    try:
        # clientIp
        imei = aes_ecb_encrypt("868123039927020")
        mac = aes_ecb_encrypt("d7:1b:3e:00:14:b3")

        body = {
            "payAmount": 100, "adId": 101097648, "appType": 1, "clientIp": "127.0.0.1", "dataType": 1, "ascribeType": 1,
            "channel": 1, "imei": imei, "type": 1, "pkg": "com.oppo.test", "mac": mac}
        t = int(datetime.now(tz=timezone.utc).timestamp() * 1000)
        body.update({"timestamp": t})

        md5 = generate_signature(body, t)
        header = {
            "signature": md5,
            "Content-Type": "application/json",
            "timestamp": t
        }
    except Exception as ex:
        logger.error(f"OPPO 上报参数错误：{stringify.from_exception(ex)}")
        return None

    url = "https://api.ads.heytapmobi.com/api/uploadActiveData"
    timeout = aiohttp.ClientTimeout(total=10)
    async with aiohttp.ClientSession(timeout=timeout) as session:
        last_exception = None
        for _ in range(3):
            try:
                async with session.post(url, header=header, data=json.dumps(body)) as response:
                    if response.status == 403:
                        raise Exception("签名错误")
                    json_data = await response.json()
                    error_code = int(json_data.get('ret', -1))
                    if error_code != 0:
                        raise Exception(f"ret: {error_code} msg: {json_data.get('msg')}")
                    else:
                        return json_data.get("ticket")
            except asyncio.TimeoutError as e:
                last_exception = e
                continue
            except Exception as ex:
                logger.error(f"OPPO 上报请求错误：\n{stringify.from_exception(ex)}")
                return None
        logger.error(f"OPPO 上报超时c：\n{stringify.from_exception(last_exception)}")
        return None


def main():
    imei = aes_ecb_encrypt("868123039927020")
    imei_verify = "XJMyaLt8fDlv4a9b8/0RNQ=="
    print(imei, imei == imei_verify)

    mac = aes_ecb_encrypt("d7:1b:3e:00:14:b3")
    mac_verify = "TEViR6jSgD/lECBl3Ah70eNy2gUQrQlekHkWqEGkZsU="
    print(mac, mac == mac_verify)

    body = {
        "payAmount": 100, "adId": 101097648, "appType": 1, "clientIp": "127.0.0.1", "dataType": 1, "ascribeType": 1,
        "channel": 1, "imei": imei, "type": 1, "pkg": "com.oppo.test", "mac": mac}

    t = 1571995483916
    body.update({"timestamp": t})
    md5 = generate_signature(body, t)
    md5_verify = "ce14fcc22abd7461e860263a8da983eb"
    print(md5, md5 == md5_verify)


if __name__ == '__main__':
    main()
