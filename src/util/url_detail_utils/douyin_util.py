import datetime
import json
import os
import random
import re
from pathlib import Path
from typing import Union, Optional, List, Dict
from urllib.parse import quote, urlencode
from src.logger.logUtil import get_logger
import httpx
import requests
from pydantic import BaseModel
from src.util.url_detail_utils.douyin_helper import ABogus

DOUYIN_DOMAIN = "https://www.douyin.com"
logger = get_logger(__name__)


def get_timestamp(unit: str = "milli"):
    """
    根据给定的单位获取当前时间 (Get the current time based on the given unit)

    Args:
        unit (str): 时间单位，可以是 "milli"、"sec"、"min" 等
            (The time unit, which can be "milli", "sec", "min", etc.)

    Returns:
        int: 根据给定单位的当前时间 (The current time based on the given unit)
    """

    now = datetime.datetime.utcnow() - datetime.datetime(1970, 1, 1)
    if unit == "milli":
        return int(now.total_seconds() * 1000)
    elif unit == "sec":
        return int(now.total_seconds())
    elif unit == "min":
        return int(now.total_seconds() / 60)
    else:
        raise ValueError("Unsupported time unit")


class DouyinConfig:
    PROXIES = {
        "http": None,
        "https": None,
    }

    MS_TOKEN = {
        "url": "https://mssdk.bytedance.com/web/report",
        "magic": 538969122,
        "version": 1,
        "dataType": 8,
        "strData": "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",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36"
    }

    TTWID = {
        "url": "https://ttwid.bytedance.com/ttwid/union/register/",
        "data": '{"region":"cn","aid":1768,"needFid":false,"service":"www.ixigua.com","migrate_info":{"ticket":"","source":"node"},"cbUrlProtocol":"https","union":true}'
    }


class BaseRequestModel(BaseModel):
    device_platform: str = "webapp"
    aid: str = "6383"
    channel: str = "channel_pc_web"
    pc_client_type: int = 1
    version_code: str = "290100"
    version_name: str = "29.1.0"
    cookie_enabled: str = "true"
    screen_width: int = 1920
    screen_height: int = 1080
    browser_language: str = "zh-CN"
    browser_platform: str = "Win32"
    browser_name: str = "Chrome"
    browser_version: str = "130.0.0.0"
    browser_online: str = "true"
    engine_name: str = "Blink"
    engine_version: str = "130.0.0.0"
    os_name: str = "Windows"
    os_version: str = "10"
    cpu_core_num: int = 12
    device_memory: int = 8
    platform: str = "PC"
    downlink: str = "10"
    effective_type: str = "4g"
    from_user_page: str = "1"
    locate_query: str = "false"
    need_time_list: str = "1"
    pc_libra_divert: str = "Windows"
    publish_video_strategy_type: str = "2"
    round_trip_time: str = "0"
    show_live_replay_strategy: str = "1"
    time_list_query: str = "0"
    whale_cut_token: str = ""
    update_version_code: str = "170400"
    msToken: str = None


class DouyinDownloader:
    def __init__(self, cookies_files: Optional[List[str]] = None):
        self.cookies_files = cookies_files or []
        self.headers_config = self._get_headers()

        self.ttwid_config = DouyinConfig.TTWID.copy()
        self.ms_token_config = DouyinConfig.MS_TOKEN.copy()

    def _get_headers(self) -> Dict[str, str]:
        """获取请求头信息。

        Returns:
            Dict[str, str]: 请求头字典
        """
        cookies_str = "SelfTabRedDotControl=%5B%7B%22id%22%3A%226972125113638455333%22%2C%22u%22%3A36%2C%22c%22%3A0%7D%5D; UIFID_TEMP=8f584679f13361d7674396b25a46f1c75dd7736730eef2bca7f1b3cfea8b216dc6eb7c63b07b64dc5b591d94bbd89607b05edb9cc7112ca32a097cd3c6c9eb38f525797a9e6952dbea5c2668a8ce0467; __security_mc_1_s_sdk_cert_key=98d35481-4758-a215; __security_mc_1_s_sdk_crypt_sdk=f0c88cc8-45af-81fb; __security_mc_1_s_sdk_sign_data_key_sso=6c8bc49b-4544-b502; __security_mc_1_s_sdk_sign_data_key_web_protect=12906d4b-475a-9480; __security_server_data_status=1; _bd_ticket_crypt_cookie=1e88584f5c7e618ca25e2be6eaa3aa1f; bd_ticket_guard_client_data=eyJiZC10aWNrZXQtZ3VhcmQtdmVyc2lvbiI6MiwiYmQtdGlja2V0LWd1YXJkLWl0ZXJhdGlvbi12ZXJzaW9uIjoxLCJiZC10aWNrZXQtZ3VhcmQtcmVlLXB1YmxpYy1rZXkiOiJCQ1dmdmZESCtmQnNIRDd5N2JTalVGMUovd0U5NlE1Q09tM3NScUp3MTNUTng2Tk9ySXJiVlc3Q3dsano5cFQrZlJhTG1ndk1iKzkreWRIekREMTFFZlU9IiwiYmQtdGlja2V0LWd1YXJkLXdlYi12ZXJzaW9uIjoyfQ%3D%3D; bd_ticket_guard_client_web_domain=2; biz_trace_id=66c7f5bd; d_ticket=77739aff7f0e75121892c609a5a25800aa731; enter_pc_once=1; is_staff_user=false; login_time=1749571892215; my_rd=2; n_mh=CFlWDhIcK31iF6ojwhIfKUaiuoj77A-_7POR6NvmDzs; odin_tt=10067cfd9e62d016658aee9a029b68ef828679e6fff02adc58a0569a0a64404b33a3f4b0ba063071c6b8b3761d936fe8036647a972d1475c47b0f4320f2359e9dc2c11939035262e820335e66fc7fa22; passport_assist_user=Cjyy6GcFvmg-FI_dTFs5yyMOBtgL54m-bZJ2cyGjl-D7cISIih4-ovjpliN0UfkJKNB8uz7CdeQsvwvQpsMaSgo8AAAAAAAAAAAAAE8Z2pAz1YR9s3WDNC73Xj1aH-YsYQ2PIeucB54Ssg-QcqMe5t_gQmNwDok7rufp7bQqEK7g8w0Yia_WVCABIgEDycfkWg%3D%3D; passport_auth_status=7f4cbf73dd4da07f85203a6a473b7d5c%2C; passport_auth_status_ss=7f4cbf73dd4da07f85203a6a473b7d5c%2C; passport_csrf_token=011a4d93e0d41748d039122f9e07d073; passport_csrf_token_default=011a4d93e0d41748d039122f9e07d073; passport_mfa_token=CjX44qj2E0L671bLKsxHJ5s6PDaDXfouVZbwgcuwBxQ3PjIf7cViAP3JB13SWoGjU81DJ51UmRpKCjwAAAAAAAAAAAAATxlohfzG27Z3vJD7cBJI%2FVQaitVwR7eTCWeLm9itMK%2FHbiviyiGXjyujiy2Ivwo2N7QQquDzDRj2sdFsIAIiAQOlQ%2FWK; sessionid=f9f65e079f080d4cc1cb8e8f0f196835; sessionid_ss=f9f65e079f080d4cc1cb8e8f0f196835; sid_guard=f9f65e079f080d4cc1cb8e8f0f196835%7C1749571892%7C5184000%7CSat%2C+09-Aug-2025+16%3A11%3A32+GMT; sid_tt=f9f65e079f080d4cc1cb8e8f0f196835; sid_ucp_v1=1.0.0-KDNmZTc2NjhhYzYxMzBmNWY4MmU0OGE5MDY1NjgyMDg3YWJmMmI4NGEKHwjAqY_n7AIQtLKhwgYY7zEgDDD-oKHXBTgCQPEHSAQaAmxmIiBmOWY2NWUwNzlmMDgwZDRjYzFjYjhlOGYwZjE5NjgzNQ; ssid_ucp_v1=1.0.0-KDNmZTc2NjhhYzYxMzBmNWY4MmU0OGE5MDY1NjgyMDg3YWJmMmI4NGEKHwjAqY_n7AIQtLKhwgYY7zEgDDD-oKHXBTgCQPEHSAQaAmxmIiBmOWY2NWUwNzlmMDgwZDRjYzFjYjhlOGYwZjE5NjgzNQ; ttwid=1%7CvAccsfcQWvCG4HbZvQ8CHtKfnhx6okzaFETd8WAIoVs%7C1749571728%7C12060446e93d1a98f25f9e798598e41f7e791d33475d8985c44433241692c820; uid_tt=0b2ef8bb6b5a80ec796eec7b657699c2; uid_tt_ss=0b2ef8bb6b5a80ec796eec7b657699c2; volume_info=%7B%22volume%22%3A0.6%2C%22isMute%22%3Afalse%7D"
        try:
            with open("static/cookies/douyin_cookies.json", "r", encoding="utf-8") as f:
                cookie_data = f.read()
            cookie_data = json.loads(cookie_data)
            cookies_str = random.choice(cookie_data.get("cookies"))
        except Exception:
            logger.error("dy随机cookie失败")
            pass
        # cookies_file = random.choice(self.cookies_files)

        # # 解析所有 douyin.com 相关的 cookies
        # cookie_pairs = []
        # for line in cookies_content:
        #     if line.startswith('.douyin.com'):
        #         parts = line.strip().split('\t')
        #         cookie_name = parts[-2]
        #         cookie_value = parts[-1]
        #         cookie_pairs.append(f"{cookie_name}={cookie_value}")
        #
        # cookies_str = '; '.join(cookie_pairs)

        return {
            "Accept-Language": "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36",
            "Referer": "https://www.douyin.com/",
            "Cookie": cookies_str,
        }

    @staticmethod
    def find_url(string: str) -> list:
        url = re.findall('http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\(\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', string)
        return url

    def extract_video_id(self, url: str) -> str:
        video_url = self.find_url(url)

        if len(video_url):
            video_url = video_url[0]
            try:
                response = requests.head(video_url, allow_redirects=True)
                url = response.url
            except Exception as e:
                return ""
        patterns = [
            r'video/(\d+)',
            r'aweme_id=(\d+)',
        ]
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        return ""

    def gen_real_msToken(self) -> str:
        try:
            payload = json.dumps(
                {
                    "magic": self.ms_token_config["magic"],
                    "version": self.ms_token_config["version"],
                    "dataType": self.ms_token_config["dataType"],
                    "strData": self.ms_token_config["strData"],
                    "tspFromClient": get_timestamp(),
                }
            )
            headers = {
                "User-Agent": self.headers_config["User-Agent"],
                "Content-Type": "application/json",
            }
            transport = httpx.HTTPTransport(retries=5)
            with httpx.Client(transport=transport) as client:
                try:
                    response = client.post(
                        self.ms_token_config["url"], content=payload, headers=headers
                    )
                    response.raise_for_status()

                    msToken = str(httpx.Cookies(response.cookies).get("msToken"))
                    if len(msToken) not in [120, 128]:
                        raise ValueError("响应内容：{0}， Douyin msToken API 的响应内容不符合要求。".format(msToken))

                    return msToken
                except Exception as e:
                    raise ValueError("Douyin msToken API 请求失败：{0}".format(e))
        except Exception as e:
            raise ValueError("Douyin msToken API{0}".format(e))

    def download_json_info(self, url: str, save_dir: str = "", return_json=True, save_json=True):
        try:
            aweme_id = self.extract_video_id(url)
            kwargs = self.headers_config
            base_params = BaseRequestModel().model_dump()
            base_params["msToken"] = self.gen_real_msToken()

            base_params["aweme_id"] = aweme_id
            bogus = ABogus()
            ab_value = bogus.get_value(base_params)
            a_bogus = quote(ab_value, safe='')
            query_str = urlencode(base_params)
            full_url = f"{DOUYIN_DOMAIN}/aweme/v1/web/aweme/detail/?{query_str}&a_bogus={a_bogus}"

            response = requests.get(full_url, headers=kwargs)

            json_data = response.json()

            if save_dir and save_json:
                save_dir = Path(save_dir) / "info"
                save_dir.mkdir(parents=True, exist_ok=True)
                with open(os.path.join(save_dir, f"{aweme_id}_info.json"), "w", encoding="utf-8") as f:
                    json.dump(json_data, f, ensure_ascii=False, indent=4)
            if return_json:
                return json_data
            return None
        except Exception as e:
            logger.error("请求失败:", e)

    @staticmethod
    def extract_info(input_dir: str):
        """从json文件中提取视频信息。

        Args:
            input_dir: 包含json文件的目录

        Returns:
            Dict: 视频信息字典
            """
        info_dir = os.path.join(input_dir, "info")
        json_files = [f for f in os.listdir(info_dir) if f.endswith('info.json')]
        if not json_files:
            return {}
        if len(json_files) > 1:
            raise ValueError("存在多个json视频信息")
        json_path = os.path.join(info_dir, json_files[0])
        with open(json_path, "r", encoding="utf-8") as f:
            info = json.load(f)
        return info

    def get_info(self, input_dir: str):
        """从json文件中提取视频信息。
        """
        info = self.extract_info(input_dir)

        tags = []
        for tag in info["aweme_detail"]["video_tag"]:
            if tag.get('tag_name'):
                tags.append(tag['tag_name'])
        for tag in info["aweme_detail"]["text_extra"]:
            if tag.get("hashtag_name"):
                tags.append(tag["hashtag_name"])
        caption = info["aweme_detail"].get("caption", "")
        if caption:
            tags.extend([item.strip() for item in caption.split("#") if item.strip()])

        # return ResourceInfo(
        #     title=info["aweme_detail"].get("item_title", ""),
        #     description=info["aweme_detail"].get("desc", ""),
        #     uploader=info["aweme_detail"].get("author", {}).get("nickname", ""),
        #     duration=int(info["aweme_detail"].get("video", {}).get("duration", 0) / 1000),
        #     tags=tags
        # )

    def download_audio(
            self,
            video_url: str, output_dir: Union[str, None] = None,
    ):
        try:
            output_path = Path(output_dir) / "audio"
            output_path.mkdir(parents=True, exist_ok=True)
            output_path = os.path.join(output_dir, "%(id)s.%(ext)s")

            video_data = self.download_json_info(video_url, output_dir, save_json=False)
            output_path = output_path % {
                "id": video_data['aweme_detail']['aweme_id'],
                "ext": "mp3",
            }
            url = video_data['aweme_detail']['music']['play_url']['uri']
            # 下载音频
            audio_data = requests.get(url)
            with open(output_path, 'wb') as f:
                f.write(audio_data.content)
            return output_path
        except Exception as e:
            logger.exception(e)

    def download_video(self, video_url: str, output_dir: Union[str, None] = None) -> str:

        try:
            output_path = Path(output_dir) / "video"
            output_path.mkdir(parents=True, exist_ok=True)
            output_path = os.path.join(output_dir, "%(id)s.%(ext)s")

            video_data = self.download_json_info(video_url, output_dir, save_json=False)
            output_path = output_path % {
                "id": video_data['aweme_detail']['aweme_id'],
                "ext": "mp4",
            }

            url = video_data['aweme_detail']['video']['download_addr']['url_list'][-1]
            _data = requests.get(url, allow_redirects=True, headers=self.headers_config)

            with open(output_path, 'wb') as f:
                f.write(_data.content)

            return output_path
        except Exception as e:
            logger.exception(e)


if __name__ == '__main__':
    cookies_files = [r"E:\Download\www.douyin.com_cookies.txt"]

    # duration = get_douyin_video_duration("https://v.douyin.com/0pcFVdG_lx4/")
    # print(duration)
    dy = DouyinDownloader(cookies_files=[])
    #
    dy.download_json_info(
        '3.58 <EMAIL> pDH:/ 03/15 以“马成钢”的视角打开《抓娃娃》笼中鸟，何时飞 # 独白 # 人物故事  https://v.douyin.com/VRVKnI5qYo8/ 复制此链接，打开Dou音搜索，直接观看视频！'
    )
