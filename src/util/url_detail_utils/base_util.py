from urllib.parse import urlparse

import requests


def remove_url_params(url: str) -> str:
    parsed_url = urlparse(url)
    clean_url = f"{parsed_url.scheme}://{parsed_url.netloc}{parsed_url.path}"
    return clean_url



def get_full_link(url: str):
    """
    设置最多跳转次数，超过则抛出异常
    :param url:
    # :param max_redirects:
    :return:
    """
    result = urlparse(url)
    if result.netloc == "youtu.be":
        url = result.path
        if "?" in url:
            url = url.split("?")[0]
        return f"https://www.youtube.com/watch?v={url[1:]}"
    elif result.netloc == "xhslink.com":
        return url
    else:
        response = requests.head(url, allow_redirects=True, timeout=3)
        return response.url


def string_mark_content(text, start_mark, end_mark, split_key=None, start_contain=False, end_contain=False):
    start_location = text.find(start_mark)
    if start_location == -1:
        raise Exception(f"not fond start_mark:{start_mark}")
    if start_contain:
        start_location += len(start_mark)
    setup1_text = text[start_location:]

    end_location = setup1_text.find(end_mark)
    if end_location == -1:
        raise Exception(f"not fond end mark:{end_mark}")
    if end_contain:
        end_location += len(end_mark)
    target_text = setup1_text[0:end_location]
    if split_key:
        target_text = target_text.split(split_key)[-1]
    return target_text
