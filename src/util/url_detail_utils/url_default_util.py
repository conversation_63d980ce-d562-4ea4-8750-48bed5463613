import hashlib
import json
import math
from datetime import datetime
from urllib.parse import parse_qs, urlencode
import re
import requests
from bs4 import BeautifulSoup

from src.util.url_detail_utils.base_util import string_mark_content


def get_ks_dur(url):
    """快手 url内容解析"""
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36",
        "origin": "https://www.kuaishou.com",
        "cookie": "kpf=PC_WEB; clientid=3; did=web_6e083e0a3aac4bcd91b52ac463c7cfa4; kpn=KUAISHOU_VISION; userId=2888848352; kuaishou.server.webday7_st=ChprdWFpc2hvdS5zZXJ2ZXIud2ViZGF5Ny5zdBKwAUpq9R7CXx9U3Ey25PD8KQWt423pBX6LcKKRkI-PqSFNRsHPceV8YynYo7hu6LLVNU4hO-FYrxk0E_J2a-zbqI9ULyDhDv__maD4BSezyLR-Ca_QYUehJXON5je8lZ58VPSMMkQ_pX4IEMM4G_lpCr88c2kdrIHlJKICZo51SKSn4pTqP8dOISFmkPZGMWZc8L2uwL5sfibo5C6sSfEJ3NaIj2XlCs1AUT4MsJdmviQKGhKQHK981JaYVDFOSZbyEEWAlpMiIO2BsnETSe1ZLi6q6S-xA_NF-qokqdWt4kGUPOqfec1oKAUwAQ; kuaishou.server.webday7_ph=fe78019caadb4f38b64ea893598f6467f1a9"
    }
    response = requests.get(url, headers=headers)
    response.raise_for_status()
    if response.text:
        print(response.text)
        author_id = string_mark_content(response.text, '"author":{"type":"id","generated":false,"id":"', '",',
                                        start_contain=True)

        author_data = string_mark_content(response.text, f'"{author_id}":', '"}', start_contain=True,
                                          end_contain=True)

        author = json.loads(author_data)
        photo_id = string_mark_content(response.text, '"photo":{"type":"id","generated":false,"id":"', '"',
                                       start_contain=True)
        photo_data = string_mark_content(response.text, f'"{photo_id}":', ',"riskTagUrl"', start_contain=True)
        photo = json.loads(photo_data + "}")
        duration_minutes = max(math.ceil(int(photo.get("duration")) / 1000 / 60), 1)
        return {
            'duration_minutes': duration_minutes,
            'avatar': author.get("headerUrl"),
            'nickname': author.get("name"),
            "title": photo.get("caption")
        }

    return {}


def get_xhs_dur(url):
    """小红书 url内容解析"""
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36",
        "cookie": "abRequestId=e3df3e5e-4c47-52ca-b99b-36ac80efa541; a1=19715b583edy6i8jvfkijwe6r6ztmneaczxgdzwgq50000281272; webId=29231cf73d32e3827733ce54f0cbaf22; gid=yjWy2D2YdjSDyjWy2D2YquuEdfvK3YVhiT3VCkAEdKFKuk2896Id0f888JYyJWJ8K8yWYd0q; web_session=040069b3d2d3a5d4611ebd00743a4b6b3a87fd; xsecappid=xhs-pc-web; webBuild=4.72.0; acw_tc=0a5086db17528266937904809e82e56bb38dc1d1ca0f61539e6686c64d8493; websectiga=f47eda31ec99545da40c2f731f0630efd2b0959e1dd10d5fedac3dce0bd1e04d; sec_poison_id=c8a0af9e-5ec8-409c-b4f5-961366b0ca9c; loadts=1752826736458; unread={%22ub%22:%22685fbcb7000000001002461c%22%2C%22ue%22:%226867bf7f000000002201fa69%22%2C%22uc%22:25}"
    }
    response = requests.get(url, headers=headers)
    # response = requests.get(url)
    soup = BeautifulSoup(response.content, "html.parser")
    # 从<script name="schema:podcast-show" type="application/ld+json">  和 </script>中获取json数据
    scripts = soup.find_all('script')
    target_script = None
    for script in scripts:
        if "window.__INITIAL_STATE__=" in script.text:
            target_script = script
    if target_script is None:
        return {}
    target_str = target_script.text.replace("window.__INITIAL_STATE__=", "").replace("undefined", "null")
    target_data = json.loads(target_str)

    note_id = target_data.get("note", {}).get("firstNoteId")
    if not note_id:
        return None
    detail = target_data.get("note", {}).get("noteDetailMap", {}).get(note_id, {}).get("note")
    if not detail:
        return None
    video = detail.get("video")
    if not video:
        return {}
    user = detail.get("user", {})
    title = detail.get("title")
    duration_second = detail.get("video", {}).get("capa", {}).get("duration", 0)
    duration_minutes = max(math.ceil(int(duration_second) / 60), 1)
    avatar = user.get("avatar")
    nickname = user.get("nickname")
    return {
        'duration_minutes': duration_minutes,
        'avatar': avatar,
        'nickname': nickname,
        "title": title,
        "video": True
    }

def get_xyz_dur(epi):
    response = requests.get(
        f"https://www.xiaoyuzhoufm.com/episode/{epi}",
        headers={
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36"
        },
    )
    soup = BeautifulSoup(response.content, "html.parser")
    # 从<script name="schema:podcast-show" type="application/ld+json">  和 </script>中获取json数据
    script = soup.find("script", attrs={"name": "schema:podcast-show"})
    if not script:
        return {}
    datas = json.loads(script.string)
    return datas


def appsign(params, appkey, appsec):
    "为请求参数进行 APP 签名"
    params.update({"appkey": appkey})
    params = dict(sorted(params.items()))  # 按照 key 重排参数
    query = urlencode(params)  # 序列化参数
    sign = hashlib.md5((query + appsec).encode()).hexdigest()  # 计算 api 签名
    params.update({"sign": sign})
    return params


def get_bvid_from_av(av_id):
    url = f"https://api.bilibili.com/x/web-interface/view?aid={av_id}"
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.150 Safari/537.36'
    }

    response = requests.get(url, headers=headers)
    if response.status_code == 200:
        data = response.json()
        if 'data' in data and 'bvid' in data['data']:
            return data['data']['bvid']
        else:
            return av_id
    else:
        return av_id


def get_bil_dur(result):
    query_params = parse_qs(result.query)

    bv_id = query_params.get("bvid")
    if bv_id:
        bv_id = bv_id[0]

    p = int(query_params.get("p", [1])[0])

    query_params = {"p": str(p)}

    if not bv_id:
        bv_id = result.path.split("/")[2]
    pattern = r'av\d*'
    match = re.match(pattern, bv_id.lower())
    if match:
        bv_id = get_bvid_from_av(int(bv_id[2:]))
    # b战视频api访问会多次会被ban Ip，需要登录?
    # 获取视频信息
    # '获取最新的 img_key 和 sub_key'
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3",
        "Referer": "https://www.bilibili.com/",
    }
    params = appsign({"bvid": bv_id}, "1d8b6e7d45233436", "560c52ccd288fed045859ed18bffd973")
    query = urlencode(params)
    # 控制3s超时时间
    resp = requests.get("https://api.bilibili.com/x/web-interface/view?" + query, headers=headers, timeout=3)
    data = resp.json()
    if data["code"] == 0:
        return data["data"], query_params
    return None, None



def get_youtube_dur(video_id, api_key):
    """
    Fetches YouTube video details including duration and title.

    :param video_id: YouTube video ID
    :param api_key
    :return: Tuple containing duration in minutes and video title
    """
    api_url = "https://googleapis.zimuzu.org/youtube/v3/videos"
    params = {
        "id": video_id,
        "key": api_key,
        "part": "contentDetails,snippet",
    }
    response = requests.get(api_url, params=params, timeout=3)

    if response.status_code != 200:
        return None
    return response.json()



def get_video_duration():
    # 15 【 装修-小韩 | 小红书 - 你的生活兴趣社区】 😆yccBWsRLuWYQ1I5 😆https://www.xiaohongshu.com/discovery/item/6867a73000000000100127ee?source=webshare&xhsshare=pc_web&xsec_token=ABhQY7vo2dHiwrXJZjDZMLmOdfII1PIePDwCOJxL8GCB0=&xsec_source=pc_share
    url = 'https://www.kuaishou.com/short-video/3xj5kz87qe3eybc?authorId=3xq8nfnzic87xxw&streamSource=brilliant&hotChannelId=00&area=brilliantxxcarefully'
    print(datetime.now())
    xhs_data = get_ks_dur(url)
    duration_minutes = xhs_data.get('duration_minutes')
    nickname = xhs_data.get('nickname')
    avatar = xhs_data.get('avatar')
    title = xhs_data.get('title')
    print(datetime.now())
    print(f'duration_minutes::{duration_minutes}')
    print(f'nickname::{nickname}')
    print(f"avatar::{avatar}")
    print(f"title::{title}")


if __name__ == '__main__':
    get_video_duration()
