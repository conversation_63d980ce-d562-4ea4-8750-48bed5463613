import asyncio
from pathlib import Path

import aiofiles


async def file_iterator(path: Path, chunk_size: int = 1024 * 200, max_rate: int = 1024 * 1024):
    """异步读取文件，限制下载速度不超过 max_rate (200kb/s) chunk_size 为字节"""
    async with aiofiles.open(path, mode="rb") as f:
        while True:
            # 每次读取200kb
            chunk = await f.read(chunk_size)
            if not chunk:
                break
            yield chunk
            await asyncio.sleep(chunk_size / max_rate)  # 控制速度
