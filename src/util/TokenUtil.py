import random
import string
from datetime import datetime, timedelta
from typing import Optional

import jwt
from fastapi import Depends
from starlette import status
from starlette.exceptions import HTTPException
from starlette.requests import Request

from config import settings
from src.logger.logUtil import get_logger
from src.orm.redis_cache import common_cache
from src.util.cacheUtil import ttl_cache
from src.util.redis_util import get_redis_client

logger = get_logger(__name__)

SECRET_KEY = "20241111"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_DAY = 30
TOKEN_REFRESH_THRESHOLD_DAYS = 25  # Refresh token if it expires within this many days


def create_jwt(data: dict) -> str:
    payload = {
        "email": data.get("email"),
        "wx_open_id": data.get("wx_open_id"),
        "sub": data.get("biz_id"),
        "name": data.get("name"),
        "exp": datetime.now() + timedelta(days=ACCESS_TOKEN_EXPIRE_DAY),
    }
    return jwt.encode(payload, SECRET_KEY, algorithm=ALGORITHM)


def verify_token(token: str) -> Optional[dict]:
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        exp = datetime.utcfromtimestamp(payload.get("exp"))
        if exp - datetime.utcnow() < timedelta(days=TOKEN_REFRESH_THRESHOLD_DAYS):
            new_token = create_jwt({"sub": payload.get("sub")})
            return {"payload": payload, "token": new_token}
        return {"payload": payload, "token": token}
    except jwt.PyJWTError:
        return None


@common_cache.cache_on_arguments(namespace="get_6_random_mail_code", expiration_time=600)
def get_6_random_mail_code(email):
    # 生成6位随机数字验证码
    return "".join(random.sample(string.digits, 6))


def get_token_from_request(request: Request) -> Optional[str]:
    return request.headers.get("Authorization") or request.cookies.get("next-auth.session-token")


async def get_current_user(request: Request):
    token = get_token_from_request(request)
    if not token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Not authenticated",
            headers={"Authorization": "Bearer"},
        )

    dicts = verify_token(token)
    if not dicts:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"Authorization": "Bearer"},
        )
    try:
        user = ttl_cache.get(dicts.get("payload").get("sub"))
        if user:
            return user
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"Authorization": "Bearer"},
        )
    except Exception:
        logger.exception("Error getting user: ")

    raise HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Invalid authentication credentials",
        headers={"Authorization": "Bearer"},
    )


async def limit_current_user(request: Request):
    token = get_token_from_request(request)
    if not token:
        return None
    dicts = verify_token(token)
    try:
        user = ttl_cache.get(dicts.get("payload").get("sub"))
        if user:
            return user
        raise None
    except Exception:
        return None


# 下载限制检查依赖项
async def download_rate_limit(user: str = Depends(get_current_user)):
    key = f"download:{user.biz_id}"
    ttl = 60  # 1分钟
    limit = 4  # 单位时间内的限制次数

    redis_client = get_redis_client()
    current_count = redis_client.get(key)
    if current_count:
        current_count = int(current_count)
        if current_count >= limit:
            return True
        redis_client.incr(key)
    else:
        redis_client.set(key, 1, ex=ttl)
    return False


async def call_rate_limit(request: Request, app_key, limit_per_minute: int = 4):
    """限制某个接口的调用频率 (默认 4 次/分钟)"""
    route_name = request.scope["route"].name  # 获取当前路由名称
    key = f"rate_limit:{route_name}:{app_key}"  # 以 IP 作为限流依据
    ttl = 60  # 限制时间窗口 60 秒

    redis_client = get_redis_client()
    current_count = redis_client.get(key)
    if current_count:
        current_count = int(current_count)
        if current_count >= limit_per_minute:
            return True
        redis_client.incr(key)
    else:
        redis_client.set(key, 1, ex=ttl)
    return False


def verify_key(request: Request):
    key = request.headers.get("llmSecretKey")
    if key != settings.LLM_SECRET_KEY:
        raise HTTPException(status_code=403, detail="key is not valid")
    return key


async def get_share_user(request: Request):
    token = get_token_from_request(request)
    if not token:
        return None

    dicts = verify_token(token)
    if not dicts:
        return None
    try:
        user = ttl_cache.get(dicts.get("payload").get("sub"))
        if user:
            return user
        return None
    except Exception:
        logger.exception("Error getting user: ")
    return None
