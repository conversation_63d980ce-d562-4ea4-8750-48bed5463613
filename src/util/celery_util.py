from urllib.parse import parse_qs, urlparse

import requests
from celery import Celery

from config import settings
from src.logger.logUtil import get_celery_logger
from src.util.cacheUtil import open_api_cache

redis_broker_url = f"redis://:{settings.REDISPASSWORD}@{settings.REDISHOST}:{settings.REDISPORT}/0"

celery = Celery("aihaoji", broker=redis_broker_url)

celery.conf.update(broker_connection_retry_on_startup=True)
celery.autodiscover_tasks(["src.corn.downloads", "src.corn.book2audio"])

celery_logger = get_celery_logger(__name__)


@celery.task
def call_baidu(logid_url):
    celery_logger.info(f"Testing celery is working, logid_url is {logid_url}")
    if logid_url:
        parsed_url = urlparse(logid_url)
        query_params = parse_qs(parsed_url.query)
        price = query_params.get("price", [None])[0]
        # 服务购买成功
        latest_conversion = {"logidUrl": str(logid_url), "newType": 10}
        if price:
            try:
                # API 要求为单位为分
                latest_conversion["convertValue"] = int(float(price) * 100)
            except Exception:
                celery_logger.info(f"logidUrl: {logid_url} no price value")
        try:
            url = "https://ocpc.baidu.com/ocpcapi/api/uploadConvertData"
            headers = {"Content-type": "application/json"}
            resp = requests.post(
                url=url,
                headers=headers,
                json={
                    "token": settings.SEM_BAIDU_TOKEN,
                    # 18 留线索, 3 表单提交成功, 10 服务购买成功
                    "conversionTypes": [
                        {"logidUrl": str(logid_url), "newType": 18},
                        {"logidUrl": str(logid_url), "newType": 3},
                        latest_conversion,
                    ],
                },
            )
            if resp.status_code == 200:
                celery_logger.info(f"{logid_url} {resp.json()} 记录成功")
        except Exception:
            celery_logger.error(f"{logid_url} 记录出现异常", exc_info=True)
        celery_logger.info("Testing celery is working")


def sem_to_baidu(order_id):
    try:
        celery_logger.info(f"sem_to_baidu key: order_id_{order_id}")
        if logid_url := open_api_cache.get(f"order_id_{order_id}"):
            call_baidu.delay(logid_url)
            celery_logger.info(f"call_baidu.delay logid_url: {logid_url}")
    except Exception:
        celery_logger.error("pay-result call_baidu error {logid_url}")
