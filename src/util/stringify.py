import base64
import enum
import json
import random
import re
import secrets
import string
import traceback
from datetime import datetime, date, time
from decimal import Decimal
from uuid import UUID, uuid1

import bcrypt
from email_validator import validate_email, EmailNotValidError

TIME_FTIME = '%Y-%m-%d %H:%M:%S'
DATE_FTIME = '%Y-%m-%d'
ONLY_TIME = "%H:%M:%S"


class DateEncoder(json.JSONEncoder):
    def default(self, obj):
        """
        重新 JSONEncoder 转换方式
        :param obj:
        :return:
        """
        if isinstance(obj, Decimal):
            return float(obj)
        elif isinstance(obj, datetime):
            return obj.strftime(TIME_FTIME)
        elif isinstance(obj, date):
            return obj.strftime(DATE_FTIME)
        elif isinstance(obj, enum.Enum):
            return obj.value
        elif isinstance(obj, time):
            return obj.strftime(ONLY_TIME)
        else:
            return json.JSONEncoder.default(self, obj)


def str_to_bool(value):
    if isinstance(value, bool):
        return value
    if isinstance(value, str):
        return value.lower() in ["true", "1", "t", "y", "yes"]
    return False


def mask_phone_regex(phone):
    """使用正则表达式替换中间4位"""
    return re.sub(r'(\d{3})\d{4}(\d{4})', r'\1****\2', phone)


def mask_string_flexible(s, front=2, back=2, min_length=4):
    """
    更灵活地配置保留前后字符数
    :param s
    :param front: 保留前面字符数
    :param back: 保留后面字符数
    :param min_length: 最小处理长度
    """
    length = len(s)
    if length < min_length:
        return s

    if length <= (front + back):
        return s[:front] + '*' * (length - front)
    else:
        return s[:front] + '*' * (length - front - back) + s[-back:]


def is_email(s):
    # pattern = r'^[\w\.-]+@[\w-]+\.[a-zA-Z]{2,6}$'
    # return bool(re.fullmatch(pattern, s))
    try:
        validate_email(s, check_deliverability=False)
        return True
    except EmailNotValidError:
        return False


def is_phone(s):
    pattern = r'^1[3-9]\d{9}$'
    return bool(re.fullmatch(pattern, s))


def debug_is_phone(s):
    pattern = r'^11\d{9}$'
    return bool(re.fullmatch(pattern, s))


def is_empty(s) -> bool:
    """字符串是否为空"""
    return s is None or len(str(s).strip()) == 0


def string_mark_content(text, start_mark, end_mark, split_key=None):
    """提取字符串内指定位置内容"""
    try:
        start_location = text.find(start_mark)
        if start_location == -1:
            raise Exception(f"not fond start_mark:{start_mark}")
        setup1_text = text[start_location:]

        end_location = setup1_text.find(end_mark)
        if end_location == -1:
            raise Exception(f"not fond end mark:{end_mark}")
        target_text = setup1_text[0:end_location]
        if split_key:
            target_text = target_text.split(split_key)[-1]
        return target_text
    except Exception:
        return None


def random_letters(k: int = 6):
    return "".join(random.sample(string.ascii_letters + string.digits, k))


def get_uid():
    return str(UUID(int=random.getrandbits(128)))


def get_str_key(k=32):
    return str(UUID(int=random.getrandbits(k)))


def get_uid_system():
    return uuid1()


def hash_password(password: str) -> str:
    salt = bcrypt.gensalt(10, prefix=b"2a")
    hashed_password = bcrypt.hashpw(password.encode(), salt)
    return hashed_password.decode()


def verify_password(plain_password: str, hashed_password: str) -> bool:
    if not plain_password or not hashed_password:
        return False
    return bcrypt.checkpw(plain_password.encode(), hashed_password.encode())


def random_number(length: int) -> str:
    return str(random.randint(1, 9)) + ''.join([str(random.randint(0, 9)) for _ in range(length - 1)])


def generate_secure_random_hex(size=32):
    random_bytes = secrets.token_bytes(size)
    # 将随机数转换为十六进制字符串
    hex_representation = random_bytes.hex()
    return hex_representation


def base64str(k: str):
    return base64.b64encode(k.encode('utf-8')).decode('utf-8')


def sa_instance_state_remove(obj_dict: dict):
    if "_sa_instance_state" in obj_dict:
        del obj_dict["_sa_instance_state"]
    return obj_dict


def from_json(obj, indent=4) -> str:
    return json.dumps(obj, indent=indent, ensure_ascii=False, cls=DateEncoder)


def from_exception(error: Exception) -> str:
    tb_obj = traceback.TracebackException.from_exception(error)
    tb_str = ''.join(tb_obj.format())
    return tb_str


async def context_count(content, value):
    try:
        count = content.count(value)
        return count
    except Exception:
        return 0
