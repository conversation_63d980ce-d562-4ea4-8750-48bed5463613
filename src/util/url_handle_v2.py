from urllib.parse import urlparse

import requests

from src.logger.logUtil import get_logger
from src.model.url_detail import (
    LectureUrlDetail,
    parse_alipan_url,
    parse_baidu_url,
    parse_bilibili_url,
    parse_douyin_url,
    parse_kuaishou_url,
    parse_xiaohongshu_url,
    parse_xyz_url,
    parse_youtube_url,
)

logger = get_logger(__name__)


class YoutubeHandler:
    @staticmethod
    def process(url) -> LectureUrlDetail:
        return parse_youtube_url(url)


class BilibiliHandler:
    @staticmethod
    def process(url) -> LectureUrlDetail:
        return parse_bilibili_url(url)


class XyzHandler:
    @staticmethod
    def process(url) -> LectureUrlDetail:
        return parse_xyz_url(url)


class XiaoHongShu:
    @staticmethod
    def process(url) -> LectureUrlDetail:
        return parse_xiaohongshu_url(url)


class DouYin:
    @staticmethod
    def process(url) -> LectureUrlDetail:
        return parse_douyin_url(url)


class KuaiShou:
    @staticmethod
    def process(url) -> LectureUrlDetail:
        return parse_kuaishou_url(url)


class BaiDuPan:
    @staticmethod
    def process(url) -> LectureUrlDetail:
        return parse_baidu_url(url)


class AliPan:
    @staticmethod
    def process(url) -> LectureUrlDetail:
        return parse_alipan_url(url)


class SourceHandler:
    handlers = {
        "youtube": YoutubeHandler,
        "bilibili": BilibiliHandler,
        "xiaoyuzhou": XyzHandler,
        "xiaohongshu": XiaoHongShu,
        "douyin": DouYin,
        "kuaishou": KuaiShou,
        "baidupan": BaiDuPan,
        "alipan": AliPan,
    }

    @classmethod
    def handle_source(cls, url, source) -> LectureUrlDetail | None:
        # Simplify the check for 'Short' URLs and handle them accordingly
        if "Short" in source:
            source = source.replace("Short", "")
            # Remove 'Short' from the source to get the original source name
            try:
                # Attempt to get the full link
                url = get_full_link(url)
            except Exception as e:
                # Log the exception with the URL for better debugging
                logger.exception(f"handle full link error, url={url}, error={e}")

        # Attempt to find and use the appropriate handler based on the source
        handler = cls.handlers.get(source)
        if handler:
            try:
                return handler.process(url)
            except Exception as e:
                logger.exception(f"handle source error, url={url}, source={source}, error={e}")
                return None

    @classmethod
    async def handle_source_list(cls, url, source) -> (LectureUrlDetail | None, str):
        # Simplify the check for 'Short' URLs and handle them accordingly
        if "Short" in source:
            source = source.replace("Short", "")
            # Remove 'Short' from the source to get the original source name
            try:
                # Attempt to get the full link
                url = get_full_link(url)
            except Exception as e:
                # Log the exception with the URL for better debugging
                logger.exception(f"handle full link error, url={url}, error={e}")

        # Attempt to find and use the appropriate handler based on the source
        handler = cls.handlers.get(source)
        if handler:
            try:
                return handler.process(url), url
            except Exception as e:
                logger.exception(f"handle source error, url={url}, source={source}, error={e}")
                return None, url
        return None, url


def get_full_link(url: str):
    """
    设置最多跳转次数，超过则抛出异常
    :param url:
    :param max_redirects:
    :return:
    """
    result = urlparse(url)
    print("=====>", result.netloc)
    if result.netloc == "youtu.be":
        return local_get_youtube_full_link(result)
    elif result.netloc == "xhslink.com":
        return url
    else:
        response = requests.head(url, allow_redirects=True, timeout=3)
        return response.url


def local_get_youtube_full_link(result):
    """
    本地获取youtube跳转链接
    :param result
    :return:
    """
    # 如果url有带？，则去掉后面的参数
    url = result.path
    if "?" in url:
        url = url.split("?")[0]
    return f"https://www.youtube.com/watch?v={url[1:]}"


if __name__ == "__main__":
    print(SourceHandler.handle_source("https://www.xiaoyuzhoufm.com/episode/6734353ff373fe5d4de79016", "xiaoyuzhou"))
    # print(get_youtube_details("1BRebNUbqaI"))
