from cryptography.hazmat.primitives.ciphers.aead import AESGCM
from wechatpy import WeChatOAuth
from wechatpy.client import WeChatClient
from wechatpy.pay import WeChatPay

from config import settings
from src.logger.logUtil import get_logger
from src.orm.local_cache import local_common_cache

logger = get_logger(__name__)


class WechatBaseClient:
    def __init__(self):
        self.app_id = settings.WECHAT_APP_ID
        self.api_key = settings.WECHAT_API_KEY
        self.secret = settings.WECHAT_APP_SECRET
        self.token = settings.WECHAT_TOKEN
        self.prefix = "wechatpy"

    def get_we_chat_client(self):
        """
        获取WeChatClient对象
        :return:WeChatClient对象
        """
        session_interface = local_common_cache
        return WeChatClient(self.app_id, self.secret, session=session_interface)

    # pc
    def get_wechat_oauth(self, state=None):
        return WeChatOAuth(
            settings.WECHAT_APP_ID,
            settings.WECHAT_APP_SECRET,
            "https://aihaoji.com/api/v1/login",
            scope="snsapi_userinfo",
            state=state,
        )

    # 手机appID
    def get_wechat_mobile_oauth(self, state=None):
        return WeChatOAuth(
            settings.WECHAT_MOBILE_APP_ID,
            settings.WECHAT_MOBILE_APP_SECRET,
            "https://aihaoji.com/api/v1/login",
            scope="snsapi_userinfo",
            state=state,
        )

    def get_wechat_pay(self):
        return WeChatPay(
            self.app_id,
            self.api_key,
            mch_id="1698831790",
            mch_key="static/pay/apiclient_key.pem",
            mch_cert="static/pay/apiclient_cert.pem",
            timeout=10,
        )

    def aes_256_decrypt(self, nonce, associated_data, ciphertext):
        """
        Decrypts the given ciphertext using AES-256-GCM.

        :param key: The encryption key (32 bytes for AES-256)
        :param nonce: The nonce (12 bytes recommended)
        :param ciphertext: The encrypted data
        :param associated_data: Additional authenticated data (AAD)
        :param tag: The authentication tag (16 bytes)
        :return: The decrypted plaintext
        """
        aes_gcm = AESGCM(self.api_key)
        return aes_gcm.decrypt(nonce, ciphertext, associated_data)

    @staticmethod
    def wechat_xml_call_back():
        return "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>"

    def wechat_call_bacl(self):
        return '{"code": "SUCCESS"}'


wechat_base_client = WechatBaseClient().get_we_chat_client()
