import random
from datetime import datetime, timezone
from typing import Optional
from uuid import UUID

from src.logger.logUtil import get_logger
from src.model.affiliates_fans import AffiliatesFans, get_fans_by_user_id
from src.model.affiliates_manager import Affiliates, AffiliatesStatus, get_affiliates_by_token
from src.model.member_manager import update_member_by_points
from src.model.User_action_record_manager import UserActionProcess, UserActionType, add_user_action_record
from src.model.user_manager import UserPO

logger = get_logger(__name__)


def do_bind_fans(token: str, user: UserPO):
    """
    绑定粉丝
    :param token: 邀请code
    :param user: 用户id
    :param request: 请求
    :return:
    """
    user_id = user.biz_id
    if token:
        affiliates = get_affiliates_by_token(token)
        if not affiliates:
            logger.error(f"token not exists, token={token}")
            return
        if affiliates.user_id == user_id:
            logger.error("can't bind yourself")
            return
        if affiliates.status != AffiliatesStatus.SUCCESS:
            logger.error("Affiliates not audit success , can't bind fans")
            return
    else:
        logger.error(f"token is empty，user_id={user_id}")
        return

    if affiliates.biz_id == user_id:
        logger.error(f"can't bind yourself,user_id={user_id}")
        return

    fans = get_fans_by_user_id(user_id)
    if fans and fans.affiliates_id != affiliates.biz_id:
        logger.error(f"has bind to another affiliates，user_id={user_id}")
        return

    if not fans:
        fans = AffiliatesFans.create(
            biz_id=str(UUID(int=random.getrandbits(128))),
            user_id=user_id,
            affiliates_id=affiliates.biz_id,
            name=user.name,
            bind_time=datetime.now(tz=timezone.utc),
        )
        affiliates.update(fans_count=Affiliates.fans_count + 1, total_point=Affiliates.total_point + 30).where(
            Affiliates.biz_id == affiliates.biz_id
        ).execute()
        # send point and record
        try:
            update_member_by_points(fans.user_id, 30)
            add_user_action_record(
                affiliates.user_id, UserActionType.INVITE, UserActionProcess.ADD, "邀请用户加盟", 30, fans.user_id
            )
            update_member_by_points(str(affiliates.user_id), 30)
            add_user_action_record(
                user_id, UserActionType.INVITE, UserActionProcess.ADD, "被邀请成为粉丝", 30, str(affiliates.user_id)
            )
        except Exception:
            logger.exception(f"Error add platform point,user_id={affiliates.biz_id}")


def record_user_extra_info(check_user: UserPO, utm_source: Optional[str] = "", extra_info: Optional[dict] = None):
    origin_extra_info = check_user.extra_info_json
    if extra_info is None:
        extra_info = {}
    for key, value in extra_info.items():
        if isinstance(value, str) and value:
            origin_extra_info[key] = value
    if utm_source:
        origin_extra_info["utm_source"] = utm_source
    try:
        if origin_extra_info["utm_source"] == "invite":
            do_bind_fans(origin_extra_info["utm_content"], check_user)
    except Exception as e:
        logger.error("record_user_extra_info error, ", e)
        pass
    check_user.extra_info_json = origin_extra_info
    check_user.save()
