import redis

from config import settings

redis_pool = redis.ConnectionPool(
    host=settings.REDISHOST,
    port=settings.REDISPORT,
    username=settings.REDISUSER,
    password=settings.REDISPASSWORD,
    decode_responses=True,
    max_connections=settings.REDIS_MAX_CONECTIONS,
)


def get_redis_client(redis_pool=redis_pool):
    if not redis_pool:
        return None
    redis_client = redis.Redis(connection_pool=redis_pool)
    return redis_client


def create_redis_pool(app):
    app.state.redis_pool = redis_pool


def shutdown_redis_pool(app):
    if app.state.redis_pool:
        app.state.redis_pool.disconnect()
