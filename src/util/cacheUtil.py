import json
import os
import time
from datetime import datetime

from dogpile.cache import make_region

from config import settings

ttl_cache = make_region().configure(
    "dogpile.cache.redis",
    arguments={
        "host": settings.REDISHOST,
        "port": settings.REDISPORT,
        "db": 0,
        "redis_expiration_time": 60 * 60 * 24 * 7,  # 7天
        "username": settings.REDISUSER,
        "password": settings.REDISPASSWORD,
    },
)

open_api_cache = make_region().configure(
    "dogpile.cache.redis",
    arguments={
        "host": settings.REDISHOST,
        "port": settings.REDISPORT,
        "db": 0,
        "username": settings.REDISUSER,
        "password": settings.REDISPASSWORD,
        "redis_expiration_time": 60 * 60 * 24,  # 1天
    },
)

expiration_times = {}


def set_cache(cache, redis_key, data, expires=60 * 60 * 24):
    """设置缓存带过期时间"""
    cache.set(redis_key, data)
    cache.backend.writer_client.expire(redis_key, expires)


def set_key_expiration_time(key, expiration):
    pass


class DotDict(dict):
    """
    A dictionary that supports accessing keys as attributes.
    """

    def __getattr__(self, attr):
        try:
            return self[attr]
        except KeyError:
            raise AttributeError(f"'DotDict' object has no attribute '{attr}'")

    def __setattr__(self, attr, value):
        self[attr] = value

    def __delattr__(self, attr):
        try:
            del self[attr]
        except KeyError:
            raise AttributeError(f"'DotDict' object has no attribute '{attr}'")


def datetime_to_timestamp(data):
    if isinstance(data, dict):
        return {k: datetime_to_timestamp(v) for k, v in data.items()}
    elif isinstance(data, datetime):
        return {"__datetime__": True, "value": data.timestamp()}
    else:
        return data


def timestamp_to_datetime(data):
    if isinstance(data, dict):
        if "__datetime__" in data:
            return datetime.fromtimestamp(data["value"])
        return {k: timestamp_to_datetime(v) for k, v in data.items()}
    else:
        return data


def load_cache_from_file():
    current_time = time.time()
    if os.path.exists(settings.CACHE_FILE):
        with open(settings.CACHE_FILE, "r") as f:
            if f is None:
                return
            data = json.load(f)
            for key, (value, expiration_time) in data.items():
                if expiration_time > current_time:
                    ttl_cache.set(key, DotDict(timestamp_to_datetime(value)))
                    expiration_times[key] = expiration_time
    if os.path.exists(settings.OPEN_CACHE_FILE):
        with open(settings.OPEN_CACHE_FILE, "r") as f:
            try:
                if f is None:
                    return
                data2 = json.load(f)
                for key, (value, expiration_time) in data2.items():
                    if expiration_time > current_time:
                        open_api_cache.set(key, DotDict(timestamp_to_datetime(value)))
                        expiration_times[key] = expiration_time
            except Exception:
                pass


def should_cache_fn(*args, **kwargs):
    return_value = kwargs.get("return_value", None)
    return return_value is not None
