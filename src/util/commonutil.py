from enum import Enum
from http.client import HTTPException
from typing import Any

from pydantic import BaseModel


class ErrorCodeEnum(Enum):
    GENERATING_DOWNLOAD_FILE = ("GENERATING_DOWNLOAD_FILE", "下载文件正在生成", 10000)
    FAILED = ("FAILED", "操作失败", 10001)
    USER_UNAUTHORIZED = ("USER_UNAUTHORIZED", "用户未授权", 10002)
    USER_EXIST = ("USER_EXIST", "用户已存在", 10003)
    EMAIL_UNAUTHORIZED = ("EMAIL_EXIST", "未认证邮箱", 10004)
    ERROR_TOKEN_CHECK = ("ERROR_TOKEN_CHECK", "token校验失败", 10005)
    UNKNOWN_USER = ("UNKNOWN_USER", "未知用户", 10006)
    TASK_REPEAT = ("TASK_REPEAT", "任务重复", 10010)
    NOT_FOUND = ("NOT_FOUND", "未找到资源", 10011)
    UNKNOWN_PRODUCT = ("UNKNOWN_PRODUCT", "未知产品", 10012)
    WECHAT_PAY_ERROR = ("WECHAT_PAY_ERROR", "微信支付失败", 10013)
    WECHAT_PAY_LATTER = ("WECHAT_PAY_LATTER", "请稍后查询微信支付结果", 10014)
    NEED_BIND_WECHAT_PUBLIC_ACCOUNT = ("NEED_BIND_WECHAT_PUBLIC_ACCOUNT", "请先关注公众号", 10015)
    ORDER_LOCKED = ("ORDER_LOCKED", "重复创建订单", 10016)
    PARAMS_ERROR = ("PARAMS_ERROR", "参数异常", 10017)
    USER_NOT_EXIST = ("PARAMS_ERROR", "邮箱不存在", 10017)
    ALIPAY_ERROR = ("ALIPAY_ERROR", "支付宝支付失败", 10018)
    ALIPAY_MOBILE_ERROR = ("ALIPAY_MOBILE_ERROR", "移动端支付宝支付失败", 10033)
    EXCHANGE_CODE_ERROR = ("EXCHANGE_CODE_ERROR", "兑换码兑换失败", 10019)
    EXCHANGE_CODE_USED = ("EXCHANGE_CODE_ERROR", "兑换码已经被兑换", 10020)
    EXCHANGE_CODE_HAS_CHARGE = ("EXCHANGE_CODE_HAS_CHARGE", "已经兑换过兑换码", 10021)
    EXCHANGE_CODE_NOT_MATCH = ("EXCHANGE_CODE_NOT_MATCH", "非本人兑换码", 10022)
    EXCHANGE_CODE_EXPIRED = ("EXCHANGE_CODE_EXPIRED", "验证码已过期", 10023)
    WECHAT_UNAUTHORIZED = ("WECHAT_UNAUTHORIZED", "微信用户未授权", 10024)
    THIRD_TOKEN_ERROR = ("THIRD_TOKEN_ERROR", "第三方用户未授权", 10024)
    DOWNLOAD_ERROR = ("DOWNLOAD_ERROR", "文件正在生成中", 10025)
    FOLDER_EXISTS_ERROR = ("FOLDER_EXISTS_ERROR", "文件夹已经存在", 10026)
    FOLDER_NESTED_ERROR = ("FOLDER_NESTED_ERROR", "文件夹不支持嵌套", 10027)
    FOLDER_NOT_EXISTS = ("FOLDER_NOT_EXISTS", "文件夹不存在", 10028)
    TASK_NOT_EXISTS = ("TASK_NOT_EXISTS", "任务不存在", 10029)
    FOLDER_CAN_NOT_RENAME = ("FOLDER_CAN_NOT_RENAME", "该文件夹不支持重命名", 10030)
    FOLDER_CAN_NOT_DELETE = ("FOLDER_CAN_NOT_DELETE", "该文件夹不支持删除", 10031)
    TASK_FILE_NOT_EXISTS = ("TASK_FILE_NOT_EXISTS", "任务对应的播客文件不存在", 10032)
    USER_NO_QUOTA = ("USER_NO_QUOTA", "用户积分不足", 10007)

    WECHAT_AUTH_TOKEN_ERROR = ("WECHAT_AUTH_TOKEN_ERROR", "微信认证失败", 10301)
    WECHAT_NEED_CODE = ("WECHAT_NEED_CODE", "需要用户认证授权", 10302)
    ACCOUNT_ERROR = ("ACCOUNT_ERROR", "账号异常，请联系客服", 10303)
    USER_NO_EXIST_OR_LOGGED_OUT = ("USER_NO_EXIST_OR_LOGGED_OUT", "用户不存在或已注销", 10304)
    WECHAT_NEED_LOGOFF = ("WECHAT_NEED_LOGOFF", "该微信已有账号，请先注销/解绑其历史账号", 10305)
    WECHAT_API_ERROR = ("WECHAT_API_ERROR", "微信服务接口错误", 10306)

    IMAGE_CAPTCHA_ERROR = ("IMAGE_CAPTCHA_ERROR", "验证码选择不正确，请重新选择", 10310)

    METHOD_NOT_FOUND = ("METHOD_NOT_FOUND", "不支持当前方法", 10401)
    NO_PERMISSION = ("NO_PERMISSION", "没有权限", 10402)
    MOBILE_ERROR = ("MOBILE_ERROR", "手机号格式不正确", 10403)
    EMAIL_ERROR = ("EMAIL_ERROR", "邮箱格式不正确", 10404)
    CHECK_PATH_API = ("CHECK_PATH_API", "检查接口地址是否正确", 10405)
    UNKNOWN_ERROR = ("UNKNOWN_ERROR", "未知错误", 10406)
    PARAM_BATCH_NOT_DATA = ("PARAM_BATCH_NOT_DATA", "无需要处理的数据", 10407)
    BATCH_TOP_LIMIT = ("PARAM_BATCH_NOT_DATA", "超过批量上限{}", 10408)
    BATCH_ALL_ERROR = ("BATCH_ALL_ERROR", "批量处理全部失败", 10409)
    BATCH_PART_ERROR = ("BATCH_PART_ERROR", "批量处理部分失败", 10410)

    DEFAULT_FOLDER_NOT_EXISTS = ("DEFAULT_FOLDER_NOT_EXISTS", "默认笔记本不存在", 10432)

    ARTICLE_NOT_EXISTS = ("ARTICLE_NOT_EXISTS", "文章不存在", 10440)
    DETAIL_NOT_EXISTS = ("DETAIL_NOT_EXISTS", "文章内容不存在", 10441)

    SHARE_NOT_EXISTS = ("SHARE_NOT_EXISTS", "文章分享不存在", 10442)
    SHARE_TOKEN_ERROR = ("SHARE_TOKEN_ERROR", "分享token错误", 10443)

    # URL地址解析
    URL_PAN_NOT_DRIVE = ("URL_PAN_NOT_DRIVE", "网盘驱动ID未知", 10450)
    URL_NOT_DURATION = ("URL_NOT_DURATION", "视频时长未知", 10451)
    URL_NOT_ANALYSIS = ("URL_NOT_ANALYSIS", "地址解析失败", 10452)
    URL_UNRECOGNIZED = ("URL_UNRECOGNIZED", "地址无法识别", 10453)
    URL_CHARGING_VIDEO = ("URL_CHARGING_VIDEO", "充电视频不支持解析", 10454)

    # 流式任务
    STREAM_LOCK_ERROR = ("STREAM_LOCK_ERROR", "当前任务流已在其他平台启用", 10480)

    def __init__(self, code, message, error_code):
        self.code = code
        self.message = message
        self.error_code = error_code


class CommonResponse(BaseModel):
    code: int = 0
    message: str = "Success"
    data: Any


def common_enum_error(error: ErrorCodeEnum, data=None, formats: list[str] = None):
    if data is None:
        data = {}
    message = error.message
    if formats:
        count = str(error.message).count('{}')
        if len(formats) >= count:
            message = error.message.format(*formats)
    return CommonResponse(code=error.error_code, message=message, data=data)


def assert_field(condition, message=None):
    if not condition:
        raise HTTPException(message or "Assertion failed", "data")


def common_error(message="", code=10001, data=None):
    if data is None:
        data = {}
    return CommonResponse(code=code, message=message, data=data)


def common_third_error(message="", code=10024, data=None, verify=None):
    if data is None:
        data = {}
    return CommonResponse(code=code, message=message, data=data, verify=verify)


def common_old_password_error(message="", code=10034, data=None, verify=None):
    if data is None:
        data = {}
    return CommonResponse(code=code, message=message, data=data, verify=verify)


def common_balance_error(message="", code=10007, data=None):
    if data is None:
        data = {}
    return CommonResponse(code=code, message=message, data=data)


def common_third_success(data=None, code=0, message="success", verify=None):
    if data is None:
        data = {}
    return CommonResponse(code=code, message=message, data=data, verify=None)


def common_success(data=None, code=0, message="success"):
    if data is None:
        data = {}
    return CommonResponse(code=code, message=message, data=data)
