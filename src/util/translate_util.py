from typing import Optional

from iso639 import <PERSON>

from src.constant.prompt import translation_json_prompt, translation_prompt
from src.util.llm_interface import async_llm_chat


def lang_code_to_en_name(lang_code):
    name = <PERSON>(lang_code).name
    return name


async def llm_translate(text: str, in_language: str = "", out_language: Optional[str] = "Chinese"):
    if in_language:
        in_language = lang_code_to_en_name(in_language)
    if out_language:
        out_language = lang_code_to_en_name(out_language)
    user_prompt = translation_prompt["user"].format(in_language=in_language, out_language=out_language, text=text)
    prompt = {"system": translation_prompt["system"], "user": user_prompt}
    return await async_llm_chat(prompt)


async def llm_translate_json(json_str: str, in_language: str = "", out_language: Optional[str] = "Chinese"):
    if in_language:
        in_language = lang_code_to_en_name(in_language)
    if out_language:
        out_language = lang_code_to_en_name(out_language)
    user_prompt = translation_json_prompt["user"].format(
        json_str=json_str, in_language=in_language, out_language=out_language
    )
    prompt = {"system": translation_json_prompt["system"], "user": user_prompt}
    return await async_llm_chat(prompt, json=True)
