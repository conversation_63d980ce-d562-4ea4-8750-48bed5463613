import re
from pathlib import Path

import pypandoc

from src.logger.logUtil import get_celery_logger

celery_logger = get_celery_logger(__name__)


def clean_markdown_tags(text: str) -> str:
    # 替换开头的 ```Markdown（忽略大小写，允许后面有空格）
    text = re.sub(r"^```Markdown\s*", "", text, flags=re.IGNORECASE)
    # 替换结尾的 ```
    text = re.sub(r"```$", "", text)
    return text


def convert_latex_in_markdown(text):
    # 统一替换 \(...\) 为 $$ ...$$ 块级公式，后续 prompt 优化后并不再需要些函数
    # text = re.sub(r"\\\((.*?)\\\)", lambda m: "$$ " + m.group(1) + " $$", text) PDF一直转圈问题修复
    text = re.sub(r"\\\((.*?)\\\)", lambda m: "$" + m.group(1) + "$", text)
    return text


def sanitize_latex_commands(text):
    # 移除或替换不支持的 LaTeX 命令 PDF一直转圈问题修复
    return re.sub(r"\\symbb\{(.*?)\}", r"\1", text)

def reduce_list_nesting(text):
    """自动降低嵌套层级，防止 LaTeX 'Too deeply nested' 错误"""
    text = re.sub(r"^ {6,}- ", r"- ", text, flags=re.MULTILINE)  # 超过 3 级缩进的，降级
    text = re.sub(r"^ {6,}\d+\.", r"1.", text, flags=re.MULTILINE)  # 处理有序列表
    return text


def convert_md_to_pdf(markdown: str, pdf_file_url: str) -> bool:
    if r"\frac" in markdown:
        markdown = convert_latex_in_markdown(markdown)
    markdown = reduce_list_nesting(markdown)
    # 检查并清理非法的 LaTeX 命令（如 \symbb） PDF一直转圈问题修复
    markdown = sanitize_latex_commands(markdown)
    # 将 \n 原始输出
    markdown = markdown.replace(r"\n", r"``\n``")
    try:
        pypandoc.convert_text(
            markdown,
            "pdf",
            format="md",
            extra_args=[
                # 支持 LaTeX 数学公式
                "--mathjax",
                # pdflatex 不支持中文, 使用 xelatex
                "--pdf-engine=xelatex",
                # 设定中文字体
                "-V",
                "mainfont=Noto Sans CJK SC",
                # 让中文与英文保持相同缩进（来自GPT的建议）
                "-V",
                "CJKmainfont=Noto Sans CJK SC",
                "-V",
                "CJKsansfont=Noto Sans CJK SC",
                "-V",
                "CJKmonofont=Noto Sans Mono CJK SC",
                "--listings",  # 让代码块更好看
            ],
            outputfile=str(pdf_file_url),
            encoding="utf-8",
        )
        return True
    except Exception:
        Path(pdf_file_url).unlink(missing_ok=True)
        celery_logger.error("convert_md_to_pdf error", exc_info=True)
    return False


def convert_md_to_docx(markdown: str, docx_file_url: str) -> bool:
    try:
        pypandoc.convert_text(
            markdown,
            "docx",
            format="md",
            extra_args=[],
            outputfile=str(docx_file_url),
            encoding="utf-8",
        )
        return True
    except Exception:
        Path(docx_file_url).unlink(missing_ok=True)
        celery_logger.error("convert_md_to_docx error", exc_info=True)
    return False


def test_latex_md_to_pdf():
    # 这个是线上含有数学公式的 md, 公式为 \( ... \), 不支持，替换成 $$ ... $$$ 了
    with open(
        r"/tmp/0aac7579-e263-e0d5-fd25-da84850387f1/down_f79a2a4f-33d1-4e1d-b7a8-b9eddf082166/第1讲 计算13.md", "r"
    ) as f:
        markdown = f.read()
    pdf_file_url = (
        "/tmp/0aac7579-e263-e0d5-fd25-da84850387f1/down_f79a2a4f-33d1-4e1d-b7a8-b9eddf082166/第1讲 计算13.pdf"
    )
    convert_md_to_pdf(markdown=markdown, pdf_file_url=pdf_file_url)


def test_nesting_md_to_pdf():
    with open(
        "/tmp/6d004285-2dc0-0c34-345c-d8da3cd02f06/down_68cfbaf9-261a-45d9-9f33-791f476b72ca/Neovim调试-高效使用-配置说明.md",
        "r",
    ) as f:
        markdown = f.read()

    pdf_file_url = "/tmp/6d004285-2dc0-0c34-345c-d8da3cd02f06/down_68cfbaf9-261a-45d9-9f33-791f476b72ca/Neovim调试-高效使用-配置说明.pdf"
    convert_md_to_pdf(markdown=markdown, pdf_file_url=pdf_file_url)


if __name__ == "__main__":
    # 这个代码只能在线上 debug
    # export PYTHONPATH=. && python3 src/util/pandoc_util.py
    test_latex_md_to_pdf()
    test_nesting_md_to_pdf()
