import base64
import hashlib


def encode_map_to_string(input_dict: dict) -> str:
    # Sort the keys based on their ASCII values
    sorted_keys = sorted(input_dict.keys())
    # Initialize an empty list to store the encoded key-value pairs
    encoded_pairs = []

    # Iterate through the sorted keys
    for key in sorted_keys:
        # Encode the value
        encoded_value = str(input_dict[key])
        # Append the key-value pair to the list
        encoded_pairs.append(f"{key}={encoded_value}")

    # Join the list into a single string with '&' separator
    result_string = "&".join(encoded_pairs)

    return result_string


def get_md5_hash(input_string: str, key: str) -> str:
    # Concatenate the input string with the key
    combined_string = input_string + key

    # Get the base64 encoded value of the combined string
    base64_encoded = base64.b64encode(combined_string.encode("utf-8")).decode("utf-8")

    # Create an MD5 hash object
    md5_hash = hashlib.md5()

    # Update the hash object with the base64 encoded string
    md5_hash.update(base64_encoded.encode("utf-8"))

    # Return the hexadecimal digest of the hash
    return md5_hash.hexdigest()


if __name__ == "__main__":
    # demo_json = {"ret_model": "md", "biz_id": "c39115aa-a227-3536-903e-49c113867111"}
    # demo_json = {
    #     "task_source": "6NKngaBRcv",
    #     "duration_minutes": 100,
    #     "name": "求婚完整版的来啦",
    #     "url": "https://www.bilibili.com/video/BV1CS411w746?p=1",
    #     "author": "甜一口没"
    # }
    # t = int(get_beijing_time().timestamp() * 1000)
    # print(t)
    # demo_json.update({"app_key": "6NKngaBRcv", "t": t})
    demo_json = {
        "task_source": "6NKngaBRcv",
        "duration_minutes": 1,
        "name": "test",
        "url": "https://file.lessonplan.cn/Transfer/CnuF35yF",
        "app_key": "6NKngaBRcv",
        "t": 1724222566516,
    }
    print(encode_map_to_string(demo_json))
    sign = get_md5_hash(encode_map_to_string(demo_json), "Ir0HvDU4YtZBkouw")
    print(sign)
