# -*- coding: utf-8 -*-
from typing import Dict

import requests
from openai import AsyncOpenAI, OpenAI
from zhipuai import ZhipuAI

from config import settings
from src.constant.prompt import translation_json_prompt
from src.logger.logUtil import get_logger

logger = get_logger(__name__)


class ZhipuLLM:
    @property
    def model(self):
        return ZhipuAI(api_key=settings.ZHIPU_API_KEY)

    def chat(self, prompt: Dict, json=True):
        params = {
            "model": settings.ZHIPU_MODEL,
            "messages": [{"role": "system", "content": prompt["system"]}, {"role": "user", "content": prompt["user"]}],
            "temperature": 0.1,
        }
        if json:
            params["response_format"] = {"type": "json_object"}
        response = self.model.chat.completions.create(**params)
        result = response.choices[0].message.content
        use_tokens = response.usage.total_tokens
        return result, use_tokens


class OpenAILLM:
    def __init__(self, name: str = "DeepSeek"):
        if name.lower() == "deepseek":
            self.base_url = settings.DEEPSEEK_BASE_URL
            self.api_key = settings.DEEPSEEK_API_KEY
            self.model_name = settings.DEEPSEEK_MODEL
        elif name.lower() == "chatgpt":
            self.base_url = settings.CHATGPT_BASE_URL
            self.api_key = settings.CHATGPT_API_KEY
            self.model_name = settings.CHATGPT_MODEL
        elif name.lower() == "kimi":
            self.base_url = settings.KIMI_BASE_URL
            self.api_key = settings.KIMI_API_KEY
            self.model_name = settings.KIMI_MODEL
        elif name.lower() == "zhipu":
            self.base_url = settings.ZHIPU_BASE_URL
            self.api_key = settings.ZHIPU_API_KEY
            self.model_name = settings.ZHIPU_MODEL
        elif name.lower() == "qwen":
            self.base_url = settings.QIANWEN_BASE_URL
            self.api_key = settings.QIANWEN_API_KEY
            self.model_name = settings.QIANWEN_MODEL
        elif name.lower() == "ppinfra":
            self.base_url = settings.PPINFRA_BASE_URL
            self.api_key = settings.PPINFRA_API_KEY
            self.model_name = settings.PPINFRA_MODEL
        elif name.lower() == "siliconflow":
            self.base_url = settings.SILICONFLOW_BASE_URL
            self.api_key = settings.SILICONFLOW_API_KEY
            self.model_name = settings.SILICONFLOW_MODEL
        elif name.lower() == "minimax":
            self.base_url = settings.MINIMAX_BASE_URL
            self.api_key = settings.MINIMAX_API_KEY
            self.model_name = settings.MINIMAX_MODEL
        elif name.lower() == "doubao":
            self.base_url = settings.DOUBAO_BASE_URL
            self.api_key = settings.DOUBAO_API_KEY
            self.model_name = settings.DOUBAO_MODEL
        else:
            raise ValueError("Invalid model name")

    @property
    def model(self):
        return OpenAI(base_url=self.base_url, api_key=self.api_key)

    def chat(self, prompt: Dict, json=True):
        params = {
            "model": self.model_name,
            "messages": [
                {"role": "system", "content": prompt["system"]},
                {"role": "user", "content": prompt["user"]},
            ],
            "temperature": 0.1,
            "max_tokens": 4096,
            "timeout": 5 * 60,
        }
        if json:
            params["response_format"] = {"type": "json_object"}
        response = self.model.chat.completions.create(**params)
        result = response.choices[0].message.content
        use_tokens = response.usage.total_tokens
        return result, use_tokens

    @property
    def async_model(self):
        return AsyncOpenAI(base_url=self.base_url, api_key=self.api_key)

    async def async_chat(self, prompt: Dict, json=True):
        params = {
            "model": self.model_name,
            "messages": [
                {"role": "system", "content": prompt["system"]},
                {"role": "user", "content": prompt["user"]},
            ],
            "temperature": 0.1,
            "max_tokens": 4096,
            "timeout": 5 * 60,
        }
        if json:
            params["response_format"] = {"type": "json_object"}
        response = await self.async_model.chat.completions.create(**params)
        result = response.choices[0].message.content
        use_tokens = response.usage.total_tokens
        return result, use_tokens


class SiliconCloudLLM:
    def __init__(self):
        self.base_url = settings.SILICONFLOW_BASE_URL

    def chat(self, prompt: Dict):
        payload = {
            "model": "deepseek-ai/deepseek-llm-67b-chat",
            "messages": [{"role": "system", "content": prompt["system"]}, {"role": "user", "content": prompt["user"]}],
            "stream": False,
            "max_tokens": 2048,
            "stop": ["string"],
            "temperature": 0.1,
        }
        headers = {
            "accept": "application/json",
            "content-type": "application/json",
            "authorization": "Bearer sk-xcdhlpsbaqndmnqaijuivunjauxybsgtomlyggtjgmavjdjj",
        }

        response = requests.post(self.base_url, json=payload, headers=headers)

        result = response.json()["choices"][0].get("message", {}).get("content", "")
        use_tokens = response.json()["usage"].get("total_tokens", 0)
        return result, use_tokens


default_llm = OpenAILLM("doubao")
candidate_llm = OpenAILLM(name="zhipu")


def _get_llm_result(response):
    response = response.replace("```markdown", "").replace("```", "")
    response = response.replace("```json", "").replace("```", "")
    response = response.replace("## Result", "").replace("## 结果", "").strip()
    return response


def llm_chat(prompt, default=default_llm, candidate=candidate_llm, candidate_llm_retry_times=3, json=False):
    llm_result, use_tokes, exists_risk = None, None, False
    try:
        llm_result, use_tokes = default.chat(prompt, json=json)
        llm_result = _get_llm_result(llm_result)
        return llm_result, use_tokes, exists_risk
    except Exception as e:
        logger.error(f"default llm failed with {str(e)}, try candidate llm", exc_info=True)
        exists_risk = True
        # raise CustomException("请重新检查视频内容是否合规！")
        while candidate_llm_retry_times > 0:
            try:
                llm_result, use_tokes = candidate.chat(prompt, json=json)
                llm_result = _get_llm_result(llm_result)
                if not llm_result.strip():
                    continue
                return llm_result, use_tokes, exists_risk
            except Exception as e:
                candidate_llm_retry_times -= 1
    if llm_result is None:
        logger.error("Failed to get modified speaker text", exc_info=True)
        return "", 0, True


async def async_llm_chat(prompt, default=default_llm, candidate=candidate_llm, candidate_llm_retry_times=3, json=False):
    llm_result, use_tokes, exists_risk = None, None, False
    try:
        llm_result, use_tokes = await default.async_chat(prompt, json=json)
        llm_result = _get_llm_result(llm_result)
        return llm_result, use_tokes, exists_risk
    except Exception as e:
        logger.error(f"default llm failed with {str(e)}, try candidate llm", exc_info=True)
        exists_risk = True
        # raise CustomException("请重新检查视频内容是否合规！")
        while candidate_llm_retry_times > 0:
            try:
                llm_result, use_tokes = await candidate.async_chat(prompt, json=json)
                llm_result = _get_llm_result(llm_result)
                if not llm_result.strip():
                    continue
                return llm_result, use_tokes, exists_risk
            except Exception as e:
                candidate_llm_retry_times -= 1
    if llm_result is None:
        logger.error("Failed to get modified speaker text", exc_info=True)
        return "", 0, True


if __name__ == "__main__":
    from dotenv import load_dotenv

    load_dotenv(verbose=True)
    llm = OpenAILLM()
    prompt = {
        "system": """# 角色：语音稿修复润色大师
用户正在为一段视频讲座配文字稿，首先会给你一段录音转的的文字稿，里面含有很多口语化的表达以及一些识别错误的词。另外用户还会给你一个语料库，含有本次任务可能涉及的专有名词。请你根据录音文字稿和语料库将录音文字稿改写为较为书面的表达，去掉口语化的重复句子、停顿词和识别错误的词，特别是专有名词和谐音词等，保持语句的连贯和简洁，保持原始录音稿的意思。

# 能力描述
1. **语言专家**：能根据演讲稿语言，将原始语音稿修复为最符合该语种习惯的表达方式。因为你很熟悉常见的口语表达与书面表达的区别，所以能非常准确地根据上下文与当前讲座话题修复口语表达的冗余或者错误的字、词、句。
2. **严谨听话**：你非常遵从如下“注意事项”中的内容，严格保证回复符合其要求
3. **动态领域专家**：你会根据输入先思考和判断现在润色的文字是属于什么领域的话题，那么你就是这个领域的专家，并且懂得这个领域的全部专有名词，对于原始语音稿中的谐音字且在上下文中明显转录错的文字，使用正确的词语替代。
4. **细心反思**：你在全部润色稿完成后会再从头到尾审视一边有没有错别字等不符合”注意事项“和其他要求的地方，如果有就会加以改正。
5. **排版高手**：对于较长的内容，注意适当分段

# 注意事项
1. 【非常重要】严格按照输出格式给出你的回答，不要有其他任何多余的内容！！！
2. 回答必须是简体中文，除非某些文字原本演讲者就说的是别的语言，如果是别的语言就保持写别的语言，不要受语料库的文字语言影响。
3. 语料库只作为专业名词识别来使用，用于修改原始录音稿中的专业名词等识别错误
4. 最高优先级遵从原始录音稿文字表达，润色稿保持语句的连贯和简洁，但一定要保证原有信息全部保留，且要保持原来的表达意思。

# 输出格式
## 结果
""",
        "user": """# 语料库
短期记忆\n\n. Prompt开发范式\n+ 从最简单的prom pt开始\n+ 根据模型的错误来进行优化\n* 避免“过拟合”，时常“重构“\n*区分精确问题与模糊问题\n+ Code copilot, EasyDict\n\n对话记录。请你根据用户的发言，来归纳用户的意图，例旭\n\nopenMMLab bilibili\n

# 原始录音文字稿
这个讲完这个三个原则之后呢我们也可以去看一下就是说我们其实平时开发就自己在开发 from的时候一般来做的一个遵权的模式会是什么样子的那首先可能我们会是一个从一个最简单的 problem 开始对吧就是虽然前面讲了你要把它当成一个新人对吧但是你仍然是我一般的习惯就不会是说一开始就把这个我的任务拆得非常非常细对吧就是可能是这个几千个字的背景介绍先完全一股脑先写上去的我一般还是会以一个非常简单的说你现在就要做一个什么样的任务这个一个很简单的 problem 先开始对那么有了开始之后接下来你会去看这个观察这个大原模型的一个返回它的返回如果不符合你的预期你在慢慢地去做调整的就是一个迭代的开发的一个形式对所以它会容易出现错误的点你再去补充一些信息对吧让它不断地去修正它那个结果其实也跟刚才讲的那个概率贪缩的是一个类似的意思就是说慢慢地寻找它到你想要的那个平行平行就那个世界中去对那么另外就是说还有一个比较重要的一点就是可能是不要过逆合的就是说网上有很多这种Prompt的技巧它可能就是突然有一个词或者是一句话就特别有效对吧就是说但是这个东西就是可能会有一个过逆合的问题就是说它可能再比如说GPT的GPT4的0314版本上表现很好对但是它模型升级了升到0613版本之后就发现突然也好像就不行了就变笨了对那这个时候就可能就是由于你的这个Prompt的写得太精细了就是你就完全去护了就过护了那个概率空间对吧就是说GPT4的0314版本它是在什么样的数据上训练的它就会说那句话就很容易达到一个很好的效果对吧但是到了0613就它的训练数据也变了它的参数量也变了它的可能比如说它的这个模型结构都变了就还能这样的话你可能就原来那个Prompt就可能就适合起码了对那甚至说你不一定是用GPT你可能又切换到了一个一些开源模型团那这个时候的话你的Prompt太复杂过逆合的话就可能会很容易出问题的对还有包括你可能要时常重构一下对就是说Prompt不是说一味的去增加这个内容对因为你每增加一个Token的Prompt它的这个模型的这个输出的速度都会变慢的对这个其实是有开销的所以说对于你来说的话其实也不是一味的去增加它对那么还要觉得比如说像可以去区分这个精确问题和模糊问题对吧对就是有时候你会发现模型会放一些错误但你会发现这个错误是可以通过一些精确的方法去修正的对就是它其实通过可以通过一个规则去修正的那你就不一定要在Prompt面说你不要犯这个错误的因为你在Prompt面加这个提示的话它仍然是个概率性的结构但如果你可以通过精确的去修正的话你就不需要仍然以一个概率的方式去解决这个问题对吧那么最后一条是我平时在开发的时候还蛮喜欢用的两个工具就一个是那个co-pilot就不知道大家用不用这个GitHubco-pilot对那么其实你在这个比如说下面这个截图里面你的代码文件里面去写Prompt的时候的话其实Prompt本身这个co-pilot也会帮你去做补权对吧那这个就比较有意思就是因为co-pilot就比如说你用的是GitHubco-pilot它背后用的模型的体系就是这个OpenEye的这个系列的模型对吧那如果你发现它的这个它自动补权出来的结果对吧就跟你想的方向是挺一致的话那就说明其实这个方向可能你不需要再花太多的额外的这个指令去告诉它了对吧对但是如果你发现它的补权出来的内容跟你想要的方向是正好是差得很远的那这个时候你要注意就再多加些信息让它引导到你想要的那个方向上去的对这个其实是挺好的一个就是GitHubco-pilot可以帮你来补充一些Prompt内容而且你可以观察一下就是模型现在的预测是什么样的你可以根据它这个预测来看是不是需要做调整对那么后面那个EasyDict的话是这样子就是因为我们现在会用很多绝大多数是一些GPT的或者说像那个Cloud的这些模型它可能对于英文的这个token或者英文的这个Prompt的这个效果会更好所以说我经常会把这个Prompt从中文翻译成中英文的所以说我会用EasyDict的这个工具快捷键换出然后写把这个中文的Prompt写进去然后它会自动去调各个平台的这个翻译其中也可能也包括OpenMate的翻译就是让我们回去综合一下去选一个表达比较好的一个英文翻译的去写进去这个其实也是对于平时做Prompt开发的时候还有蛮能做的体校的对那么再
""",
    }
    import json

    # translation_json_prompt["user"] = translation_json_prompt["user"].format(
    #     in_language="",
    #     out_language="Chinese",
    #     JSON_STR=json.dumps(
    #         {
    #             "hello": "I like china",
    #             "world": {"outline": "English, Chinese, Janpanes", "summary": "English is very important"},
    #         },
    #         indent=4,
    #     ),
    # )
    # result, use_tokens = llm.chat(translation_json_prompt)
    # print(result)

    JSON_STR = """{
"key_words_tags": ["初创企业", "加速器", "Y Combinator", "创业投资", "硅谷"],
"target_audience": ["初创企业创始人", "创业投资者", "对创业生态感兴趣的学生和研究人员"],
"one_sentence_summary": "本文详细介绍了Y Combinator（YC）加速器项目的内容、申请流程、成功案例及其对初创企业的重要价值。",
"takeaways": ["Y Combinator 是全球最大且最成功的初创企业加速器，已成功孵化超过2,000家公司，创造了超过60,000个工作岗位，并产生了超过$300 billion的价值。", "YC 的投资模式简单直接，通过标准化的在线申请流程来决定资助对象。\n- 申请YC的关键要素是拥有一个强大的创始团队和一个令人兴奋的创业想法，而非高收入或显著的市场牵引力。", "YC 不资助生活方式企业或副项目，强调公司必须有显著的成长潜力。", "申请YC时应专注于构建产品，而非过度准备申请材料，展示在申请和面试之间的实际进展非常重要。"],
"in_depth_qa": [["Y Combinator 的主要投资标准是什么？","主要标准包括一个强大的创始团队和一个有潜力的创业想法。"], ["申请YC时应该如何准备？", "应该专注于构建产品，展示在申请和面试之间的实际进展。"], ["Y Combinator 的投资金额和股权要求是多少？", "YC 投资$125,000以换取7%的股权。"], ["Y Combinator 的远程工作模式是如何运作的？", "自疫情以来，YC 已完全转向远程工作模式，不再要求创始团队搬迁到硅谷。"], ["如果申请YC失败，应该如何应对？", "应该继续专注于产品和团队建设，许多成功的创始人都是多次申请后才被接受的。"]],
"terminology_explanation": ["加速器 (Accelerator): 一种为初创企业提供资金、指导和资源的短期高强度项目。", "Y Combinator (YC): 一个著名的初创企业加速器，以投资和支持早期阶段的科技创业公司而闻名。", "股权 (Equity): 公司所有权的一部分，通常以股份形式表示。", "Demo Day: 初创企业在加速器项目结束时展示其产品和业务模式的活动，目的是吸引潜在投资者。", "Hacker House: 一种共享居住空间，通常由科技创业者和程序员使用，以降低生活成本并促进交流。"]
}"""
    translation_json_prompt["user"] = translation_json_prompt["user"].format(
        in_language="",
        out_language="English",
        JSON_STR=JSON_STR,
    )
    result, use_tokens = llm.chat(translation_json_prompt)
    xx = json.loads(result)
