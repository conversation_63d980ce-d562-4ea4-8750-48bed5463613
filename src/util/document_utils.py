import logging
import tempfile
from pathlib import Path
from typing import Optional, Union

import pypandoc

logger = logging.getLogger(__name__)


class DocumentConverter:
    def convert(
        self,
        content: Union[str, bytes],
        from_format: str = "html",
        to_format: str = "docx",
        file_path: str = "",
        extra_args: Optional[list] = None,
    ) -> bytes:
        """
        使用 pypandoc 转换文档格式

        Args:
            content: 要转换的内容
            from_format: 源格式 (html, markdown, etc.)
            to_format: 目标格式 (docx, pdf, etc.)
            extra_args: 额外的 pandoc 参数
        """
        try:
            # 确保内容是字符串
            if isinstance(content, bytes):
                content = content.decode("utf-8")

            # 创建临时文件
            with tempfile.TemporaryDirectory() as temp_dir:
                output_file = Path(temp_dir) / f"output.{to_format}"

                # 使用 pypandoc 转换
                pypandoc.convert_text(
                    content,
                    to_format,
                    format=from_format,
                    extra_args=extra_args or [],
                    outputfile=str(output_file),
                    encoding="utf-8",
                )

                # 读取生成的文件
                return output_file.read_bytes()

        except Exception as e:
            logger.exception("Document conversion failed")
            raise RuntimeError(f"Document conversion failed: {str(e)}")
