default_code = "readlecture"
# 微信登录认证
key_pc_wechat_access_token = "readlecture:pc-wechat-access-token"
key_app_wechat_access_token = "readlecture:app-wechat-access-token"
key_pc_wechat_access_token_by_code = f"readlecture:pc-wechat-access-token-by-code-{{code}}"
key_app_wechat_access_token_by_code = f"readlecture:app-wechat-access-token-by-code-{{code}}"

key_js_ticket = f"{default_code}:wechat-js-ticket-{{source}}"
# 微信绑定错误返回
key_wechat_bind_error = f"readlecture:wechat-bind-error-{{user_id}}"

# 微信认证事件
key_pc_wechat_auth = f"readlecture:pc-wechat-auth-{{category}}-{{state}}"
# 微信回调需要处理的缓存数据
key_pc_wechat_auth_notice = f"readlecture:pc-wechat-notice-{{category}}-{{state}}"

# 开放平台 key
key_open_user = f"readlecture:openapi:platform-{{biz_id}}"

# 验证码缓存 key
key_open_captcha = f"readlecture:openapi:captcha-{{username}}-{{typename}}"

# special 特供接口平台 任务 处理 锁
key_special_lock_task = f"readlecture:special:latest-task-{{task_id}}"
# source 原文任务 任务 处理 锁
key_source_lock_task = f"readlecture:source:latest-task-{{task_id}}"
# source 原文任务 任务 黑名单
key_source_task_block = f"readlecture:source:block-task"
# source 原文任务 任务 用户限流
key_source_task_limit = f"readlecture:source:limit-task-{{user_id}}"

# AI任务限流
key_ai_task_limit = f"readlecture:AI:limit-task-{{ai_type}}"

# 积分锁
key_points_lock = f"readlecture:lock:points-{{user_id}}"

# 原文任务扣减积分记录缓存
key_source_task_points = f"{default_code}:source-task-points-{{task_id}}"

# app 版本
key_app_lastest = f"{default_code}:app-lastest-{{platform}}"

# 智普任务状态
key_special_task_status = f"{default_code}:special-task-status-{{task_id}}"
key_special_task_caption = f"{default_code}:special-task-caption-{{task_id}}"

ttl_url_detail = 60 * 20
key_url_detail = f"{default_code}:url-detail-md5-{{md5_value}}"

## 笔记本相关
# 笔记本树
key_folder_tree = f"{default_code}:folder-tree-{{user_id}}"

## 文章相关
# 任务列表缓存
key_task_list_del = f"{default_code}:article-id-{{user_id}}"
key_task_list = f"{default_code}:article-id-{{user_id}}-{{folder_id}}-{{page_no}}-{{page_size}}-{{conditions}}"
ttl_task_list = 60 * 20

# 任务文章缓存
ttl_task_id = 60 * 60 * 24 * 7
key_task_id = f"{default_code}:article-id-{{user_id}}-{{task_id}}"

# 文章详细信息分页缓存
ttl_article_details_paginate = 60 * 60 * 24
key_article_details_all = f"{default_code}:details-page-{{user_id}}"
key_article_details_meta = f"{default_code}:details-page-{{user_id}}-meta-{{task_id}}"
key_article_details_paginate = f"{default_code}:details-page-{{user_id}}-{{task_id}}-{{page_no}}-{{page_size}}"

# 我的分享
ttl_share_paginate = 60 * 60 * 24 * 7
key_share_all = f"{default_code}:share-page-{{user_id}}"
key_share_paginate = f"{default_code}:share-page-{{user_id}}-{{page_no}}-{{page_size}}-{{conditions}}"
key_share_token = f"{default_code}:share-info-{{token}}"
key_share_id = f"{default_code}:share-info-{{task_id}}"

# 播客流缓存
key_audio_stream_lock = f"demo-audio-stream-lock-{{task_id}}"
key_audio_stream = f"demo-audio-stream-{{task_id}}"
