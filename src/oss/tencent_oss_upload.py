from config import settings
from src.logger.logUtil import get_logger
from src.oss.sts import Sts

logger = get_logger(__name__)


def get_credential():
    config = {
        # 请求URL，域名部分必须和domain保持一致
        "url": "https://sts.tencentcloudapi.com/",
        "domain": "sts.tencentcloudapi.com",
        # 临时密钥有效时长，单位是秒
        "duration_seconds": 1800,
        "secret_id": settings.COS_SECRET_ID,
        # 固定密钥
        "secret_key": settings.COS_SECRET_KEY,
        # 换成你的 bucket
        "bucket": "readlectur-private-1257176257",
        # 换成 bucket 所在地区
        "region": "ap-guangzhou",
        # 这里改成允许的路径前缀，可以根据自己网站的用户登录态判断允许上传的具体路径
        # 例子： a.jpg 或者 a/* 或者 * (使用通配符*存在重大安全风险, 请谨慎评估使用)
        "allow_prefix": ["video/*", "audio/*"],
        # 密钥的权限列表。简单上传和分片需要以下的权限，其他权限列表请看 https://cloud.tencent.com/document/product/436/31923
        "allow_actions": [
            # 简单上传
            "name/cos:PutObject",
            "name/cos:PostObject",
            # 分片上传
            "name/cos:InitiateMultipartUpload",
            "name/cos:ListMultipartUploads",
            "name/cos:ListParts",
            "name/cos:UploadPart",
            "name/cos:CompleteMultipartUpload",
        ],
        # 临时密钥生效条件，关于condition的详细设置规则和COS支持的condition类型可以参考 https://cloud.tencent.com/document/product/436/71306
        # "condition": {
        #     "ip_equal":{
        #         "qcs:ip":[
        #             "************/24",
        #             "************/24",
        #         ]
        #     }
        # }
    }

    try:
        sts = Sts(config)
        response = sts.get_credential()
        return {
            "sessionToken": response["credentials"]["sessionToken"],
            "tmpSecretId": response["credentials"]["tmpSecretId"],
            "tmpSecretKey": response["credentials"]["tmpSecretKey"],
            "expiredTime": response["expiredTime"],
            "startTime": response["startTime"],
        }
    except Exception:
        logger.exception("An error occurred while getting the credential.")


if __name__ == "__main__":
    print(get_credential())
