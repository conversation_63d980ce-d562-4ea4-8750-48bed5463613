import os
import time

from qiniu import Auth, put_file

from config import settings
from src.logger.logUtil import get_logger

logger = get_logger(__name__)
q = Auth(settings.QINIU_ACCESS_KEY, settings.QINIU_SECRET_KEY)


bucket_name = "read-lecture"


def get_upload_token():
    return q.upload_token(bucket_name)


def upload_img(file_path, biz_id, file_name):
    """
    上传图片
    file_path: 本地文件路径
    biz_id: 业务id
    file_name: 上传到七牛云的文件名
    """
    if not os.path.exists(file_path):
        logger.info(f"File {file_path} does not exist.")
        return

    token = get_upload_token()
    date = time.strftime("%Y%m%d", time.localtime())
    file_name = f"img/{date}/{biz_id}/{file_name}"

    try:
        ret, info = put_file(token, file_name, file_path, version="v2")
        if info.status_code == 200:
            return file_name
    except Exception as e:
        logger.exception(f"An error occurred while uploading the file: {e}")


def upload_zip(file_path, biz_id, file_name):
    """
    上传zip文件
    :param file_path: 本地文件路径
    :param biz_id: 业务id
    :param file_name: 上传到七牛云的文件名
    :return:
    """
    if not os.path.exists(file_path):
        logger.error(f"File {file_path} does not exist.")
        return

    token = get_upload_token()
    date = time.strftime("%Y%m%d", time.localtime())
    file_name = f"zip/{date}/{biz_id}/{file_name}"

    try:
        ret, info = put_file(token, file_name, file_path, version="v2")
        if info.status_code == 200:
            return file_name
    except Exception as e:
        logger.exception(f"An error occurred while uploading the file: {e}")


if __name__ == "__main__":
    print(get_upload_token())
    print(upload_img("test.png", 1234, "test.png"))
    print(upload_zip("test.zip", 1234, "test.zip"))
