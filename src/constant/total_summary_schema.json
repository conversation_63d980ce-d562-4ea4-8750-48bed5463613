{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"one_sentence_summary": {"type": "string"}, "takeaways": {"type": "array", "items": {"type": "string"}}, "in_depth_qa": {"type": "array", "items": {"type": "array", "items": [{"type": "string"}, {"type": "string"}], "minItems": 2, "maxItems": 2}}, "key_words_tags": {"type": "array", "items": {"type": "string"}}, "target_audience": {"type": "array", "items": {"type": "string"}}, "terminology_explanation": {"type": "array", "items": {"type": "string"}}}, "required": ["one_sentence_summary", "takeaways", "in_depth_qa", "key_words_tags", "target_audience", "terminology_explanation"], "additionalProperties": false}