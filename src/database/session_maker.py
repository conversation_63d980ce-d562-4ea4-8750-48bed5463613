"""
数据库会话
"""
from contextlib import asynccontextmanager
from typing import Any, AsyncGenerator

from sqlalchemy import URL
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine

from config import settings

DATABASE_URL = URL.create(
    'postgresql+asyncpg',
    host=settings.DB_HOST,
    port=settings.DB_PORT,
    username=settings.DB_USER,
    password=settings.DB_PASSWORD,
    database=settings.DB_NAME
)

print(DATABASE_URL)
engine = create_async_engine(
    DATABASE_URL,
    echo=False,  # 如果需要调试，可以设置为True
    pool_size=10,  # 连接池大小（可根据实际情况调整）
    max_overflow=20,  # 超过连接池大小外最多创建的连接数
    pool_pre_ping=True,  # 执行前检查连接是否有效
    pool_timeout=30,  # 连接池中没有线程可用时，在抛出异常前等待的时间
    pool_recycle=3600  # 多少秒之后对连接进行一次回收（重置）
)
AsyncSessionLocal = async_sessionmaker(
    engine,
    autocommit=False,
    autoflush=False,
    class_=AsyncSession,
    expire_on_commit=False
)


async def async_db() -> AsyncGenerator[AsyncSession | Any, Any]:
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()


@asynccontextmanager
async def get_db_manager():
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()
