from enum import Enum, IntEnum


class Md5Key(Enum):
    FILTER = "filter"
    URL = "url"
    TASK = "task_source"
    CONDITIONS = "conditions"


class SourceEnum(Enum):
    """来源平台"""
    PC = 'pc'
    H5 = 'h5'
    APP_IOS = 'ios'
    APP_ANDROID = 'android'
    OPENAPI = 'openapi'
    SPECIAL = 'special'  # 特供接口
    LENOVO = 'lenovo'  # 联想
    EXCHANGE = 'exchange'
    UNKNOWN = 'unknown'  # 未知来源


class SpecialTaskCategoryEnum(Enum):
    """特供任务 类型"""
    MARKDOWN = "markdown"


class UrlSourceEnum(Enum):
    """URL解析支持"""
    YOUTUBE = "youtube"
    BILI = "bilibili"
    XYZ = "xiaoyuzhou"  # 小宇宙
    XHS = "xiaohongshu"  # 小红书
    DOUYIN = "douyin"
    KUS = "kuaishou"  # 快手
    BAIDUPAN = "baidupan"
    ALIPAN = "alipan"


class TaskStatusEnum(Enum):
    """任务状态"""
    FINISHED = 'finished'  # 忽略不用 后期统一优化保留数据查询使用
    FINISH = 'finish'  # 完成
    WAITING = 'waiting'  # 等待
    FAILED = 'fail'  # 失败
    PROCESSING = 'processing'  # 进行中
    CONFIRM = 'confirm'  # 已确认

    INVALID = "invalid"  # 无效
    DELETED = "deleted"  # 删除


class ApprovalStatusEnum(Enum):
    """审批状态"""
    DRAFT = "draft"  # 草稿
    PENDING = "pending"  # 审批中
    APPROVED = "approved"  # 已通过
    REJECTED = "rejected"  # 已拒绝
    CANCELED = "canceled"  # 已取消


class PlatformLevelEnum(Enum):
    ADMIN = ("ADMIN", "管理员", 1000)
    TREASURER = ("TREASURER", "财务", 600)
    EMPLOYEE = ("EMPLOYEE", "内部员工", 500)
    ENTERPRISE = ("ENTERPRISE", "企业", 300)
    PERSON = ("PERSON", "个人", 300)

    def __init__(self, mark, message, code):
        self.mark = mark
        self.message = message
        self.code = code

    @staticmethod
    def get_code(mark):
        for item in PlatformLevelEnum:
            if mark == item.mark:
                return item.code
        return None


class AffiliatesStatusEnum(IntEnum):
    """
    Affiliates status, 0 pending, 1 success, 2 fail
    its status is pending when it is created,
    its status is success when admin audit it and pass,
    its status is fail when admin audit it and not pass
    :see src.api.affiliates_api.audit_affiliates
     0 审核中 1 审核通过 2 审核不通过
    """

    PENDING = 0
    SUCCESS = 1
    FAIL = 2


class ShareTypeEnum(Enum):
    """分享类型"""
    PUBLIC = "public"  # 开放 （任何用户）
    PRIVATE = "private"  # 私有（只分享给指定的用户）



class MarketPlatTypeEnum(IntEnum):
    SYSTEM = 1   # 系统
    PLATFORM = 2  # 平台
    CHANNEL = 3  # 渠道


class TrackChannelEnum(Enum):
    """推广渠道"""
    INVITE = "invite"  # 邀请码分享
    SHARED = "shared"  # 文章分享
