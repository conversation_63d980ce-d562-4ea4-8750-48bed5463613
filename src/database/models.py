from datetime import datetime
from decimal import Decimal
from typing import List

from sqlalchemy import (
    String, Text, DateTime, BOOLEAN, Integer, TIME, DECIMAL,
    UniqueConstraint
)
from sqlalchemy.dialects.postgresql import JSON, FLOAT, DATE, SMALLINT
from sqlalchemy.orm import mapped_column, Mapped, relationship
from sqlalchemy.orm import validates

from src.database import curd
from src.database import enums
from src.util import stringify
from src.util.dateUtil import get_current_time_in_beijing
from src.util.pandoc_util import convert_latex_in_markdown
from sqlalchemy.ext.associationproxy import association_proxy


class BaseModel(curd.Base):
    """
    通用字段
    """
    __abstract__ = True

    def to_dict(self, remove_col: list[str] = None):
        if remove_col is None:
            remove_col = []
        data_dict = dict()
        for c in self.__table__.columns:
            if c.name not in remove_col:
                data_dict[c.name] = getattr(self, c.name)
        return data_dict

    def __init__(self, **kwargs):
        # 只保留模型定义的字段
        valid_keys = {column.name for column in self.__table__.columns}
        filtered_kwargs = {k: v for k, v in kwargs.items() if k in valid_keys}
        super().__init__(**filtered_kwargs)


class AffiliatesModel(BaseModel):
    """
    粉丝邀请码
    """
    __tablename__ = "affiliates"
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, doc='唯一标识')
    biz_id: Mapped[str] = mapped_column(String(128), unique=True)
    contact: Mapped[str] = mapped_column(String(255), doc="联系方式")
    user_id: Mapped[str] = mapped_column(String(128), default="SystemCreate",
                                         doc="用户id，可能是用户id，也可能是系统创建的")
    status: Mapped[int] = mapped_column(SMALLINT, default=enums.AffiliatesStatusEnum.PENDING, doc='状态')
    bind_token: Mapped[str] = mapped_column(String(255), default=stringify.get_str_key)  # 绑定token
    fans_count: Mapped[int] = mapped_column(Integer, default=0, doc="粉丝数量")
    total_point: Mapped[int] = mapped_column(Integer, default=0, doc="总积分")
    remark = mapped_column(JSON, nullable=True)
    create_time = mapped_column(DateTime, nullable=True, default=get_current_time_in_beijing)
    update_time = mapped_column(DateTime, nullable=True, default=get_current_time_in_beijing)
    delete_flag: Mapped[int] = mapped_column(Integer, default=0)


class AffiliatesFansModel(BaseModel):
    """
    粉丝
    """
    __tablename__ = "affiliates_fans"
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, doc='唯一标识')
    biz_id: Mapped[str] = mapped_column(String(255), unique=True)
    name: Mapped[str] = mapped_column(String(255), nullable=True)
    affiliates_id: Mapped[str] = mapped_column(String(255), doc="原邀请Id，现兼容分享Id")
    user_id: Mapped[str] = mapped_column(String(255), default="system")
    bind_time = mapped_column(DateTime, nullable=True, default=get_current_time_in_beijing)
    bind_status: Mapped[int] = mapped_column(SMALLINT, default=0, doc="0 临时绑定 1 永久绑定")
    channel: Mapped[str] = mapped_column(String(20), nullable=True, doc="渠道")
    point: Mapped[int] = mapped_column(Integer, nullable=True, doc="奖励积分")
    create_time = mapped_column(DateTime, nullable=True, default=get_current_time_in_beijing)
    update_time = mapped_column(DateTime, nullable=True, default=get_current_time_in_beijing)
    delete_flag: Mapped[int] = mapped_column(Integer, default=0)


class AffiliatesOrderModel(BaseModel):
    """
    粉丝订单
    """
    __tablename__ = "affiliates_order"
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, doc='唯一标识')
    biz_id: Mapped[str] = mapped_column(String(128), unique=True)
    order_id: Mapped[str] = mapped_column(String(255), nullable=True, doc="支付订单id")
    product_id: Mapped[int] = mapped_column(Integer, nullable=True, doc="产品id")
    affiliates_id: Mapped[str] = mapped_column(String(255), doc="分销商id")
    fans_id: Mapped[str] = mapped_column(String(255), doc="粉丝id")
    fans_name: Mapped[str] = mapped_column(String(64), nullable=True, doc="粉丝名称")
    pay_amount = mapped_column(DECIMAL(10, 2), default=Decimal("0.00"), doc="支付金额")
    affiliates_goods_type: Mapped[int] = mapped_column(SMALLINT, default=1, doc="分销商品类型 1积分 2 金额")
    order_point: Mapped[int] = mapped_column(Integer, nullable=True, doc="订单积分")
    order_status: Mapped[int] = mapped_column(SMALLINT, default=1, doc="订单状态 1 未获得 2 已获得 3 已退款")
    audit_status: Mapped[int] = mapped_column(SMALLINT, default=1, doc="审核状态 1 未审核 2 已审核")
    create_time = mapped_column(DateTime, nullable=True, default=get_current_time_in_beijing)
    update_time = mapped_column(DateTime, nullable=True, default=get_current_time_in_beijing)
    delete_flag: Mapped[int] = mapped_column(Integer, default=0)


class UserPOModel(BaseModel):
    """用户表"""
    __tablename__ = 'lecture_user'
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, doc='唯一标识')
    biz_id: Mapped[str] = mapped_column(String(128), unique=True)
    name: Mapped[str] = mapped_column(String(80), nullable=True)
    password: Mapped[str] = mapped_column(String(128), nullable=True)
    email: Mapped[str] = mapped_column(String(80), nullable=True)
    mobile: Mapped[str] = mapped_column(String(20), nullable=True)
    wx_open_id: Mapped[str] = mapped_column(String(32), nullable=True)
    wx_public_access: Mapped[bool] = mapped_column(BOOLEAN, default=False)
    mail_access: Mapped[bool] = mapped_column(BOOLEAN, default=False)
    google_open_id: Mapped[str] = mapped_column(String(32))
    create_time = mapped_column(DateTime, nullable=True, default=datetime.now)
    update_time = mapped_column(DateTime, nullable=True, default=datetime.now)
    delete_flag: Mapped[int] = mapped_column(Integer, default=0)
    head_img: Mapped[str] = mapped_column(String(2048), nullable=True)
    extra_info: Mapped[str] = mapped_column(Text, nullable=True)  # 新增的字段，用于存储JSON格式的数据
    deletion_request_time = mapped_column(DateTime, nullable=True, default=datetime.now)  # 新增待注销申请时间
    wx_unionid: Mapped[str] = mapped_column(String(128), nullable=True)
    source: Mapped[str] = mapped_column(String(20), nullable=True, doc="用户来源")


class UserRoleModel(BaseModel):
    """用户角色"""
    __tablename__ = 'lecture_user_roles'
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, doc='唯一标识')
    user_id: Mapped[str] = mapped_column(String(128))
    role_id: Mapped[str] = mapped_column(String(128))
    create_time = mapped_column(DateTime, nullable=True, default=datetime.now)
    update_time = mapped_column(DateTime, nullable=True, default=datetime.now)
    create_by: Mapped[str] = mapped_column(String(128), nullable=True)
    update_by: Mapped[str] = mapped_column(String(128), nullable=True)
    delete_flag: Mapped[int] = mapped_column(Integer, default=0)
    role: Mapped['RoleModel'] = relationship(
        'RoleModel',
        primaryjoin="RoleModel.biz_id==UserRoleModel.role_id",
        foreign_keys="[UserRoleModel.role_id]",
        uselist=False
    )


class RoleModel(BaseModel):
    """角色表"""
    __tablename__ = 'roles'

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, doc='唯一标识')
    biz_id: Mapped[str] = mapped_column(String(128), unique=True)
    name: Mapped[str] = mapped_column(String(50), unique=True, comment="角色名称")
    label: Mapped[str] = mapped_column(String(50), unique=True, comment="角色标识")
    permissions = mapped_column(JSON, nullable=True)
    create_time = mapped_column(DateTime, nullable=True, default=datetime.now)
    update_time = mapped_column(DateTime, nullable=True, default=datetime.now)
    create_by: Mapped[str] = mapped_column(String(128), nullable=True)
    update_by: Mapped[str] = mapped_column(String(128), nullable=True)
    delete_flag: Mapped[int] = mapped_column(Integer, default=0)


class PermissionModel(BaseModel):
    """权限表"""
    __tablename__ = 'permissions'
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, doc='唯一标识')
    name: Mapped[str] = mapped_column(String(30), unique=True, doc='权限名称')
    label: Mapped[str] = mapped_column(String(50), unique=True, doc='权限标识')


class LectureMemberModel(BaseModel):
    """
    会员表，包含会员信息和状态
    """
    __tablename__ = 'lecture_member'

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, doc='唯一标识')
    user_id: Mapped[str] = mapped_column(String(128))
    membership_type: Mapped[int] = mapped_column(Integer, default=0)  # 会员类型（0=免费，1=普通，2=VIP
    start_date = mapped_column(DATE)  # 会员资格开始日期
    end_date = mapped_column(DATE)  # 会员资格结束日期
    reset_date = mapped_column(DATE)  # 重置日期
    extension_date = mapped_column(DATE)  # 延期日期
    remaining_unlock: Mapped[int] = mapped_column(Integer)  # 会员剩余的分钟数
    points: Mapped[int] = mapped_column(Integer)  # 会员的积分
    sub_id: Mapped[str] = mapped_column(String(64), nullable=True)  # paddle订阅id
    created_at = mapped_column(DateTime, nullable=True, default=datetime.now)  # 创建时间
    updated_at = mapped_column(DateTime, nullable=True, default=datetime.now)  # 更新时间
    delete_flag: Mapped[int] = mapped_column(Integer, default=0)  # 删除标志
    level: Mapped[int] = mapped_column(Integer, default=0)


class LectureUserActionRecordModel(BaseModel):
    """
    用户积分操作表
    """
    __tablename__ = "lecture_user_point_record"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, doc='唯一标识')
    biz_id: Mapped[str] = mapped_column(String(128), nullable=True)
    user_id: Mapped[str] = mapped_column(String(128))
    action_relate_id: Mapped[str] = mapped_column(String(256), nullable=True)  # 关联操作的业务id ,如订单id 和任务id或者充值记录id，用来做回滚操作。
    user_action_type: Mapped[int] = mapped_column(Integer)  # 积分操作类型
    process_status: Mapped[int] = mapped_column(Integer)  # 积分操作状态
    process_remark: Mapped[str] = mapped_column(String(2048), nullable=True)  # 积分操作备注
    points: Mapped[int] = mapped_column(Integer)  # 数量
    created_at = mapped_column(DateTime, nullable=True, default=datetime.now)  # 创建时间
    updated_at = mapped_column(DateTime, nullable=True, default=datetime.now)  # 更新时间
    delete_flag: Mapped[int] = mapped_column(Integer, default=0)  # 删除标志
    rollback_id: Mapped[int] = mapped_column(Integer, default=0)

    @validates("user_action_type")
    def validate_user_action_type(self, key, user_action_type):
        if not (1 <= user_action_type <= 9):
            raise ValueError("user_action_type must be between 1 and 9")
        return user_action_type

    @validates("process_status")
    def validate_process_status(self, key, process_status):
        if not (1 <= process_status <= 9):
            raise ValueError("process_status must be between 1 and 2")
        return process_status


class FolderModel(BaseModel):
    """文件夹"""
    __tablename__ = "folder"
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, doc='唯一标识')
    biz_id: Mapped[str] = mapped_column(String(128))
    name: Mapped[str] = mapped_column(String(255))  # 文件夹名称
    parent_id: Mapped[int] = mapped_column(Integer, nullable=True)
    user_id: Mapped[str] = mapped_column(String(128), nullable=True)  # 文件夹所属用户
    depth: Mapped[int] = mapped_column(Integer, default=1)  # 文件夹层级
    total: Mapped[int] = mapped_column(Integer, default=0)  # 该文件夹下的子文件夹及任务的数量
    created_at = mapped_column(DateTime, nullable=True, default=get_current_time_in_beijing)  # 创建时间
    updated_at = mapped_column(DateTime, nullable=True, default=get_current_time_in_beijing)  # 更新时间
    delete_flag: Mapped[int] = mapped_column(Integer, default=0)

    # 自引用关系
    children: Mapped[List['FolderModel']] = relationship(
        'FolderModel',
        primaryjoin="FolderModel.parent_id==FolderModel.id",
        foreign_keys="[FolderModel.parent_id]"
    )

    tasks: Mapped[List['LectureTaskModel']] = relationship(
        'LectureTaskModel',
        primaryjoin="LectureTaskModel.folder_id==FolderModel.id",
        foreign_keys="[LectureTaskModel.folder_id]",
        order_by="LectureTaskModel.id.desc()",
        # lazy='dynamic'
    )


class LectureUrlModel(BaseModel):
    """url解析详情"""
    __tablename__ = "lecture_url_detail"
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, doc='唯一标识')
    biz_id: Mapped[str] = mapped_column(String(128))
    origin_url: Mapped[str] = mapped_column(String(1024), nullable=True)  # 目标url
    source_url: Mapped[str] = mapped_column(String(4096))  # 源url
    title: Mapped[str] = mapped_column(String(1024), nullable=True)  # 标题
    author_name: Mapped[str] = mapped_column(String(255), nullable=True)  # 作者名
    author_img: Mapped[str] = mapped_column(String(1024), nullable=True)  # 作者头像
    pic_path: Mapped[str] = mapped_column(String(2048), nullable=True)  # 图片路径
    duration_minutes: Mapped[int] = mapped_column(Integer, default=0)  # 视频时长
    task_source: Mapped[str] = mapped_column(String(255), nullable=True)  # 视频来源
    tags: Mapped[str] = mapped_column(String(1024), nullable=True)  # 标签
    intro: Mapped[str] = mapped_column(String(4096), nullable=True)  # 简介
    remark: Mapped[str] = mapped_column(String(255), nullable=True)
    extra_params: Mapped[str] = mapped_column(String(1024), nullable=True)  # 额外参数
    create_time = mapped_column(DateTime, nullable=True, default=get_current_time_in_beijing)
    update_time = mapped_column(DateTime, nullable=True, default=get_current_time_in_beijing)
    delete_flag: Mapped[int] = mapped_column(Integer, default=0)


class LectureTaskModel(BaseModel):
    """原文任务"""
    __tablename__ = 'lecture_tasks'

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, doc='唯一标识')
    biz_id: Mapped[str] = mapped_column(String(128), unique=True)

    name: Mapped[str] = mapped_column(String(2048), nullable=True)
    url: Mapped[str] = mapped_column(String(2048), nullable=True)
    duration_minutes: Mapped[int] = mapped_column(Integer, default=0)
    task_source: Mapped[str] = mapped_column(String(32), nullable=True)
    start_time = mapped_column(DateTime, nullable=True, default=datetime.now)
    end_time = mapped_column(DateTime, nullable=True)
    status: Mapped[str] = mapped_column(String(20), default=enums.TaskStatusEnum.CONFIRM.value)
    process_percent: Mapped[int] = mapped_column(Integer, nullable=True)
    token: Mapped[str] = mapped_column(String(10), nullable=True)
    user_id: Mapped[str] = mapped_column(String(128), nullable=True)
    open_platform_id: Mapped[str] = mapped_column(String(128), nullable=True)
    remark: Mapped[str] = mapped_column(String(512), nullable=True)
    author: Mapped[str] = mapped_column(String(256), nullable=True)
    order: Mapped[int] = mapped_column(Integer, default=0)
    delete_flag: Mapped[int] = mapped_column(Integer, default=0)
    article_biz_id: Mapped[str] = mapped_column(String(128), nullable=True)
    url_biz_id: Mapped[str] = mapped_column(String(128), nullable=True)
    img_url: Mapped[str] = mapped_column(String(1024), nullable=True)
    ctrl_params = mapped_column(JSON, nullable=True)
    md5: Mapped[str] = mapped_column(String(64), nullable=True)
    folder_id: Mapped[int] = mapped_column(Integer, nullable=True)
    point_id: Mapped[str] = mapped_column(String(128), nullable=True)
    create_time = mapped_column(DateTime, nullable=True, default=get_current_time_in_beijing)
    update_time = mapped_column(DateTime, nullable=True, default=get_current_time_in_beijing)
    retry_count: Mapped[int] = mapped_column(SMALLINT, default=0)
    article: Mapped['LectureArticleModel'] = relationship(
        'LectureArticleModel',
        primaryjoin="LectureArticleModel.biz_id==LectureTaskModel.article_biz_id",
        foreign_keys="[LectureTaskModel.article_biz_id]",
        uselist=False
    )
    point_record: Mapped['LectureUserActionRecordModel'] = relationship(
        'LectureUserActionRecordModel',
        primaryjoin="LectureUserActionRecordModel.biz_id==LectureTaskModel.point_id",
        foreign_keys="[LectureTaskModel.point_id]",
        uselist=False
    )

    def __init__(self, **kwargs):
        article = kwargs.pop('article', {})
        super().__init__(**kwargs)
        if article:
            self.article = LectureArticleModel(**article)


class LectureArticleModel(BaseModel):
    """文章"""
    __tablename__ = 'lecture_article'

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, doc='唯一标识')
    biz_id: Mapped[str] = mapped_column(String(128), unique=True)
    name: Mapped[str] = mapped_column(String(2048), nullable=True)
    language: Mapped[str] = mapped_column(String(32), default="zh")
    author: Mapped[str] = mapped_column(String(255), nullable=True, doc="作者")
    article_source: Mapped[str] = mapped_column(String(32), nullable=True)
    video_url: Mapped[str] = mapped_column(String(4096), nullable=True)
    img_url: Mapped[str] = mapped_column(String(2048), nullable=True)
    out_language: Mapped[str] = mapped_column(String(32), default="zh")

    translated_outline_json = mapped_column(JSON, nullable=True)
    podcast_transcript_json = mapped_column(JSON, nullable=True)  # 播客输出内容

    translated_total_summary_json = mapped_column(JSON, nullable=True)
    corpus: Mapped[str] = mapped_column(Text, nullable=True)
    audio_url: Mapped[str] = mapped_column(String(2048), nullable=True)
    podcast_duration: Mapped[str] = mapped_column(FLOAT, nullable=True, default=0.0)
    oss_pdf_url: Mapped[str] = mapped_column(String(4096), nullable=True)
    oss_word_url: Mapped[str] = mapped_column(String(2048), nullable=True)
    create_time = mapped_column(DateTime, nullable=True, default=datetime.now)
    update_time = mapped_column(DateTime, nullable=True, default=datetime.now)
    delete_flag: Mapped[int] = mapped_column(Integer, default=0)

    details: Mapped[List['LectureArticleDetailModel']] = relationship(
        'LectureArticleDetailModel',
        primaryjoin="LectureArticleDetailModel.article_id==LectureArticleModel.biz_id",
        foreign_keys="[LectureArticleDetailModel.article_id]",
        back_populates="article"
    )

    def __init__(self, **kwargs):
        details = kwargs.pop('details', [])
        super().__init__(**kwargs)
        if details:
            self.details = [LectureArticleDetailModel(**c) for c in details]


class LectureArticleDetailModel(BaseModel):
    """文章内容"""
    __tablename__ = 'lecture_article_detail'

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, doc='唯一标识')
    biz_id: Mapped[str] = mapped_column(String(128), unique=True)
    article_id: Mapped[str] = mapped_column(String(128))
    user_id: Mapped[str] = mapped_column(String(128), default="system")

    index: Mapped[int] = mapped_column(Integer, nullable=True)

    start_time = mapped_column(TIME, default="00:00:00")
    end_time = mapped_column(TIME, default="23:59:59")

    origin_text: Mapped[str] = mapped_column(Text, nullable=True)
    speaker: Mapped[str] = mapped_column(String(256), nullable=True)

    translated_modified_text_json = mapped_column(JSON, nullable=True)

    oss_pic_path: Mapped[str] = mapped_column(Text, nullable=True)
    local_pic_path: Mapped[str] = mapped_column(Text, nullable=True)
    pic_keywords: Mapped[str] = mapped_column(Text, nullable=True)
    create_time = mapped_column(DateTime, nullable=True, default=datetime.now)
    update_time = mapped_column(DateTime, nullable=True, default=datetime.now)
    delete_flag: Mapped[int] = mapped_column(Integer, default=0)

    records: Mapped[List['LectureArticleRecordModel']] = relationship(
        'LectureArticleRecordModel',
        primaryjoin="LectureArticleRecordModel.article_detail_id==LectureArticleDetailModel.biz_id",
        foreign_keys="[LectureArticleRecordModel.article_detail_id]"
    )
    article: Mapped['LectureArticleModel'] = relationship(
        'LectureArticleModel',
        primaryjoin="LectureArticleModel.biz_id==LectureArticleDetailModel.article_id",
        foreign_keys="[LectureArticleDetailModel.article_id]",
        uselist=False,
        back_populates="details"
    )

    def to_vo(self, out_language, language):
        return {
            "biz_id": self.biz_id,
            "index": self.index,
            "start_time": self.start_time.strftime(stringify.ONLY_TIME),
            "end_time": self.end_time.strftime(stringify.ONLY_TIME),
            # 用户选择语言未润色的 text, 这里取值为 origin_text 为命名问题
            "out_language_origin_text": convert_latex_in_markdown(self.origin_text),
            "out_language_modified_text": convert_latex_in_markdown(
                (self.translated_modified_text_json or {}).get(out_language) or ""
            ),
            "modified_text": convert_latex_in_markdown((self.translated_modified_text_json or {}).get(language) or ""),
            "speaker": self.speaker,
            "oss_pic_path": self.oss_pic_path,
        }


class LectureArticleRecordModel(BaseModel):
    """
    文章划线部分记录
    """
    __tablename__ = 'lecture_article_record'

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, doc='唯一标识')
    biz_id: Mapped[str] = mapped_column(String(150), unique=True)
    article_detail_id: Mapped[str] = mapped_column(String(150))
    type_name: Mapped[str] = mapped_column(String(10))
    user_id: Mapped[str] = mapped_column(String(150))
    record_id: Mapped[str] = mapped_column(String(150))
    context_json = mapped_column(JSON, nullable=True)
    create_time = mapped_column(DateTime, nullable=True, default=datetime.now)
    update_time = mapped_column(DateTime, nullable=True, default=datetime.now)
    delete_flag: Mapped[int] = mapped_column(Integer, default=0)


class LectureAITaskModel(BaseModel):
    """Ai 任务"""
    __tablename__ = 'lecture_ai_tasks'

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, doc='唯一标识')
    biz_id: Mapped[str] = mapped_column(String(128), unique=True)

    user_id: Mapped[str] = mapped_column(String(128), nullable=True)
    task_type: Mapped[int] = mapped_column(Integer, default=0)
    name: Mapped[str] = mapped_column(String(255), nullable=True)
    task_source: Mapped[str] = mapped_column(String(32), nullable=True)
    start_time = mapped_column(DateTime, nullable=True, default=get_current_time_in_beijing)
    end_time = mapped_column(DateTime, nullable=True)
    status: Mapped[str] = mapped_column(String(20), default=enums.TaskStatusEnum.CONFIRM.value)
    process_percent: Mapped[int] = mapped_column(Integer, nullable=True)
    article_biz_id: Mapped[str] = mapped_column(String(128), nullable=True)
    ai_detail_id: Mapped[str] = mapped_column(String(128), nullable=True)
    remark: Mapped[str] = mapped_column(String(512), nullable=True)
    order: Mapped[int] = mapped_column(Integer, default=0)
    delete_flag: Mapped[int] = mapped_column(Integer, default=0)
    ctrl_params = mapped_column(JSON, nullable=True)


class PlatformUserModel(BaseModel):
    """开放平台--用户信息"""
    __tablename__ = 'open_platform_user'

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, doc='唯一标识')
    biz_id: Mapped[str] = mapped_column(String(128), unique=True)

    name: Mapped[str] = mapped_column(String(100), nullable=True)
    daily_limit: Mapped[int] = mapped_column(Integer, default=1000)
    points: Mapped[int] = mapped_column(Integer, default=0)

    create_time = mapped_column(DateTime, nullable=True, default=get_current_time_in_beijing)  # 创建时间
    update_time = mapped_column(DateTime, nullable=True, default=get_current_time_in_beijing)  # 更新时间

    delete_flag: Mapped[int] = mapped_column(Integer, default=0)

    password: Mapped[str] = mapped_column(String(100), nullable=True)
    email: Mapped[str] = mapped_column(String(100), nullable=True)
    mobile: Mapped[str] = mapped_column(String(20), nullable=True)

    mail_cert: Mapped[bool] = mapped_column(BOOLEAN, default=False)
    # 用户等级区分外部内部用户
    level: Mapped[int] = mapped_column(Integer, default=300)
    # 会员等级 备用会员等级区分
    member_level: Mapped[int] = mapped_column(Integer, default=0)

    head_img: Mapped[str] = mapped_column(Text, nullable=True)
    enterprise_cert: Mapped[bool] = mapped_column(BOOLEAN, default=False)  # 企业认证
    extra_info = mapped_column(JSON, nullable=True)  # 用于存储JSON格式的数据

    create_by: Mapped[str] = mapped_column(String(128), nullable=True)
    update_by: Mapped[str] = mapped_column(String(128), nullable=True)

    platform_keys: Mapped[List['PlatformKeyModel']] = relationship(
        'PlatformKeyModel',
        primaryjoin="PlatformKeyModel.user_id==PlatformUserModel.biz_id",
        foreign_keys="[PlatformKeyModel.user_id]",
        back_populates="user"
    )
    folders: Mapped[List['FolderModel']] = relationship(
        'FolderModel',
        primaryjoin="FolderModel.user_id==PlatformUserModel.biz_id",
        foreign_keys="[FolderModel.user_id]"
    )


class PlatformKeyModel(BaseModel):
    """开放平台 key"""
    __tablename__ = 'open_platform'

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, doc='唯一标识')
    biz_id: Mapped[str] = mapped_column(String(128), unique=True)
    name: Mapped[str] = mapped_column(String(255))
    app_key: Mapped[str] = mapped_column(String(255))
    secret_key: Mapped[str] = mapped_column(String(255))
    daily_limit: Mapped[int] = mapped_column(Integer, default=1000)
    points: Mapped[int] = mapped_column(Integer, default=0)

    user_id: Mapped[str] = mapped_column(String(128), nullable=True)
    create_by: Mapped[str] = mapped_column(String(128), nullable=True)
    update_by: Mapped[str] = mapped_column(String(128), nullable=True)

    create_time = mapped_column(DateTime, nullable=True, default=get_current_time_in_beijing)
    update_time = mapped_column(DateTime, nullable=True, default=get_current_time_in_beijing)
    delete_flag: Mapped[int] = mapped_column(Integer, default=0)

    user: Mapped[PlatformUserModel] = relationship(
        'PlatformUserModel',
        primaryjoin="PlatformKeyModel.user_id==PlatformUserModel.biz_id",
        foreign_keys="[PlatformKeyModel.user_id]",
        back_populates="platform_keys"
    )

    def to_vo(self):
        return {
            "biz_id": self.biz_id,
            "create_time": self.create_time,
            "update_time": self.update_time,
            "name": self.name,
            "app_key": self.app_key,
            "secret_key": self.secret_key,
            "user_id": self.user_id
        }


class ApprovalConfigModel(BaseModel):
    """审批配置表"""
    __tablename__ = 'approval_configs'

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, doc='唯一标识')
    biz_id: Mapped[str] = mapped_column(String(128), unique=True)
    request_type: Mapped[str] = mapped_column(String(50), nullable=False, unique=True, doc='配置类型')
    name: Mapped[str] = mapped_column(String(100), nullable=False, doc='配置名称')
    steps = mapped_column(JSON, nullable=False, doc='审批步骤配置(JSON格式)')
    is_active: Mapped[bool] = mapped_column(BOOLEAN, default=True, doc="是否激活")
    create_time = mapped_column(DateTime, nullable=True, default=get_current_time_in_beijing)
    update_time = mapped_column(DateTime, nullable=True, default=get_current_time_in_beijing)
    delete_flag: Mapped[int] = mapped_column(Integer, default=0)


class ApprovalRequestModel(BaseModel):
    """审批请求表"""
    __tablename__ = 'approval_requests'

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, doc='唯一标识')
    biz_id: Mapped[str] = mapped_column(String(128), unique=True)
    request_type: Mapped[str] = mapped_column(String(50), nullable=False, doc='审批类型')
    requester_id: Mapped[str] = mapped_column(String(128), nullable=False, doc='申请人ID', index=True)
    title: Mapped[str] = mapped_column(String(200), doc='审批标题')
    content = mapped_column(JSON, doc='申请内容(JSON格式)')
    status: Mapped[str] = mapped_column(String(50), default=enums.ApprovalStatusEnum.PENDING.value)
    current_step: Mapped[int] = mapped_column(Integer, default=1, doc='当前审批步骤')
    create_time = mapped_column(DateTime, nullable=True, default=get_current_time_in_beijing)
    update_time = mapped_column(DateTime, nullable=True, default=get_current_time_in_beijing)
    delete_flag: Mapped[int] = mapped_column(Integer, default=0)

    user_requester: Mapped[PlatformUserModel] = relationship(
        'PlatformUserModel',
        primaryjoin="ApprovalRequestModel.requester_id==PlatformUserModel.biz_id",
        foreign_keys="[ApprovalRequestModel.requester_id]"
    )

    processes: Mapped[List['ApprovalProcessModel']] = relationship(
        'ApprovalProcessModel',
        primaryjoin="ApprovalProcessModel.approval_id==ApprovalRequestModel.biz_id",
        foreign_keys="[ApprovalProcessModel.approval_id]",
        back_populates="request"
    )


class ApprovalProcessModel(BaseModel):
    """审批表"""
    __tablename__ = 'approval_process'

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, doc='唯一标识')
    biz_id: Mapped[str] = mapped_column(String(128), unique=True)
    approval_id: Mapped[str] = mapped_column(String(128), doc="审批请求id")
    approver_id: Mapped[str] = mapped_column(String(128), doc="审批人/角色", index=True)
    step: Mapped[int] = mapped_column(Integer, default=1, doc="审批步骤")
    comment: Mapped[str] = mapped_column(String(255), nullable=True, doc="审批描述")
    status: Mapped[str] = mapped_column(String(20), default=enums.ApprovalStatusEnum.DRAFT.value)
    action_time = mapped_column(DateTime, nullable=True, doc="审批时间")
    last_step: Mapped[bool] = mapped_column(BOOLEAN, default=False, doc="终审")
    create_time = mapped_column(DateTime, nullable=True, default=get_current_time_in_beijing)
    update_time = mapped_column(DateTime, nullable=True, default=get_current_time_in_beijing)
    delete_flag: Mapped[int] = mapped_column(Integer, default=0)

    request: Mapped[ApprovalRequestModel] = relationship(
        'ApprovalRequestModel',
        primaryjoin="ApprovalProcessModel.approval_id==ApprovalRequestModel.biz_id",
        foreign_keys="[ApprovalProcessModel.approval_id]"
    )
    user_approver: Mapped[PlatformUserModel] = relationship(
        'PlatformUserModel',
        primaryjoin="ApprovalProcessModel.approver_id==PlatformUserModel.biz_id",
        foreign_keys="[ApprovalProcessModel.approver_id]"
    )


class AppVersionModel(BaseModel):
    """APP 版本更新表"""
    __tablename__ = "app_versions"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, doc='唯一标识')
    platform: Mapped[str] = mapped_column(String(30), index=True, doc="'android' 或 'ios'")
    version_name: Mapped[str] = mapped_column(String(50), doc="版本号，如 '1.2.3'")
    version_code: Mapped[int] = mapped_column(Integer, index=True, doc="版本代码，如 123")
    is_force_update: Mapped[bool] = mapped_column(BOOLEAN, default=False, doc="是否强制更新")
    content: Mapped[str] = mapped_column(Text, doc="更新内容（必选）")
    download_url: Mapped[str] = mapped_column(String(300), nullable=True)
    is_active: Mapped[bool] = mapped_column(BOOLEAN, default=True, doc="是否启用")
    create_time = mapped_column(DateTime, nullable=True, default=get_current_time_in_beijing)
    update_time = mapped_column(DateTime, nullable=True, default=get_current_time_in_beijing)
    create_by: Mapped[str] = mapped_column(String(128), nullable=True)
    update_by: Mapped[str] = mapped_column(String(128), nullable=True)
    delete_flag: Mapped[int] = mapped_column(Integer, default=0)

    __table_args__ = (
        UniqueConstraint('platform', 'version_code', name='uq_platform_version_code'),
    )


class SpecialContentModel(BaseModel):
    """智普AI播客任务"""
    __tablename__ = "special_markdown_content"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, doc='唯一标识')
    biz_id: Mapped[str] = mapped_column(String(128), unique=True)

    file_url: Mapped[str] = mapped_column(String(2048), nullable=True, doc="文件地址")
    content: Mapped[str] = mapped_column(Text, nullable=True, doc="文件内容")
    md5: Mapped[str] = mapped_column(String(64), nullable=True)
    created_at = mapped_column(DateTime, nullable=True, default=get_current_time_in_beijing)
    updated_at = mapped_column(DateTime, nullable=True, default=get_current_time_in_beijing)
    delete_flag: Mapped[int] = mapped_column(Integer, default=0)


class SpecialTaskModel(BaseModel):
    """智普AI播客任务"""
    __tablename__ = "special_markdown_task"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, doc='唯一标识')
    biz_id: Mapped[str] = mapped_column(String(128), unique=True)
    callback_url: Mapped[str] = mapped_column(String(2048), nullable=True, doc="回调地址")
    status: Mapped[str] = mapped_column(String(20), default=enums.TaskStatusEnum.CONFIRM.value, doc="状态")
    audio_url: Mapped[str] = mapped_column(String(2048), nullable=True)
    caption_list = mapped_column(JSON, nullable=True, doc="对话内容")
    duration: Mapped[int] = mapped_column(Integer, nullable=True, doc="时长 秒")
    size: Mapped[int] = mapped_column(Integer, nullable=True, doc="文件大小")
    retries: Mapped[int] = mapped_column(Integer, default=0, doc="重试次数")
    remark: Mapped[str] = mapped_column(String(255), nullable=True, doc="回调地址")
    ctrl_params = mapped_column(JSON, nullable=True, doc="Ai 播客参数")
    md5: Mapped[str] = mapped_column(String(64), nullable=True)
    category: Mapped[str] = mapped_column(String(20), nullable=True)
    content_id: Mapped[str] = mapped_column(String(128), nullable=True)
    user_platform_id: Mapped[str] = mapped_column(String(128), nullable=True, doc="平台keyid")
    point_id: Mapped[str] = mapped_column(String(128), nullable=True, doc="积分扣减记录Id")
    created_at = mapped_column(DateTime, nullable=True, default=get_current_time_in_beijing)
    updated_at = mapped_column(DateTime, nullable=True, default=get_current_time_in_beijing)
    delete_flag: Mapped[int] = mapped_column(Integer, default=0)
    source_content: Mapped[SpecialContentModel] = relationship(
        'SpecialContentModel',
        primaryjoin="SpecialTaskModel.content_id==SpecialContentModel.biz_id",
        foreign_keys="[SpecialTaskModel.content_id]"
    )

    def to_vo(self):
        return {
            "task_id": self.biz_id,
            "status": self.status,
            "audio_url": self.audio_url,
            "caption_list": self.caption_list,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
            "duration": self.duration,
            "size": self.size,
            "ctrl_params": self.ctrl_params,
            "user_id": self.user_platform_id
        }


class ShareModel(BaseModel):
    """笔记分享"""
    __tablename__ = "shares"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, doc='唯一标识')
    biz_id: Mapped[str] = mapped_column(String(128), index=True, unique=True)
    task_id: Mapped[str] = mapped_column(String(128), doc="任务id")
    name: Mapped[str] = mapped_column(String(2048), nullable=True)
    share_token: Mapped[str] = mapped_column(String(64), unique=True, index=True)
    user_id: Mapped[str] = mapped_column(String(128), doc="用户id")
    share_type: Mapped[str] = mapped_column(String(20), default=enums.ShareTypeEnum.PUBLIC.value, doc="分享类型")
    encryption_key: Mapped[str] = mapped_column(String(10), nullable=True, doc="明文密码")
    access_limit: Mapped[int] = mapped_column(Integer, default=-1, doc="访问次数限制")
    access_count: Mapped[int] = mapped_column(Integer, default=0, doc="访问次数")
    points: Mapped[int] = mapped_column(Integer, default=0, doc="奖励积分")
    expiry_date = mapped_column(DateTime, nullable=True, doc="过期时间")
    is_active: Mapped[bool] = mapped_column(BOOLEAN, default=True, doc="是否启用")
    create_time = mapped_column(DateTime, nullable=True, default=get_current_time_in_beijing)
    update_time = mapped_column(DateTime, nullable=True, default=get_current_time_in_beijing)
    delete_flag: Mapped[int] = mapped_column(Integer, default=0)

    task: Mapped[LectureTaskModel] = relationship(
        'LectureTaskModel',
        primaryjoin="ShareModel.task_id==LectureTaskModel.biz_id",
        foreign_keys="[ShareModel.task_id]",
        uselist=False
    )


class ShareAccessModel(BaseModel):
    """访问记录"""
    __tablename__ = "share_accesses"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, doc='唯一标识')
    biz_id: Mapped[str] = mapped_column(String(128), index=True, unique=True)
    share_id: Mapped[str] = mapped_column(String(128), doc="任务id")
    access_time = mapped_column(DateTime, default=get_current_time_in_beijing, doc="访问时间")
    ip_address: Mapped[str] = mapped_column(String(50), nullable=True, doc="访问IP")
    user_agent = mapped_column(Text, nullable=True, doc="用户代理信息")
    registered = mapped_column(BOOLEAN, default=False, doc="是否通过此访问注册")
    user_id: Mapped[str] = mapped_column(String(128), nullable=True, doc="访问用户Id")
    create_time = mapped_column(DateTime, nullable=True, default=get_current_time_in_beijing)
    update_time = mapped_column(DateTime, nullable=True, default=get_current_time_in_beijing)
    delete_flag: Mapped[int] = mapped_column(Integer, default=0)


# class MarketPlatformModel(BaseModel):
#     __tablename__ = "market_platforms"
#     id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, doc='唯一标识')
#     biz_id: Mapped[str] = mapped_column(String(128), index=True, unique=True)
#     type_name: Mapped[int] = mapped_column(SMALLINT, default=3)
#     parent_id: Mapped[int] = mapped_column(Integer, default=0)
#     name: Mapped[str] = mapped_column(String(50), doc="平台名称")
#     mark: Mapped[str] = mapped_column(String(50), index=True, unique=True, doc="标识")
#     callback_list = mapped_column(JSON, nullable=True, doc="回传配置")
#     create_time = mapped_column(DateTime, nullable=True, default=get_current_time_in_beijing)
#     update_time = mapped_column(DateTime, nullable=True, default=get_current_time_in_beijing)
#     delete_flag: Mapped[int] = mapped_column(Integer, default=0)
#
#
# class MarketClickModel(BaseModel):
#     __tablename__ = "market_clicks"
#     id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, doc='唯一标识')
#     biz_id: Mapped[str] = mapped_column(String(128), index=True, unique=True)
#     system_name: Mapped[str] = mapped_column(String(50), doc="系统")
#     oaid: Mapped[str] = mapped_column(String(50), nullable=True, doc="设备id")
#     imei: Mapped[str] = mapped_column(String(50), nullable=True, doc="设备标识")
#     click_time = mapped_column(DateTime, nullable=True, default=get_current_time_in_beijing, doc="点击时间")
#     channel_id: Mapped[str] = mapped_column(String(128),doc="渠道")
#     channel_name: Mapped[str] = mapped_column(String(50), doc="渠道英文名称")
#     click_code: Mapped[str] = mapped_column(String(50), nullable=True, doc="监测链接code")
#     adid: Mapped[str] = mapped_column(String(50), nullable=True, doc="广告创意id")
#     group_id: Mapped[str] = mapped_column(String(50), nullable=True, doc="广告组id")
#     campaign_id: Mapped[str] = mapped_column(String(50), nullable=True, doc="计划id")
#     content = mapped_column(JSON, nullable=True, doc="数据")
#     source_type: Mapped[str] = mapped_column(String(50), nullable=True, doc="平台名称")
#     remark: Mapped[str] = mapped_column(String(50), nullable=True, doc="平台名称")
#     create_time = mapped_column(DateTime, nullable=True, default=get_current_time_in_beijing)
#     update_time = mapped_column(DateTime, nullable=True, default=get_current_time_in_beijing)
#     delete_flag: Mapped[int] = mapped_column(Integer, default=0)