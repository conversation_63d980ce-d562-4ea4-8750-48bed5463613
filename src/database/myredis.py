import asyncio
from contextlib import asynccontextmanager
from typing import Any, Async<PERSON><PERSON>ator, TypeVar, Type

import msgpack
import redis.asyncio as aioredis
from redis.asyncio import Redis

from config import settings
from src.database.models import BaseModel
from src.util import stringify

redis_url = f"redis://{settings.REDISUSER}:{settings.REDISPASSWORD}@{settings.REDISHOST}:{settings.REDISPORT}/1"
aioredis_pool = aioredis.ConnectionPool.from_url(
    url=redis_url,
    decode_responses=False,
    max_connections=settings.REDIS_MAX_CONECTIONS
)
T = TypeVar('T', bound=BaseModel)


async def async_redis() -> AsyncGenerator[Redis, Any]:
    async with aioredis.Redis(connection_pool=aioredis_pool) as _redis:
        yield _redis


@asynccontextmanager
async def get_async_redis() -> AsyncGenerator[Redis, Any]:
    async with aioredis.Redis(connection_pool=aioredis_pool) as _redis:
        yield _redis


class RedisLockContext:
    def __init__(self, redis_instance: Redis, lock_key: str, timeout: int = 30):
        self.redis_instance = redis_instance
        self.lock_key = lock_key
        self.timeout = timeout

    async def acquire(self):
        acquired_lock = False
        while not acquired_lock:
            acquired_lock = await self.redis_instance.set(self.lock_key, 1, nx=True, ex=self.timeout)
            if not acquired_lock:
                await asyncio.sleep(0.05)
        return self

    async def release(self):
        await self.redis_instance.delete(self.lock_key)


def lock(lock_key: str, timeout: int = 30):
    def decorator(func):
        async def wrapper(*args, **kwargs):
            async with get_async_redis() as _redis:
                redis_lock = RedisLockContext(_redis, lock_key, timeout)
                try:
                    await redis_lock.acquire()
                    return await func(*args, **kwargs)
                finally:
                    await redis_lock.release()

        return wrapper

    return decorator


async def get_cache(
        redis_: aioredis.Redis,
        key: str,
        model: type = None
) -> Any:
    """获取缓存"""
    try:
        data = await redis_.get(key)
        if not data:
            return None
        deserialized = msgpack.unpackb(
            data,
            raw=False,  # 自动转换str
            timestamp=3  # 处理时间戳
        )
        return model(**deserialized) if model else deserialized
    except Exception as e:
        print(f"缓存获取失败: {stringify.from_exception(e)}")
        # logger.error(f"缓存获取失败: {stringify.from_exception(e)}")
        return None


async def set_cache(
        redis_: aioredis.Redis,
        key: str,
        value: Any,
        ttl: int = None
) -> bool:
    """设置缓存"""
    try:
        serialized = msgpack.packb(
            value,
            use_bin_type=True,  # 正确编码二进制数据
            datetime=True  # 特殊处理datetime
        )
        actual_ttl = ttl or settings.DEFAULT_CACHE_TTL
        return await redis_.setex(
            key,
            actual_ttl,
            serialized
        )
    except Exception as e:
        print(f"缓存设置失败: {stringify.from_exception(e)}")
        # logger.error(f"缓存设置失败: {stringify.from_exception(e)}")
        return False


async def set_push(
        redis_: aioredis.Redis,
        key: str,
        value: Any,
) -> int:
    """设置缓存"""
    try:
        serialized = msgpack.packb(
            value,
            use_bin_type=True,  # 正确编码二进制数据
            datetime=True  # 特殊处理datetime
        )
        return await redis_.lpush(
            key,
            serialized
        )
    except Exception as e:
        print(f"缓存列表设置失败: {stringify.from_exception(e)}")
        # logger.error(f"缓存设置失败: {stringify.from_exception(e)}")
        return 0


async def get_push(
        redis_: aioredis.Redis,
        key: str
) -> Any:
    """读取列表"""
    try:
        data = await redis_.rpop(key)
        if data:
            deserialized = msgpack.unpackb(
                data,
                raw=False,  # 自动转换str
                timestamp=3  # 处理时间戳
            )
            return deserialized
    except Exception as e:
        print(f"缓存列表获取失败: {stringify.from_exception(e)}")
        # logger.error(f"缓存设置失败: {stringify.from_exception(e)}")
        return None


async def get_cached_or_fetch(
        redis_: aioredis.Redis,
        key: str,
        fetch_func: callable,
        model: Type[T] = None,
        ttl: int = 60 * 60 * 24,
        flush: bool = False
) -> T | None:
    """
    获取缓存或从数据源获取的通用依赖
    :param redis_
    :param key: 缓存键
    :param fetch_func: 数据获取函数
    :param model: Pydantic模型类
    :param ttl: 缓存时间(秒)
    :param flush: 是否强制刷新缓存
    :return: 模型实例
    """
    if not flush:
        cached = await get_cache(redis_, key, model)
        if cached:
            return cached

    fresh_data = await fetch_func()
    if fresh_data:
        await set_cache(redis_, key, fresh_data, ttl)
        return model(**fresh_data) if model else fresh_data
    return None


async def redis_cache_keys_redis(redis, key, vague=True):
    if vague:
        key = key + "*"
    cursor, cache_keys = 0, []
    while True:
        cursor, _keys = await redis.scan(cursor=cursor, match=key, count=20)
        cache_keys += _keys
        if cursor == 0:
            break
    return cache_keys


async def clear_redis_cache(redis, key, vague=True):
    cache_keys = await redis_cache_keys_redis(redis, key, vague)
    clear_keys = list()
    for item in cache_keys:
        if key in str(item):
            clear_keys.append(item)
    if len(clear_keys) > 0:
        await redis.delete(*clear_keys)
