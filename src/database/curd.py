"""
基类/混入
"""
from datetime import datetime, date, time

from sqlalchemy import select, update, func
from sqlalchemy.ext.asyncio import AsyncAtt<PERSON>, AsyncSession
from sqlalchemy.orm import DeclarativeBase, load_only

from src.util.stringify import TIME_FTIME, DATE_FTIME, ONLY_TIME


async def serialize_model(model, max_depth=0):
    if not model:
        return model
    if max_depth > 4:
        return None
    data = model.__dict__
    if "_sa_instance_state" in data:
        del data["_sa_instance_state"]
    depth = max_depth + 1
    for key, value in data.items():
        if isinstance(value, Base):
            data[key] = await serialize_model(value, depth)
        elif isinstance(value, datetime):
            data[key] = value.strftime(TIME_FTIME)
        elif isinstance(value, date):
            data[key] = value.strftime(DATE_FTIME)
        elif isinstance(value, time):
            data[key] = value.strftime(ONLY_TIME)
        elif isinstance(value, list):
            list_data = list()
            for item in value:
                if isinstance(item, Base):
                    sub_data = await serialize_model(item, depth)
                else:
                    sub_data = item
                list_data.append(sub_data)
            data[key] = list_data
        else:
            data[key] = value
    return data


async def serialize_paginate(data: dict):
    items = list()
    for item in data.get("items"):
        items.append(await serialize_model(item))
    data["items"] = items
    return data


async def serialize_list(data: list):
    result_data = list()
    for item in data:
        result_data.append(await serialize_model(item))
    return result_data


class Base(AsyncAttrs, DeclarativeBase):

    @staticmethod
    async def serialize_data(cls):
        data = await serialize_model(cls)
        return data

    @classmethod
    async def generate_model(cls, db: AsyncSession, data: dict):
        """
        创建数据
        """
        model_data = cls(**data)
        db.add(model_data)
        await db.commit()
        await db.refresh(model_data)
        return model_data

    @classmethod
    async def generate_list_model(cls, db: AsyncSession, data: list):
        for item in data:
            db.add(item)
        await db.commit()

    @classmethod
    async def update_model(cls, db: AsyncSession, filters, data: dict):
        """
        更新数据
        :param db:
        :param filters: 更新条件
        :param data: 更新内容
        :return:
        """
        async_result = await db.execute(
            update(cls).filter(*filters).values(data)
        )
        await db.commit()
        return async_result.rowcount

    @classmethod
    async def query_model(cls,
                          db: AsyncSession,
                          filters: list,
                          fields: list = None,
                          joins: list = None,
                          sorts: list = None,
                          serialize: bool = False
                          ):
        """
        获取一条信息
        :param db:
        :param filters: 查询条件
        :param fields: 限制字段
        :param joins: 关联关系数据 joined load(getattr(table_model, key)).load_only(*_field_list)
        :param sorts: 排序
        :param serialize
        :return:
        """
        if joins is None:
            joins = []
        if fields is None:
            fields = []

        stmt = select(cls)
        if fields:
            stmt = stmt.options(load_only(*fields), *joins)
        else:
            stmt = stmt.options(*joins)
        if sorts:
            stmt = stmt.order_by(*sorts)
        async_result = await db.execute(stmt.filter(*filters))
        data = async_result.unique().first()
        if data is None:
            return None
        if serialize:
            data = await cls.serialize_data(data[0])
        else:
            data = data[0]
        return data

    @staticmethod
    def search_paginate(data, current_page: int, total_name: str) -> dict:
        """
        处理分页
        :param data:
        :param current_page:
        :param total_name
        :return:
        """
        res_data = {
            total_name: 0,
            'items': [],
            'current': current_page
        }
        for index, item in enumerate(data):
            if index == 0:
                res_data[total_name] = item[1]
            res_data['items'].append(item[0])
        return res_data

    @classmethod
    async def search_model_paginate(
            cls,
            db: AsyncSession,
            page_size: int,
            current_page: int,
            filters,
            fields: list = None,
            joins: list = None,
            sorts: list = None,
            serialize: bool = False,
            total_name: str = 'total'
    ):
        """
        分页查询
        :param db:
        :param page_size:
        :param current_page:
        :param filters: 条件
        :param fields: 限制字段
        :param joins: link表
        :param sorts: 排序
        :param serialize: 是否序列化
        :param total_name
        :return:
        """
        if joins is None:
            joins = []
        if fields is None:
            fields = []
        stmt = select(
            cls,
            func.count(cls.id).over().label(total_name)
        )
        if fields:
            stmt = stmt.options(load_only(*fields), *joins)
        else:
            stmt = stmt.options(*joins)

        if isinstance(filters, list):
            stmt = stmt.filter(*filters)
        else:
            stmt = stmt.filter(filters)

        if sorts:
            stmt = stmt.order_by(*sorts)

        offset = page_size * (current_page - 1)
        async_result = await db.execute(stmt.offset(offset).limit(page_size))
        data = async_result.unique().all()
        result_data = cls.search_paginate(data, current_page, total_name)
        if serialize:
            result_data = await serialize_paginate(result_data)
        return result_data

    @classmethod
    async def search_model_all(
            cls,
            db: AsyncSession,
            filters=None,
            limit_size: int = 500,
            fields: list = None,
            joins: list = None,
            sorts: list = None,
            serialize: bool = False
    ):
        """
        分页查询
        :param db:
        :param limit_size: 最大数量
        :param filters: 条件
        :param fields: 限制字段
        :param joins: link表
        :param sorts: 排序
        :param serialize
        :return:
        """
        if filters is None:
            filters = []
        if joins is None:
            joins = []
        if fields is None:
            fields = []
        stmt = select(cls)
        if fields:
            stmt = stmt.options(load_only(*fields), *joins)
        else:
            stmt = stmt.options(*joins)

        if isinstance(filters, list):
            stmt = stmt.filter(*filters)
        else:
            stmt = stmt.filter(filters)

        if sorts:
            stmt = stmt.order_by(*sorts)

        async_result = await db.execute(stmt.offset(0).limit(limit_size))
        data = async_result.unique().scalars().all()
        if serialize:
            data = await serialize_list(data)
        return data

    @classmethod
    async def count_model(
            cls,
            db: AsyncSession,
            filters,
    ):
        """
        数量
        """
        stmt = select(func.count(cls.id)).select_from(cls)
        async_result = await db.execute(stmt.filter(*filters))
        data = async_result.unique().scalar_one_or_none()
        return data

    @classmethod
    async def random_model(
            cls,
            db: AsyncSession,
            filters,
            fields: list = None,
            joins: list = None
    ):
        """
        随机一条信息
        """
        if joins is None:
            joins = []
        if fields is None:
            fields = []
        stmt = select(cls)
        if fields:
            stmt = stmt.options(load_only(*fields), *joins)
        else:
            stmt = stmt.options(*joins)

        if isinstance(filters, list):
            stmt = stmt.filter(*filters)
        else:
            stmt = stmt.filter(filters)

        async_result = await db.execute(stmt.order_by(func.random()).limit(1))
        data = async_result.unique().scalar_one_or_none()
        return data
