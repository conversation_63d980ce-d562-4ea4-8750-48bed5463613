"""
常量
"""
import re

voice_female = ['zh_f_qingqing', 'zh_f_hunhun', 'zh_f_jiang<PERSON>', 'zh_f_youyou', 'zh_f_yelan', 'zh_f_cheng<PERSON>',
                'zh_f_linge']

voice_male = ['zh_m_jiawen', 'zh_m_suyu', 'zh_m_gulin', 'zh_m_tianfang', 'zh_m_xia<PERSON>uang', 'zh_m_shenlan']

# 笔记本相关
DEFAULT_FOLDER = "默认笔记本"
DEFAULT_CONTENTS = 4  # 首页默认展示的 content 个数(sub_folders + tasks <=4)
MAX_FOLDER_DEPTH = 3  # 最大层级

# 任务相关
MAX_RETRY = 5  # 任务重试次数

# 文章相关
MAX_RECORD = 30  # 每条划线记录的最大记录数

# 联系客服提示
MESSAGE_CONTACT = "如有疑问，请联系我们帮您解决，微信：ReadLecture"

# 用户粉丝相关
# 邀请注册增加积分
FANS_REGISTER = 30

HEADER_SOURCE = "source"

BAIDUPAN_REDIS_PREFIX = "baidu_pan_"
BAIDUPAN_DOWNLOAD_URL_HOST = "https://pan.baidu.com"
BAIDUPAN_ARG_PATTERN = re.compile(r"fs_id=(.+)$")

ALIPAN_REDIS_PREFIX = "ali_pan_"
ALIPAN_DOWNLOAD_URL_HOST = "https://alipan.com"
ALIPAN_ARG_PATTERN = re.compile(r"drive_id=(.+?)&file_id=(.+)$")

OPEN_BATCH_MAX_COUNT = 10

SHARE_TOKEN_LENGTH = 18

PODCAST_CATEGORY = [
    "新闻播客类",
    "深度访谈类",
    "知识科普类",
    "故事叙事类",
    "情感陪伴类",
    "八卦娱乐类",
    "电影解说类"
]
PODCAST_CATEGORY_DEFAULT = "知识科普类"

MARKET_CALLBACK_LIST = [
    {
        "name": "激活",
        "mark": "activation",
        "is_active": False
    },
    {
        "name": "注册",
        "mark": "register",
        "is_active": False
    },
    {
        "name": "次留",
        "mark": "next_stay",
        "is_active": False
    },
    {
        "name": "支付",
        "mark": "payment",
        "is_active": False
    }
]
