import re
import time
from traceback import format_exc

import pytz
import redis.asyncio as redis
from apscheduler.schedulers.background import BackgroundScheduler
from fastapi import FastAPI
from fastapi.concurrency import asynccontextmanager
from fastapi.exceptions import RequestValidationError
from fastapi_limiter import FastAPILimiter
from opentelemetry.exporter.prometheus import PrometheusMetricReader
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
from opentelemetry.metrics import get_meter, set_meter_provider
from opentelemetry.sdk.metrics import MeterProvider
from prometheus_client import start_http_server
from starlette.exceptions import HTTPException
from starlette.middleware.cors import CORSMiddleware
from starlette.requests import Request
from starlette.responses import JSONResponse, Response

from config import settings
from src.api.ai.lecture_ai import router as lecture_ai_router
from src.api.ai.lecture_ai_stream import router as lecture_ai_stream_router
from src.api.ai.lecture_ai_task import router as ai_task_router
from src.api.article.article_api import router as article_router
from src.api.article.article_api_v2 import router as article_v2_router
from src.api.article.article_detail_api import router as article_detail_router
from src.api.article.article_underline_api import router as article_underline_router
from src.api.backend_platform.routers import backend_register_blue
from src.api.help.v1 import router as help_router
from src.api.login.ali_wanpan import router as ali_wanpan_router
from src.api.login.baidu_wanpan import router as baidu_wanpan_router
from src.api.login.clerk_oauth import router as oauth_router
from src.api.login.lianxiang_user_api import router as lianxiang_user_router
from src.api.login.member_api import router as member_router
from src.api.login.sms_login import router as sms_router
from src.api.login.user_api import router as user_router
from src.api.login.user_auth_api import router as user_auth_router
from src.api.login.wechat_login import router as wechat_login_router
from src.api.login.wechat_oauth import router as wechat_oauth_router
from src.api.metric.track_event import router as event_router
from src.api.open.book2audio_api import router as book2audio_router
from src.api.open.open_platform_task_api import router as open_platform_task_router
from src.api.open_platform.routers import open_register_blue
from src.api.order.exchange_code import router as exchange_code_router
from src.api.order.order_api_v2 import router as order_api_v2_router
from src.api.pay.ali import router as alipay_router
from src.api.pay.app import router as apppay_router
from src.api.pay.h5 import router as h5pay_router
from src.api.pay.iapNew import router as apple_pay_router
from src.api.special_platform.routers import special_register_blue
from src.api.task.folder_api import router as folder_router
from src.api.task.task_api import router as task_router
from src.api.task.task_api_v2 import router as task_v2_router
from src.api.task.task_api_v3 import router as task_v3_router
from src.api.task.task_api_v4 import router as task_v4_router
from src.api.task.task_api_v5 import router as task_v5_router
from src.api.task.url_detal_api import router as url_router
from src.api.visit.affiliates_api import router as affiliates_router
from src.api.visit.affiliates_fans_api import router as aff_fans_router
from src.api.visit.affiliates_order_api import router as aff_order_router
from src.core import middleare
from src.corn.task_off_user import offuser_task
from src.corn.task_scheduler import task
from src.corn.task_subscribe import subscribe_track_event
from src.database import myredis
from src.logger.logUtil import get_logger
from src.orm.init_table import init_all_tables
from src.util.TokenUtil import get_current_user
from src.util.cacheUtil import ttl_cache
from src.util.dateUtil import pretty_timedelta
from src.util.redis_util import create_redis_pool, shutdown_redis_pool
from src.api.client_platform.routers import client_register_blue

# 初始化 MeterProvider 并配置 Prometheus 导出
prometheus_reader = PrometheusMetricReader()
provider = MeterProvider(metric_readers=[prometheus_reader])
set_meter_provider(provider)

start_http_server(settings.PROMETHEUS_PORT)

# 创建一个 Meter
meter = get_meter(__name__)
request_counter = meter.create_counter(
    "http_requests_total",
    description="Total number of HTTP requests",
)

logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    create_redis_pool(app)
    subscribe_track_event()
    init_all_tables()
    redis_connection = redis.from_url(myredis.redis_url, encoding="utf-8", decode_responses=True)
    await FastAPILimiter.init(
        redis_connection,
        prefix="readlecture-limiter",
        identifier=middleare.user_aware_identifier,
        http_callback=middleare.custom_http_callback
    )
    yield
    shutdown_redis_pool(app)


app = FastAPI(lifespan=lifespan, docs_url="/docs" if settings.APP_DEBUG else None, version="1.1.0")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*", "http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(wechat_oauth_router)
app.include_router(baidu_wanpan_router)
app.include_router(ali_wanpan_router)
app.include_router(user_auth_router)
app.include_router(task_router)
app.include_router(order_api_v2_router)
app.include_router(task_v2_router)
app.include_router(task_v3_router)
app.include_router(task_v4_router)
app.include_router(task_v5_router)
app.include_router(user_router)
app.include_router(lianxiang_user_router)
app.include_router(oauth_router)
app.include_router(member_router)
app.include_router(article_router)
app.include_router(article_detail_router)

app.include_router(article_underline_router)

app.include_router(affiliates_router)
app.include_router(aff_fans_router)
app.include_router(aff_order_router)
app.include_router(open_platform_task_router)
app.include_router(url_router)
app.include_router(alipay_router)
app.include_router(wechat_login_router)
app.include_router(exchange_code_router)
app.include_router(sms_router)
app.include_router(event_router)
app.include_router(lecture_ai_router)
app.include_router(lecture_ai_stream_router)
app.include_router(folder_router)
app.include_router(ai_task_router)
app.include_router(h5pay_router)
app.include_router(apppay_router)
app.include_router(article_v2_router)
app.include_router(book2audio_router)
app.include_router(help_router)
app.include_router(apple_pay_router)

app_client_api = FastAPI(title="C端接口", docs_url="/docs" if settings.APP_DEBUG else None, version="1.1.0")

app_open_api = FastAPI(title="开放平台", docs_url="/docs" if settings.APP_DEBUG else None, version="1.1.0")

app_special_api = FastAPI(title="特供接口", docs_url="/docs" if settings.APP_DEBUG else None, version="1.1.0")

app_backend_api = FastAPI(title="管理后台", docs_url="/docs" if settings.APP_DEBUG else None, version="1.1.0")

app.mount('/client', app_client_api)
app.mount('/open', app_open_api)
app.mount('/special', app_special_api)
app.mount('/backend', app_backend_api)

client_register_blue(app_client_api)
open_register_blue(app_open_api)
special_register_blue(app_special_api)
backend_register_blue(app_backend_api)

scheduler = BackgroundScheduler()
scheduler.add_job(task, "interval", minutes=3, timezone=pytz.UTC)
# 注销账户任务，3分钟执行一次
scheduler.add_job(offuser_task, "interval", minutes=3, timezone=pytz.UTC)
scheduler.start()


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    response = JSONResponse(
        status_code=200,
        content={"message": exc.errors(), "code": "10002"},
    )
    return add_cors_headers(response)


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc):
    # 如果是HTTPException 就直接抛出对应异常
    if isinstance(exc, HTTPException):
        response = JSONResponse(
            status_code=exc.status_code,
            content={"message": exc.detail, "code": "10002"},
        )
    else:
        response = JSONResponse(
            status_code=200,
            content={"message": "An unexpected error occurred.", "code": "10001"},
        )
        logger.error("An unexpected error occurred: %s", format_exc())
    return add_cors_headers(response)


# Function to check if the request path matches any pattern
def is_path_whitelisted(path, patterns):
    for pattern in patterns:
        if re.match(pattern, path):
            return True
    return False


@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    """
    # 针对所有的请求验证下session，除了几个白名单的接口，其他接口都要从http header 里面拿到cookies， 然后从cookie 里面拿到jwToken=xxx,
    # 然后根据token 从ttl 缓存中拿到用户信息，然后放到request.session 中，后续的接口就可以直接从request.session 中拿到用户信息了
    # 并且在每次请求的时候，都要刷新ttl 缓存中的用户信息，保证用户信息不会过期
    :param request: 请求
    :param call_next:
    :return:
    """
    # 如果请求是OPTION ，则直接返回成功
    if request.method == "OPTIONS":
        response = Response(status_code=204)
        response.headers["Access-Control-Allow-Origin"] = "*"
        response.headers["Access-Control-Allow-Methods"] = "OPTIONS, GET, POST, PUT, DELETE"
        response.headers["Access-Control-Allow-Headers"] = "*"
        return response
    # 白名单
    if request.url.path in [
        "/",
        "/docs",
        "/openapi.json",
        "/api/v1/oauth",
        "/api/v1/test/cache",
        "/api/v2/task/getLatest",
        "/api/v2/task/finish",
        "/api/v1/user/edit",
        "/api/v2/ai-task/create",
        "/api/v2/ai-task/latest",
        "/api/v2/ai-task/finish",
        "/api/v2/ai-task/retry",
        "/api/v2/ai-task/sections",
        "/api/v2/ai-task/remove_queue",
        "/api/v2/ai-task/lock_queue_ai",
        "/api/v1/wechat/xml/wechat_notify",
        "/api/v4/task/getLatest",
        "/api/v4/task/update-sections",
        "/api/v4/task/finish",
        "/api/v5/task/getLatest",
        "/api/v5/task/update-sections",
        "/api/v5/task/finish",
        "/api/v1/user/mail/bind",
        "/api/v1/wechat/check",
        "/api/v1/wechat/auth",
        "/api/v1/wechat/wx-authorize-notify",
        "/api/v3/task/finish",
        "/api/v2/order/plan-list",
        "/api/v2/order/alipay/mobile/create",
        "/api/v1/wechat/xml/wechat_notify",
        "/api/v2/task/status",
        "/api/v2/order/union/pay",
        "/api/v1/user/register/mail/code",
        "/api/v2/order/alipay/status",
        "/api/v2/order/alipay/mobile/status",
        "/api/v1/track/event",
        "/api/v2/login/wx-authorize-url",
        "/api/v2/login/wx-authorize-login",
        "/api/v1/user/baidu_wanpan/auth/access_token",
        "/api/v1/user/ali_wanpan/auth/access_token",
        "/api/v1/help/wechat-official-account",
        "/api/v1/help/bind-email-for-phone-user",
        "/api/v1/order/apple_pay/iap/create",
        "/api/v1/order/apple_pay/iap/notify",
        "/api/v1/order/apple_pay/iap/verify",
        "/open/docs",
        "/open/openapi.json",
        "/special/docs",
        "/special/openapi.json",
        "/backend/docs",
        "/backend/openapi.json",
        "/client/docs",
        "/client/openapi.json",
    ]:
        resp = await call_next(request)
        return add_cors_headers(resp)

    # 正则白名单
    patterns = [
        r"^/api/v1/member/charge/free/[^/]+/[^/]+$",
        r"^/api/v1/member/charge/vip/[^/]+/[^/]+$",
        r"^/open/api/v1/.*",
        r"^/api/v1/paddle/.*",
        r"^/api/v1/user/.*",
        r"^/api/v1/lianxiang_user/.*",
        r"^/api/v1/wechat/.*",
        r"^/api/v4/task/.*",
        r"^/api/v1/article/download/.*",
        r"^/api/v2/article/download/.*",
        r"^/api/v1/book2audio/.*",
        r"^/special/api/v1/.*",
        r"^/backend/api/v1/.*",
        r"^/client/api/v1/.*",
    ]
    try:
        if is_path_whitelisted(request.url.path, patterns):
            response = await call_next(request)
            return add_cors_headers(response)

        # Check for JWT token in cookies
        user = await get_current_user(request)

        response = await call_next(request)
        # 刷新缓存
        ttl_cache.set(user.biz_id, user)

        if response.status_code == 429:
            t = response.headers.get('Retry-After')
            response = JSONResponse(status_code=200,
                                    content={"message": f"您的请求过于频繁, 请{t}秒稍后重试", "code": 10001})

        return add_cors_headers(response)
    except HTTPException as e:
        response = JSONResponse(status_code=401, content={"message": e.detail, "code": "401"})
        return add_cors_headers(response)
    except Exception:
        logger.error("HTTPException occurred: %s", format_exc())
        response = JSONResponse(status_code=500, content={"message": "Internal Server Error", "code": "10001"})
        return add_cors_headers(response)


def add_cors_headers(response: JSONResponse):
    if settings.APP_DEBUG:
        response.headers["Access-Control-Allow-Origin"] = "*"
        response.headers["Access-Control-Allow-Methods"] = "OPTIONS, GET, POST, PUT, DELETE"
        response.headers["Access-Control-Allow-Headers"] = "*"
    return response


FastAPIInstrumentor.instrument_app(app)


@app.middleware("http")
async def metrics_middleware(request, call_next):
    start_time = time.time()
    request_counter.add(1, {"method": request.method, "endpoint": request.url.path})
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = pretty_timedelta(process_time)
    return response


@app.get("/")
def read_root():
    return {"Hello": "World"}
