import uuid
from datetime import datetime

from peewee import AutoField, <PERSON><PERSON><PERSON><PERSON>, DateTimeField, DecimalField, IntegerField, Model, SmallIntegerField

from src.logger.logUtil import get_logger
from src.model.affiliates_fans import get_fans_by_user_id
from src.model.affiliates_manager import Affiliates, get_affiliates_by_biz_id
from src.model.affiliates_product import AffiliatesProduct
from src.model.member_manager import update_member_by_points
from src.orm.pgrepo import db

logger = get_logger(__name__)


class AffiliatesOrder(Model):
    """
    分销订单
    """

    id = AutoField(primary_key=True, verbose_name="自增id")
    biz_id = CharField(null=False, unique=True)  # 业务id
    order_id = CharField(null=True)  # 支付订单id
    product_id = IntegerField(null=True)  # 产品id
    affiliates_id = CharField(null=False)  # 分销商id
    fans_id = CharField(null=False)  # 粉丝id
    fans_name = Char<PERSON><PERSON>(null=True)  # 粉丝名称
    pay_amount = DecimalField(max_digits=10, decimal_places=2, null=False)  # 支付金额
    affiliates_goods_type = SmallIntegerField(default=1)  # 分销商品类型 1积分 2 金额
    order_point = IntegerField(null=True)  # 订单积分
    order_status = SmallIntegerField(default=1)  # 订单状态 1 未获得 2 已获得 3 已退款
    audit_status = SmallIntegerField(default=1)  # 审核状态 1 未审核 2 已审核
    create_time = DateTimeField(default=datetime.now)
    update_time = DateTimeField(default=datetime.now)
    delete_flag = SmallIntegerField(default=0)

    class Meta:
        table_name = "affiliates_order"
        database = db


def get_count_and_page_by_affiliates_id(affiliates_id: str, page_size: int = 10, page_no: int = 1):
    """
    Get order count and page by affiliates id
    :param affiliates_id: affiliates id
    :param page_size: page size
    :param page_no: page no
    :return: order count and page
    """
    total = AffiliatesOrder.select().where(AffiliatesOrder.affiliates_id == affiliates_id).count()
    orders = (
        AffiliatesOrder.select()
        .where(AffiliatesOrder.affiliates_id == affiliates_id)
        .order_by(-AffiliatesOrder.id)
        .paginate(page_no, page_size)
    )
    return total, orders


def create_affiliates_order_by_level(fans, points, **order):
    if points == 0:
        return

    try:
        with db.atomic():
            # 创建分成
            affs = get_affiliates_by_biz_id(fans.affiliates_id)
            if not affs:
                return
            affs.total_point += points
            Affiliates.update(total_point=Affiliates.total_point + points).where(
                Affiliates.biz_id == affs.biz_id
            ).execute()

            update_member_by_points(affs.user_id, points)

            # 创建分销订单
            AffiliatesOrder.create(
                biz_id="aff_order" + str(uuid.uuid4()),
                order_id=order.get("order_id"),
                product_id=order.get("product_id"),
                affiliates_id=fans.affiliates_id,
                order_point=points,
                order_status=2,
                audit_status=2,
                fans_id=fans.biz_id,
                fans_name=fans.name,
                pay_amount=order.get("amount"),
            )
    except Exception:
        logger.exception(f"Error creating affiliates order, fans_id={fans.biz_id}, level_percent={points}")


async def create_affiliates_order_and_distribute(**kwargs):
    """
    Create distribution order and distribute
    :param order: lecture order info
    :return:
    """

    # check if order is fans order
    if not kwargs.get("order_id"):
        return None
    if kwargs.get("amount") == 0:
        return None
    # get product percent   from order
    points = AffiliatesProduct.get_by_product_id(kwargs.get("product_id"))
    if not points:
        return None

    fans = get_fans_by_user_id(kwargs.get("user_id"))
    if not fans:
        return None
    # 更新粉丝状态
    if fans.bind_status == 0:
        fans.bind_status = 1
        fans.save()
    # 创建1级分销订单
    create_affiliates_order_by_level(fans, points, **kwargs)
