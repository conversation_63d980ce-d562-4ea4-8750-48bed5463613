import random
from datetime import datetime, timezone
from enum import IntEnum
from typing import Optional
from uuid import UUID

from peewee import AutoField, CharField, DateTimeField, Model, SmallIntegerField, IntegerField

from src.logger.logUtil import get_logger
from src.orm.pgrepo import db

logger = get_logger(__name__)


class BindStatus(IntEnum):
    """
    Bind status, 0 temporary binding, 1 permanent binding
    if fans bind to affiliates, it will be temporary binding,
    and fans pay for the affiliates, it will be permanent binding
    if fans is temporary binding, it can bind other affiliates after 30 days
    """

    TEMPORARY = 0  # 临时绑定
    PERMANENT = 1  # 永久绑定


class AffiliatesFans(Model):
    """
    粉丝
    """

    id = AutoField(primary_key=True)
    biz_id = CharField(null=False, unique=True)
    name = CharField(null=True)
    affiliates_id = CharField(null=False)
    user_id = CharField(null=False, default="system")
    bind_time = DateTimeField(default=lambda: datetime.now(tz=timezone.utc))
    bind_status = SmallIntegerField(default=0)  # 0 临时绑定 1 永久绑定
    channel = CharField(null=True)  # 渠道
    point = IntegerField(null=True, default=30) # 奖励积分
    create_time = DateTimeField(default=lambda: datetime.now(tz=timezone.utc))
    update_time = DateTimeField(default=lambda: datetime.now(tz=timezone.utc))
    delete_flag = SmallIntegerField(default=0)

    class Meta:
        table_name = "affiliates_fans"
        database = db


def create_affiliates_fans(**kwargs):
    """
    Create affiliates fans
    :param kwargs: affiliates fans info
    """
    AffiliatesFans.create(
        biz_id=str(UUID(int=random.getrandbits(128))),
        affiliates_id=kwargs.get("affiliates_id"),
        user_id=kwargs.get("user_id"),
        bind_time=kwargs.get("bind_time"),
    )


def get_fans_count_and_page_by_affiliates_id(affiliates_id, page_size, page_no):
    """
    Get fans count and page by affiliates id
    :param affiliates_id:
    :param page_size:
    :param page_no:
    :return:
    """
    total = (
        AffiliatesFans.select()
        .where(AffiliatesFans.affiliates_id == affiliates_id, AffiliatesFans.delete_flag == 0)
        .count()
    )
    fans = (
        AffiliatesFans.select()
        .where(AffiliatesFans.affiliates_id == affiliates_id, AffiliatesFans.delete_flag == 0)
        .order_by(-AffiliatesFans.id)
        .paginate(page_no, page_size)
    )
    return total, fans


def get_fans_by_user_id(user_id) -> Optional[AffiliatesFans]:
    """
    Get fans by user id
    :param user_id: user id
    :return: fans
    """
    fans = (
        AffiliatesFans.select().where((AffiliatesFans.user_id == user_id) & (AffiliatesFans.delete_flag == 0)).first()
    )
    if fans:
        return fans
    else:
        return None
