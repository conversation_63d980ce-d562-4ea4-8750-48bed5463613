from datetime import datetime, timezone

from peewee import (
    <PERSON><PERSON><PERSON><PERSON>,
    DateTimeField,
    IntegerField,
    Model,
    SmallIntegerField,
    TextField,
    AutoField
)
from playhouse.postgres_ext import J<PERSON><PERSON>ield

from src.database.enums import TaskStatusEnum
from src.orm.pgrepo import db


class SpecialMarkdownContent(Model):
    """ Markdown 文本内容"""
    id = AutoField(primary_key=True)
    biz_id = CharField(unique=True, max_length=150, null=False)
    file_url = CharField(null=True, max_length=2048)
    content = TextField(null=True)
    md5 = CharField(null=True, max_length=64)
    created_at = DateTimeField(default=lambda: datetime.now(timezone.utc))  # 创建时间, UTC 格式
    updated_at = DateTimeField(default=lambda: datetime.now(timezone.utc))
    delete_flag = IntegerField(default=0)

    class Meta:
        table_name = "special_markdown_content"
        database = db


class SpecialMarkdownTask(Model):
    """ Markdown 播客任务"""
    id = <PERSON><PERSON>ield(primary_key=True)
    biz_id = CharField(unique=True, max_length=150, null=False)
    callback_url = CharField(null=True, max_length=2048)
    status = CharField(max_length=20, default=TaskStatusEnum.CONFIRM.value)
    audio_url = CharField(null=True, max_length=2048)
    caption_list = JSONField(null=True)
    duration = IntegerField(null=True)
    size = IntegerField(null=True)
    created_at = DateTimeField(default=lambda: datetime.now(timezone.utc))  # 创建时间, UTC 格式
    updated_at = DateTimeField(default=lambda: datetime.now(timezone.utc))  # 更新时间, UTC 格式
    retries = SmallIntegerField(default=0)
    remark = CharField(null=True)
    # ctrl_params 可传如 language, voice, format 等参数
    ctrl_params = JSONField(null=True)
    md5 = CharField(null=True, max_length=64)
    delete_flag = IntegerField(default=0)
    content_id = CharField(max_length=150, null=False)
    user_platform_id = CharField(max_length=150, null=True)
    point_id = CharField(null=True)
    class Meta:
        table_name = "special_markdown_task"
        database = db

    def to_vo(self):
        return {
            "task_id": self.biz_id,
            "status": self.status,
            "audio_url": self.audio_url,
            "caption_list": self.caption_list,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
            "duration": self.duration,
            "size": self.size,
            "ctrl_params": self.ctrl_params,
        }

