from peewee import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Integer<PERSON><PERSON>, Model

from src.orm.pgrepo import db
from src.util.dateUtil import get_current_time_in_beijing


class Folder(Model):
    id = AutoField(primary_key=True)
    biz_id = Char<PERSON>ield(null=False)
    name = Char<PERSON>ield(null=False)  # 文件夹名称
    parent_id = IntegerField(null=True)
    user_id = CharField(null=True)  # 文件夹所属用户
    depth = IntegerField(default=1)  # 文件夹层级
    total = IntegerField(default=0)  # 该文件夹下的子文件夹及任务的数量
    created_at = DateTimeField(default=get_current_time_in_beijing)  # 创建时间
    updated_at = DateTimeField(default=get_current_time_in_beijing)  # 更新时间
    delete_flag = IntegerField(default=0)

    class Meta:
        table_name = "folder"
        database = db  # 绑定数据库

    def to_vo(self):
        return {
            "id": self.id,
            "biz_id": self.biz_id,
            "name": self.name,
            "parent_id": self.parent_id or 0,
            "created_at": self.created_at.strftime("%Y-%m-%d %H:%M:%S") if self.created_at else None,
            "updated_at": self.updated_at.strftime("%Y-%m-%d %H:%M:%S") if self.updated_at else None,
        }


def get_folder_by_id(folder_id, user_id):
    return Folder.select().where(Folder.user_id == user_id, Folder.id == folder_id, Folder.delete_flag == 0).first()


def get_folder_dict_by_id(folder_id_list, user_id):
    if not folder_id_list:
        return {}
    arrs = (
        Folder.select(Folder.id, Folder.name)
        .where(Folder.user_id == user_id, Folder.id.in_(folder_id_list), Folder.delete_flag == 0)
        .dicts()
    )
    return {arr["id"]: arr["name"] for arr in arrs}


def get_folder_by_name(name, parent_id, user_id):
    return (
        Folder.select()
        .where(Folder.user_id == user_id, Folder.parent_id == parent_id, Folder.name == name, Folder.delete_flag == 0)
        .first()
    )
