import hashlib
import json
import math
import random
import time
import urllib
from datetime import datetime
from typing import Optional
from urllib.parse import parse_qs, urlparse
from uuid import UUID
import re
import isodate
import requests
from bs4 import BeautifulSoup
from peewee import AutoField, <PERSON><PERSON><PERSON><PERSON>, Date<PERSON><PERSON><PERSON><PERSON>, Integer<PERSON>ield, Model

from config import settings
from src.constant.wanpan import (
    ALI_WANPAN_ARG_PATTERN,
    ALI_WANPAN_REDIS_PREFIX,
    BAIDU_WANPAN_ARG_PATTERN,
    BAIDU_WANPAN_REDIS_PREFIX,
)
from src.logger.logUtil import get_logger
from src.orm.pgrepo import db
from src.util.cacheUtil import open_api_cache
from src.util.stringify import string_mark_content
from src.util.url_detail_utils import douyin_util, url_default_util

logger = get_logger(__name__)


class LectureUrlDetail(Model):
    """
    url详情
    """

    id = AutoField(primary_key=True)
    biz_id = Char<PERSON>ield(null=True)
    origin_url = CharField(null=False)  # 目标url
    source_url = CharField(null=True, max_length=4096)  # 源url
    title = CharField(null=True)  # 标题
    author_name = CharField(null=True)  # 作者名
    author_img = CharField(null=True)  # 作者头像
    pic_path = CharField(null=True, max_length=2048)  # 图片路径
    duration_minutes = IntegerField(null=True)  # 视频时长
    task_source = CharField(null=True)  # 视频来源
    tags = CharField(null=True)  # 标签
    intro = CharField(null=True)  # 简介
    remark = CharField(null=True)
    extra_params = CharField(null=True)  # 额外参数
    create_time = DateTimeField(default=datetime.now)
    update_time = DateTimeField(default=datetime.now)
    delete_flag = IntegerField(default=0)

    class Meta:
        table_name = "lecture_url_detail"
        database = db


def get_by_short_url(short_url):
    """
    Get article detail by article id
    :param article_id:  article id
    :return:  article detail
    """
    article_detail = (
        LectureUrlDetail.select()
        .where((LectureUrlDetail.origin_url == short_url) & (LectureUrlDetail.delete_flag == 0))
        .first()
    )
    if article_detail:
        return article_detail
    else:
        return None


def update_url_detail(biz, url) -> Optional[LectureUrlDetail]:
    """
    Create article detail
    :param url: article detail
    :return: article detail
    """
    try:
        article_detail = (
            LectureUrlDetail.update(origin_url=url, short_url=url, update_time=datetime.now())
            .where(LectureUrlDetail.biz_id == biz)
            .execute()
        )
        return article_detail
    except Exception:
        logger.exception("update url detail error,biz={}".format(biz))
        return None


def get_by_source_or_origin_url(source_url: str) -> Optional[LectureUrlDetail]:
    """
    Get article detail by article id
    :return:  article detail
    """
    url_detail = (
        LectureUrlDetail.select()
        .where(
            LectureUrlDetail.source_url == source_url or LectureUrlDetail.origin_url == source_url,
            LectureUrlDetail.delete_flag == 0,
        )
        .first()
    )
    if url_detail:
        return url_detail
    return None


def parse_youtube_url(url):
    # check if the URL exits
    detail = get_by_source_or_origin_url(url)
    if detail:
        return detail
    # Parse the URL
    result = urlparse(url)
    if result.netloc != "www.youtube.com" and result.netloc != "m.youtube.com":
        return None
    # get params
    params = fill_up_youtube_params(result)
    # get the video meta_data
    youtube_data = get_youtube_details(params["v"])
    # handle and save
    if youtube_data:
        return handle_and_save_youtube_url_detail(youtube_data, result, params)
    pass


def parse_xyz_url(url):
    # check if the URL exits
    detail = get_by_source_or_origin_url(url)
    if detail:
        return detail
    # Parse the URL
    result = urlparse(url)
    if result.netloc != "www.xiaoyuzhoufm.com":
        return None
    # get ep_id
    ep_id = result.path.split("/")[2]
    # get the video meta_data
    xyz_data = get_xiaoyuzhoufm_info(ep_id)
    if xyz_data:
        return handle_and_save_xyz_url_detail(xyz_data, result)


def get_xiaoyuzhoufm_info(epi):
    response = requests.get(
        f"https://www.xiaoyuzhoufm.com/episode/{epi}",
        headers={
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36"
        },
    )
    soup = BeautifulSoup(response.content, "html.parser")
    # 从<script name="schema:podcast-show" type="application/ld+json">  和 </script>中获取json数据
    script = soup.find("script", attrs={"name": "schema:podcast-show"})
    if not script:
        return {}
    # json
    datas = json.loads(script.string)
    return datas


def parse_bilibili_url(url):
    # check if the URL exits
    detail = get_by_source_or_origin_url(url)
    if detail:
        return detail
    # Parse the URL
    result = urlparse(url)
    if result.netloc != "www.bilibili.com" and result.netloc != "b23.tv" and result.netloc != "m.bilibili.com":
        return None
    # get params
    params, bv_id = fill_up_params(result)
    # get the video ID
    if not bv_id:
        bv_id = result.path.split("/")[2]
    # get the video meta_data
    bilibili_data = get_bilibili_details(bv_id)
    if bilibili_data is None:
        return None
    if bilibili_data.get("is_upower_exclusive"):
        logger.error(f"b站充电视频，不支持解析：{url}")
        return None

    # handle and save
    if bilibili_data:
        return handle_and_save_url_detail(bilibili_data, result, params)
    return None


UNKNOWN_DURATION_MINUTES = None


def remove_url_params(url: str) -> str:
    parsed_url = urlparse(url)
    clean_url = f"{parsed_url.scheme}://{parsed_url.netloc}{parsed_url.path}"
    return clean_url


def parse_douyin_url(url):
    if "douyin.com" in url and "/video/" not in url:
        parsed_url = urlparse(url)
        # 提取查询参数
        query_params = parse_qs(parsed_url.query)
        modal_id = query_params.get("modal_id", [''])[0]
        if modal_id:
            url = f"https://www.douyin.com/video/{modal_id}"

    detail = get_by_source_or_origin_url(url)
    if detail:
        return detail

    clean_url = remove_url_params(url)
    duration_minutes = 0
    author_name, author_img, title = "", "", f"抖音_{clean_url}"
    for _ in range(3):
        try:
            dy = douyin_util.DouyinDownloader(cookies_files=[])
            return_json = dy.download_json_info(url, save_json=False)
            aweme_detail = return_json.get("aweme_detail")
            if aweme_detail is None:
                raise Exception(f"解析失败：{url}")
            author_name = return_json.get("aweme_detail", {}).get("author", {}).get("nickname")
            author_img_list = return_json.get("aweme_detail", {}).get("author", {}).get("avatar_thumb", {}).get(
                "url_list",
                [])
            author_img = author_img_list[0] if author_img_list else ""
            title = return_json.get("aweme_detail", {}).get("item_title")
            duration_minutes = math.ceil(return_json.get("aweme_detail", {}).get("duration", 0) / 1000 / 60)
            break
        except Exception:
            logger.exception(f"dy地址解析错误-{_}:{url}")
            time.sleep(0.5)

    return LectureUrlDetail.create(
        biz_id=str(UUID(int=random.getrandbits(128))),
        origin_url=url,
        source_url=url,
        title=title,
        author_name=author_name,
        author_img=author_img,
        pic_path="",
        duration_minutes=duration_minutes,
        task_source="douyin",
        tags="",
        intro="",
        remark="",
        extra_params="",
    )


def get_xhs_dur(url):
    """小红书 url内容解析"""
    try:
        response = requests.get(url)
        response.raise_for_status()
        if response.text:
            split_key = '":'
            millisecond = string_mark_content(response.text, '"videoDuration"', ',', split_key)
            duration_minutes = max(math.ceil(int(millisecond) / 1000 / 60), 1)

            avatar = string_mark_content(response.text, '"avatar":', ',', split_key)

            avatar = avatar if avatar is None else avatar.replace('u002F', '').replace('"', '')

            nickname = string_mark_content(response.text, '"nickname":', ',', split_key)
            nickname = nickname if nickname is None else nickname.replace('"', '')
            return {
                'duration_minutes': duration_minutes,
                'avatar': avatar,
                'nickname': nickname
            }
        return {}
    except Exception:
        return {}


def parse_xiaohongshu_url(url):
    detail = get_by_source_or_origin_url(url)
    if detail:
        return detail
    clean_url = remove_url_params(url)
    xhs_data = url_default_util.get_xhs_dur(url)
    if xhs_data is None:
        return None
    if not xhs_data.get("video"):
        return None
    duration_minutes = xhs_data.get('duration_minutes')
    nickname = xhs_data.get('nickname')
    avatar = xhs_data.get('avatar')
    return LectureUrlDetail.create(
        biz_id=str(UUID(int=random.getrandbits(128))),
        origin_url=url,
        source_url=url,
        title=f"小红书_{clean_url}",
        author_name=nickname,
        author_img=avatar,
        pic_path="",
        duration_minutes=duration_minutes,
        task_source="xiaohongshu",
        tags="",
        intro="",
        remark="",
        extra_params="",
    )


def parse_kuaishou_url(url):
    detail = get_by_source_or_origin_url(url)
    if detail:
        return detail
    clean_url = remove_url_params(url)
    try:
        data = url_default_util.get_ks_dur(clean_url)
    except Exception:
        logger.exception(f"快手链接解析错误：{url}")
        return None
    duration_minutes = data.get('duration_minutes', 0)
    nickname = data.get('nickname')
    avatar = data.get('avatar')
    title = data.get('title')
    return LectureUrlDetail.create(
        biz_id=str(UUID(int=random.getrandbits(128))),
        origin_url=url,
        source_url=url,
        title=title,
        author_name=nickname,
        author_img=avatar,
        pic_path="",
        duration_minutes=duration_minutes,
        task_source="kuaishou",
        tags="",
        intro="",
        remark="",
        extra_params="",
    )


def parse_baidu_url(url):
    if match := BAIDU_WANPAN_ARG_PATTERN.search(url):
        fs_id = match.group(1)
    else:
        return None
    baidu_wanpan_fileinfos = open_api_cache.get(f"{BAIDU_WANPAN_REDIS_PREFIX}{fs_id}") or {}

    # 视频
    if baidu_wanpan_fileinfos.get("category") == 1:
        duration_minutes = math.ceil((baidu_wanpan_fileinfos.get("duration") or 0) / 60)
    elif baidu_wanpan_fileinfos.get("category") == 2:
        duration_minutes = math.ceil((baidu_wanpan_fileinfos.get("duration") or 0) / 60)
    else:
        return None
    vip = baidu_wanpan_fileinfos.get("vid", {})
    media_info = (baidu_wanpan_fileinfos.get("media_info") or {}).get("meta_info")
    try:
        media_info = json.loads(media_info)
        media_info.update({
            "is_wangpan_vip": True if vip.get("vip_type", -1) in [1, 2] else False,
            "vip_type": vip.get("vip_type", -1)
        })
    except Exception:
        logger.exception(media_info)
        media_info = {
            "is_wangpan_vip": True if vip.get("vip_type", -1) in [1, 2] else False,
            "vip_type": vip.get("vip_type", -1)
        }

    return LectureUrlDetail.create(
        biz_id=str(UUID(int=random.getrandbits(128))),
        origin_url=url,
        source_url=baidu_wanpan_fileinfos.get("download_url"),
        title=baidu_wanpan_fileinfos.get("filename"),
        author_name="",
        author_img="",
        pic_path="",
        duration_minutes=duration_minutes,
        task_source="baidupan",
        tags="",
        intro="",
        remark="",
        # 这个信息见 Insomnia 的 file_download_url(baidupan) 的 Docs
        extra_params=json.dumps(media_info),
    )


def parse_alipan_url(url):
    if match := ALI_WANPAN_ARG_PATTERN.search(url):
        drive_id, file_id = match.group(1), match.group(2)
    else:
        return None
    ali_wanpan_fileinfos = open_api_cache.get(f"{ALI_WANPAN_REDIS_PREFIX}{drive_id}_{file_id}") or {}
    duration_minutes = math.ceil(float((ali_wanpan_fileinfos.get("video_media_metadata") or {}).get("duration") or 0))
    if not duration_minutes:
        duration_minutes = math.ceil(
            float((ali_wanpan_fileinfos.get("video_preview_metadata") or {}).get("duration") or 0)
        )
    vip = ali_wanpan_fileinfos.get("vip", {})
    extra_params = {
        "category": ali_wanpan_fileinfos.get("category"),
        "mime_type": ali_wanpan_fileinfos.get("mime_type"),
        "file_extension": ali_wanpan_fileinfos.get("file_extension"),
        "method": ali_wanpan_fileinfos.get("method"),
        "expiration": ali_wanpan_fileinfos.get("expiration"),
        "description": ali_wanpan_fileinfos.get("description"),
        "is_wangpan_vip": True if vip.get("identity", "") in ["vip", "svip"] else False,
        "identity": vip.get("identity", "")
    }
    return LectureUrlDetail.create(
        biz_id=str(UUID(int=random.getrandbits(128))),
        origin_url=url,
        source_url=ali_wanpan_fileinfos.get("download_url"),
        title=ali_wanpan_fileinfos.get("name"),
        author_name="",
        author_img="",
        pic_path="",
        duration_minutes=math.ceil(duration_minutes / 60),
        task_source="alipan",
        tags="",
        intro="",
        remark="",
        extra_params=json.dumps(extra_params, ensure_ascii=False),
    )


def handle_and_save_xyz_url_detail(xyz_data, result):
    # path
    source_url = result.geturl()
    duration_str = xyz_data.get("timeRequired")
    if duration_str:
        duration = int(duration_str.split("T")[1].split("M")[0])
    else:
        duration = 1
    title = xyz_data.get("name")
    author = xyz_data.get("author")
    intro = (xyz_data.get("description") or "")[:3000]
    return LectureUrlDetail.create(
        biz_id=str(UUID(int=random.getrandbits(128))),
        origin_url=source_url,
        source_url=source_url,
        title=title,
        author_name=author,
        author_img="",
        pic_path="",
        duration_minutes=duration,
        task_source="xiaoyuzhou",
        tags="",
        intro=intro,
        remark="",
        extra_params=xyz_data.get("associatedMedia"),
    )
    pass


def handle_and_save_url_detail(bilibili_data, result, params):
    # path
    path = result.path.rstrip("/")
    source_url = result.geturl()
    # 如果视频数量大于1
    # 视频时长
    duration = max(int(bilibili_data["duration"] / 60), 1)
    title = bilibili_data["title"]
    aid = bilibili_data["aid"]
    bid = bilibili_data["bvid"]
    cid = bilibili_data["cid"]
    url = result._replace(
        path=path, query="&".join([f"{k}={v[0] if isinstance(v, list) and v else v}" for k, v in params.items()])
    ).geturl()
    if bilibili_data["videos"] > 1:
        # 获取视频信息
        for item in bilibili_data["pages"]:
            if item["page"] == int(params["p"]):
                duration = max(int(item["duration"] / 60), 1)
                title = item["part"]
                cid = item["cid"]
                break
    intro = bilibili_data["desc"][:3000]
    return LectureUrlDetail.create(
        biz_id=str(UUID(int=random.getrandbits(128))),
        origin_url=url,
        source_url=source_url,
        title=title,
        author_name=bilibili_data["owner"]["name"],
        author_img=bilibili_data["owner"]["face"],
        pic_path=bilibili_data["pic"],
        duration_minutes=duration,
        task_source="bilibili",
        tags=bilibili_data["tname"],
        intro=intro,
        remark=bid,
        extra_params=f"aid={aid}&bvid={bilibili_data['bvid']}&cid={cid}",
    )


def handle_and_save_youtube_url_detail(youtube_data, result, params):
    # path
    path = result.path.rstrip("/")
    source_url = result.geturl()
    # 如果视频数量大于1
    # 视频时长

    items = youtube_data.get("items", [])

    if not items:
        logger.warning(f"No details found for video ID: {source_url}")
        return None

    content_details = items[0]["contentDetails"]
    snippet = items[0]["snippet"]
    # 将snippet['tags'] 转换为字符串，以， 分割
    tagStr = ",".join(snippet["tags"]) if "tags" in snippet else ""
    durationObj = isodate.parse_duration(content_details["duration"])
    # Limit intro to 3000 characters
    intro = snippet["description"][:3000]
    duration = max(int(durationObj.total_seconds() / 60), 1)

    url = result._replace(
        path=path, query="&".join([f"{k}={v[0] if isinstance(v, list) and v else v}" for k, v in params.items()])
    ).geturl()

    return LectureUrlDetail.create(
        biz_id=str(UUID(int=random.getrandbits(128))),
        origin_url=url,
        source_url=source_url,
        title=snippet["title"],
        author_name=snippet["channelTitle"],
        author_img="",
        pic_path=snippet["thumbnails"]["default"]["url"],
        duration_minutes=duration,
        task_source="youtube",
        tags=tagStr,
        intro=intro,
        remark=items[0]["id"],
        extra_params="",
    )


def fill_up_params(result) -> (dict, str):
    """
    Fill up the query parameters for the given URL
    :param url: URL to fill up the query parameters for
    :return: Dictionary containing the query parameters
    """
    query_params = parse_qs(result.query)

    bv_id = query_params.get("bvid")
    if bv_id:
        bv_id = bv_id[0]

    if "p" in query_params:
        query_params = {"p": query_params["p"]}
    else:
        query_params = {}
    p_value = int(query_params.get("p", [1])[0])
    query_params["p"] = str(p_value)

    return query_params, bv_id


def fill_up_youtube_params(result) -> dict:
    query_params = parse_qs(result.query)
    if "v" in query_params:
        query_params = {"v": query_params["v"]}
    else:
        raise ValueError("Invalid YouTube URL")
    return query_params


def save_origin_url(origin_url, source):
    """
    Save article detail
    :param article_detail: article detail
    :return: article detail
    """
    try:
        article_detail = LectureUrlDetail.create(
            biz_id=str(UUID(int=random.getrandbits(128))),
            origin_url=origin_url,
            source_url=origin_url,
            title="",
            author_name="",
            author_img="",
            pic_path="",
            duration_minutes=0,
            task_source=source,
            tags="",
            intro="",
            remark="",
            extra_params="",
        )
        return article_detail
    except Exception:
        logger.exception("save url detail error,data={}".format(origin_url))
        return


def get_by_origin_url(origin_url):
    """
    Get article detail by article id
    :param origin_url:
    :return:
    """
    article_detail = (
        LectureUrlDetail.select()
        .where((LectureUrlDetail.origin_url == origin_url) & (LectureUrlDetail.delete_flag == 0))
        .first()
    )
    if article_detail:
        return article_detail
    else:
        return None


def get_bvid_from_av(av_id):
    url = f"https://api.bilibili.com/x/web-interface/view?aid={av_id}"
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.150 Safari/537.36'
    }

    response = requests.get(url, headers=headers)
    if response.status_code == 200:
        data = response.json()
        if 'data' in data and 'bvid' in data['data']:
            return data['data']['bvid']
        else:
            return av_id
    else:
        return av_id


def get_bilibili_details(bv_id: str):
    """
    获取bilibili视频时长,单位分钟
    :param p: 分p
    :param bv_id: 视频id
    :return:
    """
    try:
        # 如果是 av 换一下 bv
        pattern = r'av\d*'
        match = re.match(pattern, bv_id.lower())
        if match:
            bv_id = get_bvid_from_av(int(bv_id[2:]))
        # b战视频api访问会多次会被ban Ip，需要登录?
        # 获取视频信息
        # '获取最新的 img_key 和 sub_key'
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3",
            "Referer": "https://www.bilibili.com/",
        }
        params = appsign({"bvid": bv_id}, "1d8b6e7d45233436", "560c52ccd288fed045859ed18bffd973")
        query = urllib.parse.urlencode(params)
        # 控制3s超时时间
        resp = requests.get("https://api.bilibili.com/x/web-interface/view?" + query, headers=headers, timeout=3)
        data = resp.json()
        if data["code"] == 0:
            return data["data"]
    except Exception:
        logger.exception(f"get bilibili duration error ,bv_id={bv_id}")
    return None


def appsign(params, appkey, appsec):
    "为请求参数进行 APP 签名"
    params.update({"appkey": appkey})
    params = dict(sorted(params.items()))  # 按照 key 重排参数
    query = urllib.parse.urlencode(params)  # 序列化参数
    sign = hashlib.md5((query + appsec).encode()).hexdigest()  # 计算 api 签名
    params.update({"sign": sign})
    return params


def get_youtube_details(video_id):
    """
    Fetches YouTube video details including duration and title.

    :param video_id: YouTube video ID
    :return: Tuple containing duration in minutes and video title
    """
    try:
        api_url = "https://googleapis.zimuzu.org/youtube/v3/videos"
        params = {
            "id": video_id,
            "key": settings.GOOGLE_YOUTUBE_API,
            "part": "contentDetails,snippet",
        }
        response = requests.get(api_url, params=params, timeout=3)

        if response.status_code != 200:
            logger.error(f"Failed to fetch YouTube details, status code: {response.status_code}")
            return None

        return response.json()
    except requests.exceptions.RequestException as e:
        logger.exception(f"Request exception for video ID: {video_id} - {e}")
    except Exception as e:
        logger.exception(f"Unexpected error for video ID: {video_id} - {e}")
    return None


if __name__ == "__main__":
    parse_xyz_url("640ee2438be5d40013fe4a87")
