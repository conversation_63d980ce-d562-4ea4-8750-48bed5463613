from typing import List

from pydantic import BaseModel


class ResourceVO(BaseModel):
    original_type: str
    algorithm: str
    ciphertext: str
    associated_data: str
    nonce: str


class EventVO(BaseModel):
    id: str
    create_time: str
    resource_type: str
    event_type: str
    summary: str
    resource: ResourceVO


class SceneInfo(BaseModel):
    device_id: str


class Amount(BaseModel):
    payer_total: int
    total: int
    currency: str
    payer_currency: str


class GoodsDetail(BaseModel):
    goods_remark: str
    quantity: int
    discount_amount: int
    goods_id: str
    unit_price: int


class PromotionDetail(BaseModel):
    amount: int
    wechatpay_contribute: int
    coupon_id: str
    scope: str
    merchant_contribute: int
    name: str
    other_contribute: int
    currency: str
    stock_id: str
    goods_detail: List[GoodsDetail]


class Payer(BaseModel):
    openid: str


class WechatNotification(BaseModel):
    """
    https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter3_1_5.shtml
    支付结果通知
    """

    transaction_id: str
    amount: Amount
    mchid: str
    trade_state: str
    bank_type: str
    promotion_detail: List[PromotionDetail]
    success_time: str
    payer: Payer
    out_trade_no: str
    appid: str
    trade_state_desc: str
    trade_type: str
    attach: str
    scene_info: SceneInfo
