from datetime import datetime

from peewee import <PERSON>Field, <PERSON><PERSON><PERSON><PERSON>, DateTimeField, IntegerField, Model, BooleanField, TextField
from playhouse.postgres_ext import <PERSON><PERSON><PERSON>ield
from src.util import stringify
from src.orm.pgrepo import db


class TempApprovalConfig(Model):
    """审批配置表"""
    id = AutoField(primary_key=True)
    biz_id = CharField(null=False, max_length=128)
    request_type = CharField(null=False, max_length=50, unique=True)
    name = Char<PERSON>ield(null=False, max_length=100)
    steps = JSONField(null=False)
    is_active = BooleanField(default=True)
    create_time = DateTimeField(default=datetime.now)
    update_time = DateTimeField(default=datetime.now)
    delete_flag = IntegerField(default=0)

    class Meta:
        table_name = "approval_configs"
        database = db


class TempApprovalRequest(Model):
    """审批请求表"""
    id = AutoField(primary_key=True)
    biz_id = Char<PERSON>ield(null=False, max_length=128)
    request_type = Char<PERSON>ield(null=False, max_length=50)
    requester_id = Char<PERSON>ield(null=False, max_length=128, index=True)
    title = CharField(null=False, max_length=200)
    content = JSONField(null=False)
    status = CharField(null=False, max_length=128)
    current_step = IntegerField(default=1)
    create_time = DateTimeField(default=datetime.now)
    update_time = DateTimeField(default=datetime.now)
    delete_flag = IntegerField(default=0)

    class Meta:
        table_name = "approval_requests"
        database = db


class TempApprovalProcess(Model):
    """审批表"""
    id = AutoField(primary_key=True)
    biz_id = CharField(null=False, max_length=128)
    approval_id = CharField(null=False, max_length=128)
    approver_id = CharField(null=False, max_length=128, index=True)
    step = IntegerField(default=1)
    comment = CharField(null=True, max_length=255)
    status = CharField(null=False, max_length=128)
    action_time = DateTimeField(null=True)
    last_step = BooleanField(default=True)
    create_time = DateTimeField(default=datetime.now)
    update_time = DateTimeField(default=datetime.now)
    delete_flag = IntegerField(default=0)

    class Meta:
        table_name = "approval_process"
        database = db


class TempAppVersion(Model):
    """审批表"""
    id = AutoField(primary_key=True)
    platform = CharField(null=False, max_length=30, index=True)
    version_name = CharField(null=False, max_length=50)
    version_code = IntegerField(null=False, index=True)
    is_force_update = BooleanField(default=False)
    content = TextField(null=True)
    download_url = CharField(null=False, max_length=300)
    is_active = BooleanField(default=True)
    create_time = DateTimeField(default=datetime.now)
    update_time = DateTimeField(default=datetime.now)
    create_by = CharField(null=True, max_length=128)
    update_by = CharField(null=True, max_length=128)
    delete_flag = IntegerField(default=0)

    class Meta:
        table_name = "app_versions"
        database = db

        indexes = (
            (('platform', 'version_code'), True),
        )


class TempShare(Model):
    """笔记分享"""
    id = AutoField(primary_key=True)
    biz_id = CharField(null=False, max_length=128, index=True, unique=True)
    task_id = CharField(null=False, max_length=128)
    name = CharField(null=True, max_length=2048)
    share_token = CharField(index=True, unique=True, max_length=64)
    user_id = CharField(null=False, max_length=128)
    share_type = CharField(max_length=20, default="public")
    encryption_key = CharField(null=True, max_length=10)
    access_limit = IntegerField(null=False, default=-1)
    access_count = IntegerField(null=False, default=0)
    points = IntegerField(null=False, default=0)
    expiry_date = DateTimeField(null=True)
    is_active = BooleanField(default=True)

    create_time = DateTimeField(default=datetime.now)
    update_time = DateTimeField(default=datetime.now)
    delete_flag = IntegerField(default=0)

    class Meta:
        table_name = "shares"
        database = db


class TempShareAccess(Model):
    """访问记录"""
    id = AutoField(primary_key=True)
    share_id = CharField(null=False, max_length=128)
    access_time = DateTimeField(null=True, default=datetime.now)
    ip_address = CharField(null=True, max_length=50)
    user_agent = JSONField(null=True)
    registered = BooleanField(default=True)
    user_id = CharField(null=True, max_length=128)
    create_time = DateTimeField(default=datetime.now)
    update_time = DateTimeField(default=datetime.now)
    delete_flag = IntegerField(default=0)

    class Meta:
        table_name = "share_accesses"
        database = db


class TempUserRoleModel(Model):
    """用户角色"""
    id = AutoField(primary_key=True)
    user_id = CharField(null=False, max_length=128)
    role_id = CharField(null=False, max_length=128)
    create_time = DateTimeField(default=datetime.now)
    update_time = DateTimeField(default=datetime.now)
    delete_flag = IntegerField(default=0)

    class Meta:
        table_name = "lecture_user_roles"
        database = db


class TempRoleModel(Model):
    """角色表"""
    __tablename__ = 'roles'

    id = AutoField(primary_key=True)
    biz_id = CharField(null=False, max_length=128, index=True, unique=True)
    name = CharField(null=False, unique=True, max_length=50)  # 角色名称
    label = CharField(null=False, unique=True, max_length=50)  # 角色标识
    permissions = JSONField(null=True)
    create_time = DateTimeField(default=datetime.now)
    update_time = DateTimeField(default=datetime.now)
    create_by = CharField(null=True, max_length=128)
    update_by = CharField(null=True, max_length=128)
    delete_flag = IntegerField(default=0)

    class Meta:
        table_name = "roles"
        database = db


class TempPermissionModel(Model):
    """权限表"""
    __tablename__ = 'permissions'

    id = AutoField(primary_key=True)
    name = CharField(null=False, unique=True, max_length=50)  # 权限名称
    label = CharField(null=False, unique=True, max_length=50)  # 权限标识

    class Meta:
        table_name = "permissions"
        database = db


def init_approval_config():
    request_type = 'action_open_pay_point'
    approval_config = TempApprovalConfig.select().where(
        TempApprovalConfig.request_type == request_type
    ).first()
    biz_id = stringify.get_uid()
    if not approval_config:
        TempApprovalConfig.create(
            biz_id=biz_id,
            request_type=request_type,
            name="开放平台用户充值审批流",
            steps=[{
                "approver_id": "open_user_c149c2a5-bd83-161f-9728-8b006ce64725",
                "step": 1, "last_step": True}]
        )
