from typing import Optional


class AffiliatesProduct:
    """
    set the distribution percent of the product
    """

    @staticmethod
    def get_by_product_id(product_id) -> Optional[int]:
        """
        get the distribution percent of the product
        :param product_id: only support ProductID.SUBSCRIBE_STANDARD, ProductID.SUBSCRIBE_VIP, ProductID.POINTS
        :return: percent of the distribution
        """
        data = {
            "plan_53d6e361-5145-1a45-a61a-0889e30c4a21": 100,  # 月卡
            "plan_d8110071-4181-b688-980a-37755af9909f": 400,  # 年卡
            "plan_332c832d-7192-40ff-ef14-d537a53223d7": 1000,  # 2年卡
            "plan_332c832d-7192-40ff-ef14-d537a532773d": 2500,  # 5年卡
            "plan_332c832d-7192-40ff-ef14-d5372g32233d": 6000,  # 终身
        }
        return data.get(product_id, 0)
