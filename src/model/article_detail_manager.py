import asyncio
import random
from datetime import datetime
from typing import Optional
from uuid import UUID

from peewee import (
    AutoField,
    CharField,
    DatabaseError,
    DateTimeField,
    IntegerField,
    IntegrityError,
    Model,
    SmallIntegerField,
    TextField,
    TimeField,
)
from playhouse.postgres_ext import <PERSON><PERSON><PERSON><PERSON>

from src.logger.logUtil import get_logger
from src.orm.pgrepo import db
from src.util.dateUtil import getTimeBySeconds
from src.util.pandoc_util import convert_latex_in_markdown

logger = get_logger(__name__)


class LectureArticleDetail(Model):
    id = AutoField(primary_key=True)
    biz_id = CharField(null=False, unique=True)
    article_id = CharField(null=False)
    user_id = CharField(null=False, default="system")
    index = IntegerField(null=True)
    start_time = TimeField(default="00:00:00")
    end_time = TimeField(default="23:59:59")
    origin_text = TextField(null=True)
    speaker = <PERSON><PERSON><PERSON><PERSON>(null=True)
    translated_modified_text_json = J<PERSON><PERSON>ield(null=True)
    oss_pic_path = TextField(null=True)
    local_pic_path = TextField(null=True)
    pic_keywords = TextField(null=True)
    create_time = DateTimeField(default=datetime.now)
    update_time = DateTimeField(default=datetime.now)
    delete_flag = SmallIntegerField(default=0)

    class Meta:
        table_name = "lecture_article_detail"
        database = db

    def to_vo(self, out_language, language):
        return {
            "biz_id": self.biz_id,
            "index": self.index,
            "start_time": self.start_time,
            "end_time": self.end_time,
            # 用户选择语言未润色的 text, 这里取值为 origin_text 为命名问题
            "out_language_origin_text": convert_latex_in_markdown(self.origin_text),
            "out_language_modified_text": convert_latex_in_markdown(
                (self.translated_modified_text_json or {}).get(out_language) or ""
            ),
            "modified_text": convert_latex_in_markdown((self.translated_modified_text_json or {}).get(language) or ""),
            "speaker": self.speaker,
            "oss_pic_path": self.oss_pic_path,
        }

    def to_open_vo(self, out_language):
        return {
            "biz_id": self.biz_id,
            "index": self.index,
            "start_time": self.start_time,
            "end_time": self.end_time,
            # 用户选择语言未润色的 text, 这里取值为 origin_text 为命名问题
            "origin_text": self.origin_text,
            "out_language_modified_text": (self.translated_modified_text_json or {}).get(out_language) or "",
            "speaker": self.speaker,
            "oss_pic_path": self.oss_pic_path,
        }


def get_all_details_by_article_biz_id(article_biz_id):
    return LectureArticleDetail.select().where(
        (LectureArticleDetail.article_id == article_biz_id) & (LectureArticleDetail.delete_flag == 0)
    )


def get_article_detail_by_biz_id(biz_id):
    return (
        LectureArticleDetail.select()
        .where((LectureArticleDetail.biz_id == biz_id) & (LectureArticleDetail.delete_flag == 0))
        .first()
    )


def get_article_detail_by_id(article_id):
    """
    Get article detail by article id
    :param article_id:  article id
    :return:  article detail
    """
    article_detail = (
        LectureArticleDetail.select()
        .where((LectureArticleDetail.article_id == article_id) & (LectureArticleDetail.delete_flag == 0))
        .first()
    )
    if article_detail:
        return article_detail
    else:
        return None


def delete_article_detail_by_id(article_biz_id):
    """
    Delete article detail by article biz id
    :param article_biz_id:
    :return:
    """
    try:
        LectureArticleDetail.update(delete_flag=1).where(
            (LectureArticleDetail.article_id == article_biz_id) & (LectureArticleDetail.delete_flag == 0)
        ).execute()
    except Exception:
        logger.exception(f"Error deleting article detail，article_id={article_biz_id}")


def create_article_detail(**kwargs):
    try:
        start_time = getTimeBySeconds(kwargs.get("start_time"))
        end_time = getTimeBySeconds(kwargs.get("end_time"))
        biz_id = str(UUID(int=random.getrandbits(128)))

        article_detail = LectureArticleDetail.create(
            biz_id=biz_id,
            article_id=kwargs.get("article_id"),
            start_time=start_time,
            end_time=end_time,
            speaker_text=kwargs.get("speaker_text"),
            translation=kwargs.get("translation"),
            origin_text=kwargs.get("modified_speaker_text"),
            summary_note=kwargs.get("section_note"),
            oss_pic_path=kwargs.get("oss_pic_path"),
        )
        return article_detail  # Return the created instance
    except IntegrityError as e:
        logger.exception(f"Error creating article detail，article_id={kwargs.get('article_id')}", e)
        return None
    except DatabaseError as e:
        logger.exception(f"Error creating article detail，article_id={kwargs.get('article_id')}", e)
        return None
    except Exception as e:
        logger.exception(f"Error creating article detail，content={kwargs}", e)
        return None


def update_article_detail_by_section(
    section,
    article_id,
    input_language="zh",
    output_language="zh",
):
    """
    Update article detail from section
    """
    if not section or not article_id:
        return

    try:
        article_detail = LectureArticleDetail.get(LectureArticleDetail.biz_id == section.biz_id)

        translated_modified_text_json = article_detail.translated_modified_text_json or {}
        if section.out_language_modified_text:
            translated_modified_text_json[output_language] = section.out_language_modified_text
        if section.modified_text and input_language != output_language:
            translated_modified_text_json[input_language] = section.modified_text
        article_detail.translated_modified_text_json = translated_modified_text_json
        article_detail.save()
    except LectureArticleDetail.DoesNotExist:
        logger.warning(f"Article detail not found, biz_id={section.biz_id}")


def create_article_detail_by_section(section, article_id, out_language="zh") -> Optional[LectureArticleDetail]:
    """
    Create article detail from section
    """
    if not section or not article_id:
        return

    try:
        if section.biz_id:
            count = LectureArticleDetail.select().where(LectureArticleDetail.biz_id == section.biz_id).count()
            if count >= 0:
                logger.warning(f"Article detail already exists, biz_id={section.biz_id}")
                return
        if not section.biz_id:
            # 这里避免重复推送
            db_section = (
                LectureArticleDetail.select()
                .where(
                    LectureArticleDetail.article_id == article_id,
                    LectureArticleDetail.index == section.index,
                )
                .first()
            )
            if db_section:
                logger.warning(f"Article detail already exists, biz_id={db_section.biz_id}")
                return
        biz_id = str(UUID(int=random.getrandbits(128)))
        timer_start_time = getTimeBySeconds(section.start_time)
        timer_end_time = getTimeBySeconds(section.end_time)
        article_detail = LectureArticleDetail.create(
            biz_id=biz_id,
            article_id=article_id,
            start_time=timer_start_time,
            end_time=timer_end_time,
            origin_text=section.text,
            oss_pic_path=section.oss_pic_path,
            speaker=section.speaker,
            index=section.index,
            pic_keywords=section.pic_keywords,
            local_pic_path=section.local_pic_path,
        )
        return article_detail
    except IntegrityError as e:
        logger.exception(f"Error creating article detail, article_id={article_id}", e)
    except DatabaseError as e:
        logger.exception(f"Error creating article detail, article_id={article_id}", e)
    except Exception as e:
        logger.exception(f"Error creating article detail, content={article_id}", e)
    return


async def create_article_detail_by_section_list(sectionArr: list, article_id: str):
    """
    Create article detail list from section array
    """
    if not sectionArr or not article_id:
        return

    # delete_article_detail_by_id(article_id)

    tasks = []
    for section in sectionArr:
        tasks.append(create_article_details(section, article_id))

    await asyncio.gather(*tasks)


async def create_article_details(section, article_id):
    try:
        biz_id = str(UUID(int=random.getrandbits(128)))
        timer_start_time = getTimeBySeconds(section.start_time)
        timer_end_time = getTimeBySeconds(section.end_time)
        article_detail = LectureArticleDetail.create(
            biz_id=biz_id,
            article_id=article_id,
            start_time=timer_start_time,
            end_time=timer_end_time,
            origin_text=section.text,
            speaker_text=section.modified_text,
            out_language_speaker_text=section.out_language_modified_text,
            summary_note=section.section_note,
            out_language_summary_note=section.out_language_section_note,
            oss_pic_path=section.oss_pic_path,
            speaker=section.speaker,
        )
        return article_detail
    except IntegrityError as e:
        logger.exception(f"Error creating article detail, article_id={article_id}", e)
    except DatabaseError as e:
        logger.exception(f"Error creating article detail, article_id={article_id}", e)
    except Exception as e:
        logger.exception(f"Error creating article detail, content={article_id}", e)
    return None


async def article_generator():
    pass
