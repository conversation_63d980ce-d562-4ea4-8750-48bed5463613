import random
from datetime import datetime
from enum import IntEnum
from typing import Optional
from uuid import UUID

from peewee import AutoField, CharField, DateTimeField, IntegerField, Model, SmallIntegerField
from pydantic import BaseModel, Field

from src.logger.logUtil import get_logger
from src.orm.pgrepo import db

logger = get_logger(__name__)


class AffiliatesApply(BaseModel):
    contact: str = Field(..., min_length=1)  # Ensure contact is not empty
    occupation: Optional[str] = Field(None, max_length=50)
    reason: Optional[str] = Field(None, max_length=200)
    socialMediaLink: Optional[str] = Field(None, max_length=200)
    token: Optional[str] = Field(None, max_length=8)


class AffiliatesStatus(IntEnum):
    """
    Affiliates status, 0 pending, 1 success, 2 fail
    its status is pending when it is created,
    its status is success when admin audit it and pass,
    its status is fail when admin audit it and not pass
    :see src.api.affiliates_api.audit_affiliates
    """

    PENDING = 0
    SUCCESS = 1
    FAIL = 2


class AffiliatesPrize(IntEnum):
    """
    Affiliates prize type, 1 积分 2 金额
    """

    POINT = 1
    AMOUNT = 2


class Affiliates(Model):
    """
    分销商
    """

    id = AutoField(primary_key=True)  # 自增id
    biz_id = CharField(null=False, unique=True)  # 分销商id
    contact = CharField(null=False)  # 联系方式
    user_id = CharField(null=False, default="SystemCreate")  # 用户id，可能是用户id，也可能是系统创建的
    status = SmallIntegerField(default=AffiliatesStatus.PENDING)  # 状态 0 审核中 1 审核通过 2 审核不通过
    bind_token = CharField(null=False, default=str(UUID(int=random.getrandbits(32))))  # 绑定token
    fans_count = IntegerField(default=0)  # 粉丝数量
    total_point = IntegerField(default=0)  # 总积分
    create_time = DateTimeField(default=datetime.now)
    update_time = DateTimeField(default=datetime.now)
    remark = CharField(null=True)  # 备注
    delete_flag = SmallIntegerField(default=0)

    class Meta:
        table_name = "affiliates"
        database = db


def create_affiliates(**kwargs):
    """
    Create distribution
    :param kwargs: distribution info
    """
    aff = Affiliates.create(
        biz_id=kwargs.get("bind_token") or str(UUID(int=random.getrandbits(128))),
        user_id=kwargs.get("user_id"),
        contact=kwargs.get("contact"),
        status=AffiliatesStatus.SUCCESS,
        remark=kwargs.get("remark"),
        bind_token=kwargs.get("bind_token"),
    )
    return aff.bind_token


async def create_affiliates_by_user_id(
    user_id: str, contact: str, bind_token: Optional[str] = None, remark: Optional[str] = None
):
    """
    Create distribution by user id
    :param remark:  备注
    :param bind_token: invitation token
    :param contact: contact information including phone number or email
    :param user_id: user id
    :return:
    """
    return create_affiliates(user_id=user_id, contact=contact, bind_token=bind_token, remark=remark)


async def get_affiliates_by_user_id(user_id: str) -> Optional[Affiliates]:
    """
    Query distribution by user id
    :param user_id: user id
    :return: distribution
    """
    affiliates = Affiliates.select().where(Affiliates.user_id == user_id, Affiliates.delete_flag == 0).get_or_none()
    return affiliates


def get_affiliates_by_token(token) -> Optional[Affiliates]:
    """
    Query distribution by token
    :param token: affiliate token
    :return: affiliate
    """
    affiliates = Affiliates.select().where(Affiliates.bind_token == token, Affiliates.delete_flag == 0).first()
    return affiliates


def get_affiliates_by_biz_id(biz_id) -> Optional[Affiliates]:
    """
    Query distribution by token
    :param biz_id:affiliate biz_id
    :return: affiliate
    """
    affiliates = (
        Affiliates.select()
        .where(Affiliates.biz_id == biz_id, Affiliates.status == AffiliatesStatus.SUCCESS, Affiliates.delete_flag == 0)
        .first()
    )
    return affiliates


async def audit_affiliates_by_biz_id(biz_id, status):
    """
    Query distribution by biz id
    :param biz_id: distribution id
    :param status: distribution status
    @see AffiliatesStatus
    :return: distribution
    """
    query = Affiliates.update(status=status).where(
        Affiliates.biz_id == biz_id, Affiliates.status == AffiliatesStatus.PENDING
    )
    query.execute()
