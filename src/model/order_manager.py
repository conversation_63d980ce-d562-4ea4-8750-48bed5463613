import asyncio
import random
import re
from datetime import datetime, timedelta
from enum import IntEnum
from typing import Optional

from peewee import AutoField, CharField, DateTimeField, DecimalField, IntegerField, Model
from playhouse.postgres_ext import <PERSON><PERSON><PERSON><PERSON>
from playhouse.shortcuts import model_to_dict
from pydantic import BaseModel

from src.logger.logUtil import get_logger
from src.model.User_action_record_manager import UserActionProcess, UserActionType, add_user_action_record
from src.model.affiliates_order import create_affiliates_order_and_distribute
from src.model.lecture_plan_manager import get_lecture_plan_by_biz_id
from src.model.member_manager import (
    LectureMember,
    MembershipType,
    get_lecture_member_by_user_id,
    membership_initial_unlock,
)
from src.orm.pgrepo import db
from src.util.dateUtil import get_beijing_time

logger = get_logger(__name__)


class ProductID(IntEnum):
    SUBSCRIBE_STANDARD = 1
    SUBSCRIBE_VIP = 2
    POINTS = 3


class PaymentType(IntEnum):
    HPJ_WECHAT = 1
    PADDLE = 2
    WE_CHAT = 3
    ALIPAY = 4
    MOBILE_ALIPAY = 6
    EXCHANGE_CODE = 5
    APPLE_PAY = 7  # 苹果内购


class OrderStatus(IntEnum):
    SUCCESS = 1  # 支付成功
    PENDING = 2  # 待支付
    CANCELED = 3  # 已取消
    REFUNDED = 4  # 已退款


hpj_order_status = {"OD": OrderStatus.SUCCESS, "WP": OrderStatus.PENDING, "CD": OrderStatus.CANCELED}

products = {
    ProductID.SUBSCRIBE_STANDARD: {  # 普通用户
        "100积分": 9.9,
        "450积分": 39,
        "1400积分": 109,
    },
    ProductID.SUBSCRIBE_VIP: {  # VIP用户
        "2800积分": 199,
        "9000积分": 559,
        "16000积分": 999,
    },
}

titles = {
    ProductID.SUBSCRIBE_STANDARD: "普通会员",
    ProductID.SUBSCRIBE_VIP: "高级会员",
}

membership_days = {
    ProductID.SUBSCRIBE_STANDARD: {  # 普通用户
        "100积分": 30,
        "450积分": 90,
        "1400积分": 365,
    },
    ProductID.SUBSCRIBE_VIP: {  # VIP用户
        "2800积分": 30,
        "9000积分": 90,
        "16000积分": 365,
    },
}


class LectureOrders(Model):
    """
    订单表，包含订单信息
    """

    id = AutoField(primary_key=True)  # 主键，自增序列
    user_id = CharField(null=False)  # 用户ID
    product_id = CharField(max_length=64, null=False)  # 产品ID
    option = CharField(max_length=32, null=True)  # 选项
    order_id = CharField(max_length=64, unique=True, null=True)  # 订单ID
    transaction_id = CharField(max_length=32, null=True)  # 交易ID
    amount = DecimalField(max_digits=10, decimal_places=2, null=False)  # 金额
    status = IntegerField(null=False)  # 状态
    created_at = DateTimeField(default=datetime.now)  # 创建时间
    updated_at = DateTimeField(default=datetime.now)  # 更新时间
    payment_type = IntegerField(null=True)  # 支付类型 1. 微信 2. 支付宝 3. paddle 7. 苹果内购
    payment_result = JSONField(null=True)  # 支付结果
    source = CharField(null=True)  # 订单来源

    class Meta:
        db_table = "lecture_orders"
        database = db


class CreateOrderRequest(BaseModel):
    product_id: int
    option: str


def post_order_update(user_id, product_id, option):
    if product_id == ProductID.SUBSCRIBE_STANDARD or product_id == ProductID.SUBSCRIBE_VIP:
        return update_membership(user_id, product_id, option)
    # elif product_id == ProductID.POINTS:
    #     return update_points(user_id, option)
    else:
        return False


def generate_order_str(order_id):
    now = get_beijing_time()
    time_str = now.strftime("%Y%m%d%H%M%S")  # 格式化时间
    random_str = f"{random.randint(0, 9999):04d}"  # 生成4位随机数，确保前导零
    return f"{time_str}{order_id}{random_str}"


def update_points(user_id, option):
    member = get_lecture_member_by_user_id(user_id)
    if not member:
        return False

    points = parse_option_points(option)
    if not points:
        return False

    now = get_beijing_time()

    # 使用事务和原子操作增加积分
    with LectureMember._meta.database.atomic():
        query = LectureMember.update(points=LectureMember.points + points, updated_at=now).where(
            LectureMember.user_id == user_id
        )

        updated_rows = query.execute()

        if updated_rows == 0:
            return False

        # 获取更新后的 member 对象
        member = LectureMember.get(LectureMember.user_id == user_id)
        return {"member": model_to_dict(member)}


def checkMemberStatus(membership_type, new_membership_type) -> int:
    # 降级
    if membership_type > new_membership_type:
        return 1
    # 升级
    elif membership_type < new_membership_type:
        return 2
    # 续期
    else:
        return 3


def update_membership(user_id, product_id, option):
    member = get_lecture_member_by_user_id(user_id)
    if not member:
        return False

    now = get_beijing_time()
    today = now.date()

    if product_id == ProductID.SUBSCRIBE_STANDARD:
        new_membership_type = MembershipType.STANDARD
    elif product_id == ProductID.SUBSCRIBE_VIP:
        new_membership_type = MembershipType.VIP
    else:
        return False

    try:
        days = membership_days[new_membership_type][option]
    except KeyError:
        days = parse_option_days(option)

    if not days:
        return False
    update_status = checkMemberStatus(member.membership_type, new_membership_type)

    # 续期
    if update_status == 3:
        member.end_date = member.end_date + timedelta(days=days)
    # 升级
    elif update_status == 2:
        member.membership_type = new_membership_type
        member.start_date = today
        member.reset_date = today
        if member.end_date < today:
            member.extension_date = today + timedelta(days=days)
        else:
            member.extension_date = member.end_date + timedelta(days=days)
        member.end_date = today + timedelta(days=days)
        member.remaining_unlock = membership_initial_unlock[new_membership_type]
    # 降级
    else:
        if member.extension_date < today:
            member.extension_date = today
        member.extension_date = member.extension_date + timedelta(days=days)  # 普通会员有效期

    member.updated_at = now
    member.save()

    points = parse_option_points(option)
    if points:
        return update_points(user_id, option)

    # return False
    return {"member": model_to_dict(member)}


def parse_option_days(option):
    pattern = re.compile(r"(\d+)\s*([天年月])")
    match = pattern.match(option)

    if not match:
        return None

    value, unit = match.groups()
    value = int(value)

    if unit == "天":
        return value
    elif unit == "月":
        return value * 30
    elif unit == "年":
        return value * 365
    else:
        return None


def parse_option_points(option):
    pattern = re.compile(r"(\d+)\s*(积分)")
    match = pattern.match(option)

    if not match:
        return None

    value, unit = match.groups()
    value = int(value)

    if unit == "积分":
        return value
    else:
        return None


def get_order_from_id(orderId: str) -> Optional[LectureOrders]:
    try:
        return LectureOrders.get(LectureOrders.order_id == orderId)
    except LectureOrders.DoesNotExist:
        return None

def get_order_from_status(user_id: str, statuses, include_deleted=False):
    """
    根据用户ID和状态查询订单

    Args:
        user_id (str): 用户ID
        statuses: 订单状态，可以是单个状态或状态列表
                 - 单个状态: OrderStatus.SUCCESS
                 - 多个状态: [OrderStatus.SUCCESS, OrderStatus.REFUNDED]
        include_deleted (bool): 是否包含已删除的订单，默认False

    Returns:
        list: 订单列表
    """
    try:
        # 构建基础查询条件
        conditions = [LectureOrders.user_id == user_id]

        # 处理状态条件
        if isinstance(statuses, list):
            # 多个状态使用 in_() 方法
            conditions.append(LectureOrders.status.in_(statuses))
        else:
            # 单个状态直接比较
            conditions.append(LectureOrders.status == statuses)

        # 添加 delete_flag 过滤
        if not include_deleted:
            conditions.append(LectureOrders.delete_flag == 0)

        # 执行查询并排序
        orders = LectureOrders.select().where(*conditions).order_by(LectureOrders.created_at.desc())

        return list(orders)

    except Exception as e:
        logger.error(f"查询订单失败: user_id={user_id}, statuses={statuses}, error={str(e)}")
        return []



async def update_member_info(order: LectureOrders):
    """
    更新会员信息 2024-11-17
    """
    try:
        member = get_lecture_member_by_user_id(order.user_id)
        if not member:
            logger.error(f"Member not found for order {order}")
            return

        now = get_beijing_time()
        today = now.date()

        plan = get_lecture_plan_by_biz_id(order.product_id)
        if not plan:
            logger.error(f"Plan not found for order {order}")
            return

        update_status = checkMemberStatus(member.membership_type, plan.type)

        # 续期
        if update_status == 3:
            member.end_date = member.end_date + timedelta(days=plan.day_of_the_product())
        # 升级
        elif update_status == 2:
            member.membership_type = plan.type
            member.start_date = today
            member.reset_date = today
            if member.end_date < today:
                member.extension_date = today + timedelta(days=plan.day_of_the_product())
            else:
                member.extension_date = member.end_date + timedelta(days=plan.day_of_the_product())
            member.end_date = today + timedelta(days=plan.day_of_the_product())
            member.remaining_unlock = membership_initial_unlock[plan.type]
        # 降级
        else:
            if member.extension_date < today:
                member.extension_date = today
            member.extension_date = member.extension_date + timedelta(days=plan.day_of_the_product())  # 普通会员有效期

        member.updated_at = now
        member.points = member.points + plan.point
        member.save()
        logger.info(f"Member updated: {model_to_dict(member)}")
    except Exception:
        logger.exception(f"Error updating member info,order_id={order.order_id}")

async def downgrade_member_info(order: LectureOrders):
    """
    更新会员信息 2024-11-17
    """
    try:
        member = get_lecture_member_by_user_id(order.user_id)
        if not member:
            logger.error(f"Member not found for order {order}")
            return

        now = get_beijing_time()
        today = now.date()

        plan = get_lecture_plan_by_biz_id(order.product_id)
        if not plan:
            logger.error(f"Plan not found for order {order}")
            return

        member.end_date=member.end_date-timedelta(days=plan.day_of_the_product())
        member.extension_date=member.extension_date-timedelta(days=plan.day_of_the_product())
        member.reset_date=today
        member.updated_at=now
        member.points=member.points-plan.point
        if member.end_date<today or member.points<=0:
            member.membership_type=MembershipType.FREE
        member.save()
        logger.info(f"Member updated: {model_to_dict(member)}")
    except Exception:
        logger.exception(f"Error updating member info,order_id={order.order_id}")


async def update_payment_record(order: LectureOrders):
    try:
        logger.info(f"Updating payment record for order {order}")
        plan = get_lecture_plan_by_biz_id(order.product_id)
        action_type = UserActionType.RECHARGE if order.amount > 0 else UserActionType.FREE_CHARGE
        process_remark = f"用户充值{plan.title}" if order.amount > 0 else f"用户免费充值{plan.title}"

        add_user_action_record(
            order.user_id,
            action_type,
            UserActionProcess.ADD,
            process_remark,
            plan.point,
            order.order_id,
        )
    except Exception:
        logger.exception(f"Error adding user point record,trade_order_id={order.order_id}")
    pass
async def dwongrade_payment_record(order: LectureOrders):
    try:
        logger.info(f"dwongrade payment record for order {order}")
        plan = get_lecture_plan_by_biz_id(order.product_id)
        action_type = UserActionType.CANCEL
        process_remark = f"用户退款{plan.title}"

        add_user_action_record(
            order.user_id,
            action_type,
            UserActionProcess.SUB,
            process_remark,
            plan.point,
            order.order_id,
        )
    except Exception:
        logger.exception(f"Error adding user point record,trade_order_id={order.order_id}")
    pass


async def update_affiliates_info(order):
    try:
        # 设置分销相关信息
        oder_dict = model_to_dict(order)
        await create_affiliates_order_and_distribute(**oder_dict)
    except Exception:
        logger.exception(f"Error create affiliates order and distribute,trade_order={order.order_id}")


async def safe_update_member_info(order):
    try:
        await update_member_info(order)
    except Exception as e:
        logger.exception(f"Error in update_member_info: {e}")


async def safe_update_payment_record(order):
    try:
        await update_payment_record(order)
    except Exception as e:
        logger.exception(f"Error in update_payment_record: {e}")


async def safe_update_affiliates_info(order):
    try:
        await update_affiliates_info(order)
    except Exception as e:
        logger.exception(f"Error in update_affiliates_info: {e}")


async def order_call_back(order: LectureOrders):
    # 更新会员信息
    # 更新支付记录
    # 更新分销信息
    await asyncio.gather(
        safe_update_member_info(order), safe_update_payment_record(order), safe_update_affiliates_info(order)
    )

async def order_refund_call_back(order: LectureOrders):
   await asyncio.gather(downgrade_member_info(order),dwongrade_payment_record(order))


def create_free_order(user_id, product_id, exchange_code, payment_type=PaymentType.EXCHANGE_CODE, source: str = None):
    plan = get_lecture_plan_by_biz_id(product_id)
    if not plan:
        logger.error(f"Plan not found for product_id {product_id}")
        return

    now = get_beijing_time()

    new_order = LectureOrders.create(
        user_id=user_id,
        product_id=plan.biz_id,
        option=plan.title,
        amount=0,
        status=OrderStatus.SUCCESS,
        payment_type=payment_type,
        transaction_id=exchange_code,
        created_at=now,
        updated_at=now,
        source=source
    )
    new_order.order_id = generate_order_str(new_order.id)
    new_order.save()

    asyncio.create_task(order_call_back(new_order))


async def handle_wechat_notify(jsons: dict):
    # Log the notification
    # Extract necessary information from the notification
    transaction_id = jsons.get("transaction_id")
    amount = jsons.get("total_fee")
    mchid = jsons.get("mch_id")
    trade_state = jsons.get("result_code")
    success_time = jsons.get("time_end")
    out_trade_no = jsons.get("out_trade_no")

    # Log the extracted information
    logger.info(
        "Transaction ID: %s, Amount: %s, MchID: %s, Trade State: %s, Success Time: %s",
        transaction_id,
        amount,
        mchid,
        trade_state,
        success_time,
    )

    # Update the order status in the database
    order = get_order_from_id(out_trade_no)
    if order:
        if order.status == OrderStatus.SUCCESS:
            logger.info("Order already updated: %s", order.__data__)
            return
        # Update the order in the database
        counts = (
            LectureOrders.update(
                status=OrderStatus.SUCCESS,
                payment_type=PaymentType.WE_CHAT,
                transaction_id=transaction_id,
                payment_result=jsons,
            )
            .where((LectureOrders.order_id == out_trade_no) & (LectureOrders.status != OrderStatus.SUCCESS))
            .execute()
        )

        logger.info("Order updated successfully: %s", order.__data__)

        # Call the order callback
        if counts > 0 and trade_state == "SUCCESS":
            await order_call_back(order)
    else:
        logger.warn("Order not found for transaction ID: %s", transaction_id)
