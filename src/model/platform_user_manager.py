import random
from datetime import datetime
from enum import Enum
from uuid import UUID

from peewee import (
    AutoField,
    BooleanField,
    CharField,
    DateTimeField,
    IntegerField,
    Model,
    TextField
)
from playhouse.sqlite_ext import <PERSON><PERSON><PERSON><PERSON>

from src.logger.logUtil import get_logger
from src.orm.pgrepo import db
from src.util.stringify import hash_password

logger = get_logger(__name__)


class PlatformLevelEnum(Enum):
    ADMIN = ("ADMIN", "管理员", 1000)
    EMPLOYEE = ("EMPLOYEE", "内部员工", 500)
    ENTERPRISE = ("ENTERPRISE", "企业", 300)
    PERSON = ("PERSON", "个人", 300)

    def __init__(self, mark, message, code):
        self.mark = mark
        self.message = message
        self.code = code

    @staticmethod
    def get_code(mark):
        for item in PlatformLevelEnum:
            if mark == item.mark:
                return item.code
        return None


class PlatformUser(Model):
    """
    开放平台--用户信息
    """

    id = AutoField(primary_key=True)
    biz_id = CharField(null=False, index=True, unique=True,
                       default="open_user_" + str(UUID(int=random.getrandbits(128))),
                       max_length=100)
    name = CharField(null=False, max_length=100)
    daily_limit = IntegerField(null=False, default=1000)
    points = IntegerField(null=False, default=0)
    create_time = DateTimeField(default=datetime.now)
    update_time = DateTimeField(default=datetime.now)
    delete_flag = IntegerField(default=0)

    email = CharField(null=True, max_length=100)
    mail_cert = BooleanField(default=False)
    password = CharField(null=True, max_length=100)
    mobile = CharField(null=True, max_length=20)
    level = IntegerField(null=False, default=300)

    member_level = IntegerField(null=False, default=0)

    head_img = TextField(null=True)
    enterprise_cert = BooleanField(default=False)  # 企业认证
    extra_info = JSONField(null=True)  # 用于存储JSON格式的数据

    create_by = CharField(null=True, max_length=100)
    update_by = CharField(null=True, max_length=100)

    class Meta:
        table_name = "open_platform_user"
        database = db

    def to_vo(self):
        return {
            "biz_id": self.biz_id,
            "name": self.name,
            "email": self.email,
            "points": self.points,
            "mobile": self.mobile,
            "level": self.level,
            "head_img": self.head_img,
            "enterprise_cert": self.enterprise_cert,
            "create_time": self.create_time,
            "update_time": self.update_time,
        }


def init_platform_admin():
    email = '<EMAIL>'
    password = 'aihaojiadmin'
    user = PlatformUser.select().where(PlatformUser.email == email).first()
    if not user:
        PlatformUser.create(
            name='admin',
            email=email,
            password=hash_password(password),
            mail_cert=True,
            level=PlatformLevelEnum.ADMIN.code
        )
