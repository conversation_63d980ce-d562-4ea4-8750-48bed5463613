import random
from datetime import datetime
from typing import Optional
from uuid import UUID

from peewee import AutoField, CharField, DateTimeField, Model, SmallIntegerField, TextField, FloatField
from playhouse.postgres_ext import <PERSON><PERSON><PERSON><PERSON>
from pydantic import Base64UrlStr, BaseModel

from src.orm.pgrepo import db


class LectureArticle(Model):
    id = AutoField(primary_key=True)
    biz_id = CharField(null=False, unique=True, default=str(UUID(int=random.getrandbits(128))))
    name = CharField(null=False)
    author = CharField(null=True)
    article_source = CharField(null=True)
    video_url = CharField(null=False, max_length=1024)
    img_url = CharField(null=True, max_length=1024)
    language = CharField(default="zh", null=False)
    out_language = CharField(default="zh", null=True)
    oss_url = Char<PERSON>ield(null=True)  # TODO 下个版本删除, 这一期引用的代码去掉
    oss_key = CharField(null=True)  # TODO 下个版本删除, 这一期引用的代码去掉
    content = TextField(null=True)  # TODO 下个版本删除, 这一期引用的代码去掉
    outline = TextField(null=True)  # TODO 下个版本删除, 这一期引用的代码去掉
    out_language_outline = TextField(null=True)  # TODO 下个版本删除, 这一期引用的代码去掉
    translated_outline_json = JSONField(null=True)
    podcast_transcript_json = JSONField(null=True)  # 播客输出内容
    total_summary = TextField(null=True)  # TODO 下个版本删除, 这一期引用的代码去掉
    out_language_total_summary = TextField(null=True)  # TODO 下个版本删除, 这一期引用的代码去掉
    translated_total_summary_json = JSONField(null=True)
    corpus = TextField(null=True)
    audio_url = CharField(null=True, max_length=2048)
    podcast_duration = FloatField(default=0.0)  # 播客时长
    oss_pdf_url = CharField(null=True, max_length=2048)
    oss_word_url = CharField(null=True, max_length=2048)
    create_time = DateTimeField(default=datetime.now)
    update_time = DateTimeField(default=datetime.now)
    delete_flag = SmallIntegerField(default=0)

    class Meta:
        table_name = "lecture_article"
        database = db

    def to_vo(self):
        return {
            "name": self.name,
            "author": self.author,
            "video_url": self.video_url,
            "out_language": self.out_language,
            "outline": dict(self.translated_outline_json or {}).get(self.out_language),
            "total_summary": dict(self.translated_total_summary_json or {}).get(self.out_language),
            "create_time": self.create_time,
            "update_time": self.update_time,
        }

    def to_vo_audio(self):
        data = self.to_vo()
        data.update({
            "audio_url": self.audio_url,
            "podcast_duration": self.podcast_duration,
            "podcast_transcript_json": self.podcast_transcript_json
        })
        return data


class ArticleVO(BaseModel):
    id: int = None
    # 业务id 用UUID 生成str
    biz_id: str
    name: str
    video_url: Base64UrlStr
    language: str = "chinese"
    oss_url: str
    oss_key: str
    content: str
    img_url: str
    create_time: str = datetime.now()
    update_time: str = datetime.now()


def get_article_by_id(article_id) -> Optional[LectureArticle]:
    return (
        LectureArticle.select().where((LectureArticle.biz_id == article_id) & (LectureArticle.delete_flag == 0)).first()
    )


def get_article_by_url(url) -> Optional[LectureArticle]:
    return LectureArticle.select().where((LectureArticle.video_url == url) & (LectureArticle.delete_flag == 0)).first()
