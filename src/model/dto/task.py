from pydantic import BaseModel, Field


class TaskCreate(BaseModel):
    task_source: str = Field(..., description="任务来源 youtube, bilibili, upload")
    status: str = "confirm"
    ossKey: str = Field(None, description="oss key，只有在导入的时候有用")
    duration_minutes: int = Field(0, description="任务时长,只有在导入的时候有用")
    ctrl_params: str = Field(None, description="任务参数")
    url_biz_id: str = Field(None, description="url biz id")
