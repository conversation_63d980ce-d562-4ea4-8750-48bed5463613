from datetime import datetime, timezone
from enum import Enum
from functools import partial

from peewee import AutoField, BooleanField, CharField, DateTimeField, IntegerField, Model, SmallIntegerField

from src.logger.logUtil import get_logger
from src.orm.pgrepo import db

logger = get_logger(__name__)


class ProvidedType(str, Enum):
    CUSTOMER_SERVICE = "customer_service"
    BULK_SALE = "bulk_sale"
    REAL_TIME_EXCHANGE = "real_time_exchange"
    KOL = "kol"


class ExchangeCode(Model):
    id = AutoField()
    exchange_code = CharField(unique=True)
    exchange_code_type = CharField()
    # 数据库里使用 utc 时间，前端可转成 local time
    has_provided = BooleanField(default=False)
    batch_id = CharField()
    exchange_code_times = IntegerField(default=1)
    expired_date = DateTimeField()
    created_at = DateTimeField(default=partial(datetime.now, tz=timezone.utc))
    updated_at = DateTimeField(default=partial(datetime.now, tz=timezone.utc))
    delete_flag = SmallIntegerField(default=0)

    class Meta:
        database = db
        table_name = "exchange_codes"


class ExchangeRecord(Model):
    id = AutoField()
    user_id = CharField(null=True)
    exchange_code = CharField()
    batch_id = CharField()
    types = CharField(choices=["pre", "real"])
    has_exchanged = BooleanField(default=False)
    exchanged_at = DateTimeField(null=True)
    provided_type = CharField(choices=[ProvidedType.CUSTOMER_SERVICE, ProvidedType.BULK_SALE, ProvidedType.KOL])
    provided_at = DateTimeField(null=True)
    created_at = DateTimeField(default=partial(datetime.now, tz=timezone.utc))
    updated_at = DateTimeField(default=partial(datetime.now, tz=timezone.utc))
    delete_flag = SmallIntegerField(default=0)

    # TODO, 定义外键
    class Meta:
        database = db
        table_name = "exchange_records"


if __name__ == "__main__":
    db.create_tables([ExchangeCode, ExchangeRecord])
    logger.info("create exchange tables")
