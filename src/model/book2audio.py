from datetime import datetime, timezone

from peewee import <PERSON><PERSON><PERSON><PERSON>, DateTimeField, IntegerField, Model, SmallIntegerField, TextField
from playhouse.postgres_ext import <PERSON><PERSON><PERSON><PERSON>

from src.orm.pgrepo import db


class BookTaskStatus:
    waiting = "waiting"
    processing = "processing"
    completed = "completed"
    failed = "failed"
    invalid = "invalid"
    deleted = "deleted"


# TODO: 需要一个定时任务，定时清理已删除的任务


# 定义 Peewee ORM 模型
class BookTask(Model):
    biz_id = Char<PERSON>ield(unique=True, null=False)
    book_title = Char<PERSON>ield(null=True, max_length=2048)
    author = CharField(null=True, max_length=2048)
    content = TextField(null=True)
    corpus = TextField(null=True)
    callback_url = CharField(null=True, max_length=2048)
    status = CharField(default=BookTaskStatus.waiting)
    audio_uri = CharField(null=True, max_length=2048)
    subtitle_uri = Char<PERSON>ield(null=True, max_length=2048)
    caption_list = JSONField(null=True)
    duration = IntegerField(null=True)
    size = IntegerField(null=True)
    created_at = DateTimeField(default=lambda: datetime.now(timezone.utc))  # 创建时间, UTC 格式
    updated_at = DateTimeField(default=lambda: datetime.now(timezone.utc))  # 更新时间, UTC 格式
    retries = SmallIntegerField(default=0)
    remark = CharField(null=True)
    # ctrl_params 可传如 language, voice, format 等参数
    ctrl_params = JSONField(null=True)
    delete_flag = IntegerField(default=0)

    class Meta:
        database = db

    def show_status(self):
        return {
            "task_id": self.biz_id,
            "status": self.status,
        }

    def to_vo(self):
        return {
            "task_id": self.biz_id,
            "book_title": self.book_title,
            "author": self.author,
            "corpus": self.corpus,
            "status": self.status,
            "audio_uri": self.audio_uri,
            "caption_list": self.caption_list,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
            "duration": self.duration,
            "size": self.size,
            "ctrl_params": self.ctrl_params,
        }


def get_latest_book2task(task_id=None):
    if task_id:
        return BookTask.select().where(BookTask.biz_id == task_id, BookTask.delete_flag == 0).first()
    else:
        conditions = [
            BookTask.delete_flag == 0,
            BookTask.status == BookTaskStatus.waiting,
        ]
        with db.atomic():
            task = BookTask.select().where(*conditions).order_by(BookTask.created_at.desc()).first()
            if task:
                task.status = BookTaskStatus.processing
                task.updated_at = datetime.now(timezone.utc)
                task.save()
            return task
