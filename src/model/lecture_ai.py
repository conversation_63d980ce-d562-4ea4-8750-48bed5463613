import uuid
from enum import Enum, IntEnum
from typing import Optional

from peewee import AutoField, CharField, DateTimeField, IntegerField, Model, SmallIntegerField, TextField
from playhouse.sqlite_ext import <PERSON><PERSON><PERSON><PERSON>

from src.constant.stream_prompt import REASONING_CONTENT_TAG
from src.orm.pgrepo import db
from src.util.dateUtil import get_current_time_in_beijing

DEFAULT_SUMMART_LIMIT = 40


class RoleValue(Enum):
    USER = "user"
    SYSTEM = "system"
    ASSISTANT = "assistant"


class AITYPE(IntEnum):
    """
    1. 大钢总结 2. 全文总结 3. 翻译 4. 划线总结 5. ai学习 6. ai润色 7. 音频 8.翻译字段
    """

    OUTLINE = 1
    SUMMARY = 2
    TRANSLATION = 3
    SUMMARY_LINE = 4
    AI_LEARNING = 5
    AI_POLISHING = 6
    AUDIO = 7
    TRANSLATION_FIELD = 8


class FetchSource(IntEnum):
    """
    1. AI大模型 2.算法侧 3.用户侧 4.文章全文
    """

    AI_MODEL = 1
    ALGORITHM = 2
    USER = 3
    ARTICLE = 4


def get_ai_group(user_id, article_biz_id):
    return (
        LectureAIGroup.select(LectureAIGroup.id, LectureAIGroup.biz_id)
        .where(LectureAIGroup.user_id == user_id, LectureAIGroup.sence_biz_id == article_biz_id)
        .first()
    )


def get_or_create_ai_group(user_id, article_biz_id):
    ai_group, _ = LectureAIGroup.get_or_create(
        user_id=user_id,
        sence_biz_id=article_biz_id,
        delete_flag=0,
        defaults={"biz_id": uuid.uuid4().hex},
    )
    return ai_group


def create_ai_detail(
    user_id,
    group_biz_id,
    biz_id,
    ai_type: Optional[int] = None,
    fetch_source: Optional[int] = None,
    content: Optional[str] = None,
    role: Optional[str] = None,
    token: Optional[int] = 0,
):
    return LectureAIDetail.create(
        user_id=user_id,
        group_biz_id=group_biz_id,
        biz_id=biz_id,
        ai_type=ai_type,
        fetch_source=fetch_source,
        content=content,
        role=role,
        token=token,
    )


def find_all_ai_details(group_biz_id):
    return LectureAIDetail.select().where(LectureAIDetail.group_biz_id == group_biz_id)


class LectureAIGroup(Model):
    id = AutoField(primary_key=True)
    biz_id = CharField(null=False, unique=True)
    sence_type = CharField(default="article")
    user_id = CharField(null=False)
    summary_limit = IntegerField(default=DEFAULT_SUMMART_LIMIT)
    used_count = IntegerField(default=0)
    sence_biz_id = CharField(null=False)
    prompt = CharField(null=True)
    delete_flag = SmallIntegerField(default=0)
    extra_params = JSONField(null=True)
    created_at = DateTimeField(default=get_current_time_in_beijing)
    updated_at = DateTimeField(default=get_current_time_in_beijing)

    class Meta:
        table_name = "lecture_ai_group"
        database = db

    def check_can_use(self):
        return self.used_count < self.summary_limit


class LectureAIDetail(Model):
    id = AutoField(primary_key=True)
    biz_id = CharField(null=False, unique=True)
    group_biz_id = CharField(null=False)
    ai_type = IntegerField(null=True)
    fetch_source = IntegerField(null=True)
    user_id = CharField(null=False, default="system")
    content = TextField(null=True)
    role = CharField(null=True)
    token = IntegerField(null=False, default=0)
    delete_flag = SmallIntegerField(default=0)
    extra_params = JSONField(null=True)
    created_at = DateTimeField(default=get_current_time_in_beijing)
    updated_at = DateTimeField(default=get_current_time_in_beijing)

    class Meta:
        table_name = "lecture_ai_detail"
        database = db

    def to_vo(self):
        content = str(self.content)
        thinking_content = ""
        if content and len(content.split(REASONING_CONTENT_TAG)) >= 2:
            thinking_content, content = content.split(REASONING_CONTENT_TAG)
        return {
            "sender": "user" if self.role == RoleValue.USER.value else "ai",
            "content": content,
            "created_at": self.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            "thinking_content": thinking_content,
        }


def find_ai_learning_history(user_id, article_biz_id, ai_type, content):
    ai_group = get_ai_group(user_id=user_id, article_biz_id=article_biz_id)
    if not ai_group:
        return None
    ai_details = (
        LectureAIDetail.select(LectureAIDetail.id, LectureAIDetail.content, LectureAIDetail.created_at)
        .where(LectureAIDetail.group_biz_id == ai_group.biz_id, LectureAIDetail.ai_type == ai_type)
        .order_by(LectureAIDetail.created_at)
    )
    next_stop = False
    for ai_detail in ai_details:
        if next_stop is True:
            return ai_detail.content
        if ai_detail.content == content:
            next_stop = True
    return None


def find_all_ai_learning_history(user_id, article_biz_id, ai_type, page_no, page_size):
    # page_size 是以组的形式
    ai_group = get_ai_group(user_id=user_id, article_biz_id=article_biz_id)
    if not ai_group:
        return [], 0
    query = LectureAIDetail.select(
        LectureAIDetail.id, LectureAIDetail.role, LectureAIDetail.content, LectureAIDetail.created_at
    ).where(LectureAIDetail.group_biz_id == ai_group.biz_id, LectureAIDetail.ai_type == ai_type)
    total = query.count()
    ai_details = query.order_by(-LectureAIDetail.created_at).paginate(page_no, page_size * 2)
    data = []
    for ai_detail in ai_details:
        data.append(ai_detail.to_vo())
    return data, total // 2


class LectureAITask(Model):
    id = AutoField(primary_key=True)
    biz_id = CharField(null=False)
    user_id = CharField(null=False)
    task_type = IntegerField(null=False)
    name = CharField(null=True)
    task_source = CharField(null=True)
    start_time = DateTimeField(default=get_current_time_in_beijing)
    end_time = DateTimeField(null=True)
    status = CharField(default="confirm", null=False)
    process_percent = IntegerField(null=True)
    article_biz_id = CharField(null=True)
    ai_detail_id = CharField(null=True)
    remark = CharField(null=True)
    order = IntegerField(default=0)
    delete_flag = IntegerField(default=0)
    ctrl_params = JSONField(null=True)

    class Meta:
        table_name = "lecture_ai_tasks"
        database = db

    def init_task(self):
        self.biz_id = "task_" + str(uuid.uuid4())
        self.create()


def get_ability_by_group_and_type(group_biz_id, ai_type: AITYPE):
    return LectureAIDetail.select().where(
        (LectureAIDetail.group_biz_id == group_biz_id)
        & (LectureAIDetail.ai_type == ai_type)
        & (LectureAIDetail.delete_flag == 0)
        & (LectureAIDetail.role == RoleValue.ASSISTANT.value)
    )


def create_ai_task_with_task_type(
    article_biz_id,
    task_type,
    name: Optional[str] = None,
    **kwargs,
):
    if ai_task := check_ai_task_has_created(
        article_biz_id=article_biz_id,
        task_type=task_type,
    ):
        return True, ai_task
    if not name:
        name = AITYPE(task_type).name
    with db.atomic():
        ai_task = LectureAITask.create(
            biz_id="task_" + str(uuid.uuid4()),
            article_biz_id=article_biz_id,
            task_type=task_type,
            name=name,
            start_time=get_current_time_in_beijing(),
            **kwargs,
        )
    return False, ai_task


def check_ai_task_has_created(article_biz_id, task_type):
    conditions = [
        LectureAITask.article_biz_id == article_biz_id,
        LectureAITask.task_type == task_type,
        LectureAITask.delete_flag == 0,
    ]
    return LectureAITask.select().where(*conditions).first()


def check_ai_task_has_list(article_biz_id, task_type_list: list[int]):
    conditions = [
        LectureAITask.article_biz_id == article_biz_id,
        LectureAITask.task_type.in_(task_type_list),
        LectureAITask.delete_flag == 0,
    ]
    return LectureAITask.select().where(*conditions)


def find_ai_task(biz_id):
    return LectureAITask.select().where(LectureAITask.biz_id == biz_id, LectureAITask.delete_flag == 0).first()


def get_latest_ai_task() -> Optional[LectureAITask]:
    return (
        LectureAITask.select()
        .where((LectureAITask.status == "confirm") & (LectureAITask.delete_flag == 0))
        .order_by(-LectureAITask.order, LectureAITask.id.asc())
        .first()
    )
