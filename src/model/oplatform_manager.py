import random
import string
from datetime import datetime
from typing import Optional
from uuid import UUID

from peewee import (
    AutoField,
    CharField,
    DateTimeField,
    IntegerField,
    Model
)
from playhouse.sqlite_ext import <PERSON><PERSON><PERSON>ield

from src.logger.logUtil import get_logger
from src.orm.pgrepo import db

logger = get_logger(__name__)


class OpenPlatform(Model):
    """
    开放平台
    """

    id = AutoField(primary_key=True)
    biz_id = CharField(null=False, default="open_" + str(UUID(int=random.getrandbits(128))))
    name = Char<PERSON><PERSON>(null=False)
    app_key = Char<PERSON>ield(null=False, default="".join(random.sample(string.ascii_letters + string.digits, 10)))
    secret_key = CharField(null=False, default="".join(random.sample(string.ascii_letters + string.digits, 16)))
    daily_limit = IntegerField(null=False, default=1000)
    points = IntegerField(null=False, default=0)
    create_time = Date<PERSON><PERSON><PERSON>ield(default=datetime.now)
    update_time = DateTimeField(default=datetime.now)
    delete_flag = IntegerField(default=0)

    user_id = Char<PERSON>ield(null=True)

    create_by = CharField(null=True)
    update_by = CharField(null=True)

    class Meta:
        table_name = "open_platform"
        database = db

    def to_vo(self):
        return {
            "biz_id": self.biz_id,
            "create_time": self.create_time,
            "update_time": self.update_time,
            "name": self.name,
            "app_key": self.app_key,
            "secret_key": self.secret_key,
            "user_id": self.user_id
        }


def get_open_platform_by_appKey(app_key) -> Optional[OpenPlatform]:
    """
    Get open platform by open platform id
    :param open_platform_id: open platform id
    :return: open platform
    """
    open_platform = OpenPlatform.select().where(OpenPlatform.app_key == app_key, OpenPlatform.delete_flag == 0).first()
    if open_platform:
        return open_platform
    else:
        return None


def sub_open_platform_point(open_platform_id, point):
    """
    Subtract open platform point
    :param open_platform_id: open platform id
    :param point: point
    :return:
    """
    try:
        open_platform = (
            OpenPlatform.select().where(OpenPlatform.biz_id == open_platform_id, OpenPlatform.delete_flag == 0).first()
        )
        if not open_platform:
            logger.error(f"Open platform not found, open_platform_id={open_platform_id}")
            return False
        old_points = open_platform.points  # 获取旧的积分
        if old_points - point < 0:
            return False

        # 使用 UPDATE ... WHERE 语句来更新积分
        rows = (
            OpenPlatform.update(points=OpenPlatform.points - point)
            .where((OpenPlatform.biz_id == open_platform_id) & (OpenPlatform.points == old_points))
            .execute()
        )

        if rows == 0:
            # 如果没有行被更新，说明积分在你操作的过程中被其他操作修改了
            logger.warn(f"操作b端商戶积分记录失败，有其他操作在同时进行，未执行记录{open_platform_id}，积分{point}")
            return False
        return True
    except Exception:
        logger.exception(f"Error sub platform point,user_id={open_platform_id}")
        return False
