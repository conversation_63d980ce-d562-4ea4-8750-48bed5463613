import calendar
from datetime import datetime, timed<PERSON>ta
from enum import IntEnum
from typing import Optional

from peewee import AutoField, CharField, DateField, DateTimeField, IntegerField, Model

from src.logger.logUtil import get_logger
from src.model.lecture_plan_manager import LecturePlan
from src.orm.pgrepo import db
from src.util.dateUtil import get_beijing_time

logger = get_logger(__name__)


class MembershipType(IntEnum):
    FREE = 0
    STANDARD = 1
    VIP = 2

    def max_batch_count(self):
        if self == MembershipType.FREE:
            return 3
        elif self == MembershipType.STANDARD:
            return 10
        elif self == MembershipType.VIP:
            return 10
        return 0

    def max_day_count_minutes(self):
        if self == MembershipType.FREE:
            return 10, 300
        elif self == MembershipType.STANDARD:
            return 100, 3000
        elif self == MembershipType.VIP:
            return 100, 3000
        return 0

    def max_day_d(self):
        if self == MembershipType.FREE:
            return 10
        elif self == MembershipType.STANDARD:
            return 100
        elif self == MembershipType.VIP:
            return 100
        return 0


class MembershipTaskRights:
    FREE = {
        "level": "FREE",
        "outline": False,  # 大纲
        "mindMap": False,  # 思维导图
        "quote": False,  # 金句摘抄
        "chapter_summary": False,  # 章节总结
        "Translation": False,  # 翻译
        "ai_summary": False,  # ai总结
        "total_summary": True,  # 全文总结
        "image_quality": "low",  # 画质
        "upload": False,  # 上传
    }
    STANDARD = {
        "level": "STANDARD",
        "outline": True,
        "mindMap": True,
        "quote": True,
        "chapter_summary": True,
        "Translation": True,
        "ai_summary": True,
        "total_summary": True,
        "image_quality": "normal",
        "upload": False,
    }
    VIP = {
        "level": "VIP",
        "outline": True,
        "mindMap": True,
        "quote": True,
        "chapter_summary": True,
        "Translation": True,
        "ai_summary": True,
        "total_summary": True,
        "image_quality": "high",
        "upload": True,
    }

    @classmethod
    def from_membership_type(cls, membership_type: MembershipType):
        if membership_type == MembershipType.FREE:
            return cls.FREE
        elif membership_type == MembershipType.STANDARD:
            return cls.STANDARD
        elif membership_type == MembershipType.VIP:
            return cls.VIP
        else:
            raise ValueError("Invalid membership type")


membership_initial_unlock = {MembershipType.FREE: 4, MembershipType.STANDARD: 20, MembershipType.VIP: -1}


class LectureMember(Model):
    """
    会员表，包含会员信息和状态
    """

    id = AutoField(primary_key=True)  # 唯一标识符
    user_id = CharField(null=False)  # 用户的唯一标识符
    membership_type = IntegerField(
        null=False, choices=[(m.value, m.name) for m in MembershipType]
    )  # 会员类型（0=免费，1=普通，2=VIP）
    start_date = DateField(null=False)  # 会员资格开始日期
    end_date = DateField(null=False)  # 会员资格结束日期
    reset_date = DateField(null=False)  # 重置日期
    extension_date = DateField(null=False)  # 延期日期
    remaining_unlock = IntegerField(null=False)  # 会员剩余的分钟数
    points = IntegerField(null=False)  # 会员的积分
    sub_id = CharField(null=True)  # paddle订阅id
    created_at = DateTimeField(default=datetime.now)  # 创建时间
    updated_at = DateTimeField(default=datetime.now)  # 更新时间
    delete_flag = IntegerField(default=0)  # 删除标志
    level = IntegerField(default=0)
    class Meta:
        table_name = "lecture_member"
        database = db


def get_lecture_member_by_user_id(user_id) -> LectureMember:
    try:
        member = LectureMember.get(LectureMember.user_id == user_id)
    except LectureMember.DoesNotExist:
        member = create_member(user_id)
    return update_member(member)


def create_member(user_id):
    # 获取当前日期
    now = get_beijing_time()
    today = now.date()
    # 创建 LectureMember 实例
    member = LectureMember.create(
        user_id=user_id,
        membership_type=MembershipType.FREE,
        start_date=today,
        reset_date=today,
        extension_date=today,
        end_date=today + timedelta(days=30),
        remaining_unlock=membership_initial_unlock[MembershipType.FREE],
        points=90,
        created_at=now,
        updated_at=now,
    )
    # 保存到数据库
    member.save()
    return member


def get_days_in_month(year, month):
    # 获取某年某月的天数
    return calendar.monthrange(year, month)[1]


def verify_member_duration(user_id: str, duration: int):
    """
    验证会员剩余时长是否足够
    :param user_id: 用户id
    :param duration: 视频时长
    :return:
    """
    member: LectureMember = get_lecture_member_by_user_id(user_id)
    if member.remaining_unlock < duration:
        return False
    return True


def sub_member_point(user_id, point):
    """
    扣除会员积分
    :param user_id:
    :param point:
    :return:
    """
    try:
        member: LectureMember = get_member_info(user_id)
        old_points = member.points  # 获取旧的积分
        if old_points - point < 0:
            return False

        # 使用 UPDATE ... WHERE 语句来更新积分
        rows = (
            LectureMember.update(points=LectureMember.points - point)
            .where((LectureMember.user_id == user_id) & (LectureMember.points == old_points))
            .execute()
        )

        if rows == 0:
            # 如果没有行被更新，说明积分在你操作的过程中被其他操作修改了
            logger.warn(f"操作会员积分记录失败，有其他操作在同时进行，未执行记录{user_id}，积分{point}")
            return False
        return True
    except Exception:
        logger.exception(f"Error sub member point,user_id={user_id}")
        return False


def sub_member_lock_count(biz_id):
    """
    扣除会员积分
    :param biz_id:
    :param duration:
    :return:
    """
    try:
        member = get_member_info(biz_id)
        # 高级vip直接返回true
        if member.membership_type == MembershipType.VIP:
            return True
        # 普通vip
        if member.remaining_unlock < 1:
            return False
        member.remaining_unlock -= 1
        member.save()
        return True
    except Exception:
        logger.exception("Error sub member point: ")
        return False


def get_member_info(user_id) -> Optional[LectureMember]:
    try:
        member = LectureMember.select().where(LectureMember.user_id == user_id, LectureMember.delete_flag == 0).first()
        return member
    except LectureMember.DoesNotExist:
        logger.exception(f"No member found with user_id {user_id}")
        return None


def get_next_month_date(date):
    year = date.year
    month = date.month
    day = date.day
    if day <= 28:
        # 日期在28号之前，加本月天数
        days_in_current_month = get_days_in_month(year, month)
        return date + timedelta(days=days_in_current_month)
    else:
        # 日期在28号之后，加下个月天数
        if month == 12:
            # 如果是12月，跳到下一年的1月
            next_month_year = year + 1
            next_month = 1
        else:
            next_month_year = year
            next_month = month + 1
        days_in_next_month = get_days_in_month(next_month_year, next_month)
        return date + timedelta(days=days_in_next_month)


def update_member(member):
    now = get_beijing_time()
    today = now.date()
    # 检查 updated_at 是否是今天
    if member.updated_at.date() == today:
        return member
    # 更新 updated_at
    member.updated_at = now
    # 检查是否到期
    if today > member.end_date:
        if member.extension_date > today:  # 延期普通会员
            member.membership_type = MembershipType.STANDARD
            member.reset_date = today
            member.end_date = member.extension_date
            member.remaining_unlock = membership_initial_unlock[MembershipType.STANDARD]
        else:  # 降级免费用户
            member.membership_type = MembershipType.FREE
            member.reset_date = today
            member.end_date = today + timedelta(days=30)
            member.remaining_unlock = membership_initial_unlock[MembershipType.FREE]
    elif member.membership_type == MembershipType.STANDARD:  # 月度资源重置
        # 检查是否到达 start_date 的下一个月
        next_month_date = get_next_month_date(member.reset_date)
        if today >= next_month_date:
            member.reset_date = next_month_date
            member.remaining_unlock = membership_initial_unlock[MembershipType.STANDARD]
    # 保存并返回修改后的 member
    member.save()
    return member


def verify_user_account(user_id: str) -> bool:
    # 获取当前会员信息
    member = get_member_info(user_id)
    if member and member.membership_type == MembershipType.VIP:
        return True
    else:
        return False


def update_member_time(member: LectureMember, product: LecturePlan):
    days = product.day_of_the_product()

    now = get_beijing_time()
    today = now.date()
    # 续期
    if product.type == member.membership_type:
        # 续期
        member.end_date = member.end_date + timedelta(days=days)
    # 升级
    elif product.type > member.membership_type:
        member.membership_type = product.type
        member.start_date = today
        member.reset_date = today
        member.end_date = today + timedelta(days=days)
    # 降级
    else:
        if member.extension_date < today:
            member.extension_date = today
        member.extension_date = member.extension_date + timedelta(days=days)


def update_member_points(user_id: str, product: LecturePlan):
    now = get_beijing_time()

    points = product.point

    query = LectureMember.update(points=LectureMember.points + points, updated_at=now).where(
        LectureMember.user_id == user_id
    )

    query.execute()


def update_member_by_points(user_id: str, points: int):
    now = get_beijing_time()

    query = LectureMember.update(points=LectureMember.points + points, updated_at=now).where(
        LectureMember.user_id == user_id
    )

    query.execute()


def delete_member_info(member: LectureMember):
    try:
        LectureMember.update(delete_flag=1).where(LectureMember.user_id == member.user_id).execute()
    except Exception as e:
        logger.exception("Error deleting member info: ", e)


def merge_member(current_biz_id, target_biz_id):
    if current_biz_id == target_biz_id:
        logger.error("不需要合并会员操作，req=%s,%s", current_biz_id, target_biz_id)
        return
    current_member = get_member_info(current_biz_id)
    target = get_member_info(target_biz_id)
    if not target:
        logger.error("无法获取目标会员信息，req=%s,%s", current_biz_id, target_biz_id)
        return
    current_member.points += target.points
    current_member.end_date = max(target.end_date, current_member.end_date)
    current_member.membership_type = max(target.membership_type, current_member.membership_type)
    current_member.save()
    delete_member_info(target)
