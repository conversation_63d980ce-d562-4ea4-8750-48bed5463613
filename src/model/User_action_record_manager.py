from datetime import datetime
from enum import IntEnum

from peewee import AutoField, CharField, DateTimeField, IntegerField, Model, SmallIntegerField

from src.logger.logUtil import get_logger
from src.orm.pgrepo import db

logger = get_logger(__name__)


class UserActionProcess(IntEnum):
    """
    积分操作状态
    """

    ADD = 1  # 加
    SUB = 2  # 减


class UserActionType(IntEnum):
    """
    积分操作类型
    """

    INVITE = 1  # 邀请
    REGISTER = 2  # 注册
    RECHARGE = 3  # 充值
    UNLOCK_ARTICLE = 4  # 解锁文章
    UNLOCK_VIDEO = 5  # 解锁视频
    CANCEL = 6  # 退款
    FREE_CHARGE = 7  # 免费充值
    SUB_RECHARGE = 8  # 订阅充值
    ADD_POINTS = 9  # 添加积分


def get_action_name(k: int) -> str:
    k = k - 1
    if k < 0 or k > 8:
        return "超范围记录"
    data = ["邀请", "注册", "充值", "解锁文章", "解锁视频", "退款", "免费充值", "订阅充值", "添加积分"]
    return data[k]


class LectureUserActionRecord(Model):
    """
    用户积分操作表
    """

    id = AutoField(primary_key=True)  # 唯一标识符
    biz_id = CharField(null=True)  # 标记
    user_id = CharField(null=False)  # 用户的唯一标识符
    action_relate_id = CharField(null=False)  # 关联操作的业务id ,如订单id 和任务id或者充值记录id，用来做回滚操作。
    user_action_type = IntegerField(null=False, choices=[(m.value, m.name) for m in UserActionType])  # 积分操作类型
    process_status = IntegerField(null=False, choices=[(m.value, m.name) for m in UserActionProcess])  # 积分操作状态
    process_remark = CharField(null=True)  # 积分操作备注
    points = IntegerField(null=False)  # 数量
    created_at = DateTimeField(default=datetime.now)  # 创建时间
    updated_at = DateTimeField(default=datetime.now)  # 更新时间
    delete_flag = SmallIntegerField(default=0)  # 删除标记
    rollback_id = SmallIntegerField(default=0)

    class Meta:
        table_name = "lecture_user_point_record"
        database = db


def add_user_action_record(user_id, user_action_type, process_status, process_remark, points, action_relate_id=""):
    """
    添加用户积分操作记录
    :param action_relate_id: 相关记录id
    :param user_id: 用户id 和商户id
    :param user_action_type: see UserActionType
    :param process_status: 积分操作状态
    :param process_remark: 积分操作备注
    :param points: 积分
    :return:
    """
    try:
        LectureUserActionRecord.create(
            user_id=user_id,
            user_action_type=user_action_type,
            process_status=process_status,
            process_remark=process_remark,
            action_relate_id=action_relate_id,
            points=points,
        )
        return True
    except Exception:
        logger.exception("Error adding user point record: ")
        return False


def handle_task_duration_record(duration_minutes, biz_id):
    """
    处理任务时长记录,如果任务时长超过了规定的时间，需要扣除积分,如果任务时长小于规定的时间，需要返还积分,如果没有积分了需要直接返回失败
    :param duration_minutes:
    :param biz_id:
    :return:
    """
    original_records = LectureUserActionRecord.select().where(
        (LectureUserActionRecord.user_action_type == UserActionType.UNLOCK_VIDEO)
        & (LectureUserActionRecord.action_relate_id == biz_id)
        & (LectureUserActionRecord.delete_flag == 0)
    )

    if not original_records:
        return -duration_minutes, None

    # 查询出来所有的action_relate_id 并计算所有的积分的和
    original_points = sum(
        record.points if record.process_status == UserActionProcess.ADD.value else -record.points
        for record in original_records
    )
    # 原来的分数是负数

    # 获取用户信息
    original_record = original_records[0]

    # 允许2分钟的误差
    if original_points + duration_minutes > 2 or original_points + duration_minutes <= 0:
        return original_points + duration_minutes, original_record.user_id
    return 0, original_record.user_id


def rollback_user_action_record(user_action_type, action_relate_id, reason):
    """
    视频解锁，添加用户积分记录的 对应回滚记录
    :param reason: 回滚原因
    :param user_action_type: see UserActionType
    :param action_relate_id: 关联操作的业务id
    :return:
    """
    try:
        # 根据2个参数查询有没有对应操作记录
        # original_records = (
        #     LectureUserActionRecord.select()
        #     .where(
        #         (LectureUserActionRecord.user_action_type == user_action_type)
        #         & (LectureUserActionRecord.action_relate_id == action_relate_id)
        #         & (LectureUserActionRecord.delete_flag == 0)
        #     )
        #     .order_by(LectureUserActionRecord.id)
        # )
        # 处理bug任务解析失败后，任务不往下执行问题
        original_records = list(LectureUserActionRecord.select().where(
            (LectureUserActionRecord.user_action_type == user_action_type)
            & (LectureUserActionRecord.action_relate_id == action_relate_id)
            & (LectureUserActionRecord.delete_flag == 0)
        ).order_by(LectureUserActionRecord.id))
        # 如果没有记录则返回
        if len(original_records) == 0:
            return None, 0
        # 查询出来所有的action_relate_id 并计算所有的积分的和
        original_points = sum(
            record.points if record.process_status == UserActionProcess.ADD.value else -record.points
            for record in original_records
        )

        # if original_points >= 0:
        #     return None, 0

        original_record = original_records[0]
        # 创建回滚记录
        LectureUserActionRecord.create(
            user_id=original_record.user_id,
            user_action_type=original_record.user_action_type,
            process_status=UserActionProcess.ADD.value,  # 回滚操作
            process_remark=f"由于 {reason},执行取消积分扣款,回滚记录:{original_record.action_relate_id}",
            action_relate_id=original_record.action_relate_id,
            points=original_points * -1,  # 积分减少
        )

        return original_record.user_id, original_record.points
    except Exception:
        logger.exception("Error adding user point record: ")
        return None, 0


async def get_user_action_record(user_id, page_size, page_no):
    """
    获取用户积分操作记录
    :param user_id: 用户id
    :return:
    """
    try:
        total = (
            LectureUserActionRecord.select()
            .where(LectureUserActionRecord.user_id == user_id, LectureUserActionRecord.delete_flag == 0)
            .count()
        )
        records = (
            LectureUserActionRecord.select()
            .where(LectureUserActionRecord.user_id == user_id, LectureUserActionRecord.delete_flag == 0)
            .order_by(-LectureUserActionRecord.id)
            .paginate(page_no, page_size)
        ).execute()
        return total, records
    except Exception:
        logger.exception("Error getting user point record: ")
        return 0, []
