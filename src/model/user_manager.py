import json
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional

import bcrypt
import jwt
from peewee import AutoField, BooleanField, CharField, DateTimeField, DoesNotExist, IntegerField, Model, TextField
from pydantic import BaseModel, Field, constr
from starlette.exceptions import HTTPException

from config import settings
from src.logger.logUtil import get_logger
from src.orm.pgrepo import db
from src.util.cacheUtil import ttl_cache

logger = get_logger(__name__)


class User_PO(BaseModel):
    id: int
    name: str
    email: str


class UserPO(Model):
    """
    用户表
    """

    id = AutoField(primary_key=True)
    biz_id = CharField(null=False)
    name = CharField(null=False)
    password = CharField(null=False)
    email = CharField(null=False)
    mobile = CharField(null=True)
    wx_open_id = CharField(null=True)
    wx_public_access = BooleanField(default=False)
    mail_access = <PERSON>oleanField(default=False)
    google_open_id = Char<PERSON>ield(null=True)
    create_time = DateTimeField(default=datetime.now)
    update_time = DateTimeField(default=datetime.now)
    delete_flag = IntegerField(default=0)
    head_img = CharField(null=True)
    extra_info = TextField(null=True)  # 新增的字段，用于存储JSON格式的数据
    deletion_request_time = DateTimeField(null=True)  # 新增待注销申请时间
    wx_unionid = CharField(null=True)
    source = CharField(null=True)

    @property
    def extra_info_json(self):
        return json.loads(self.extra_info) if self.extra_info else {}

    @extra_info_json.setter
    def extra_info_json(self, value):
        self.extra_info = json.dumps(value) if value else {}

    class Meta:
        table_name = "lecture_user"
        database = db


class ExtraInfo(BaseModel):
    utm_source: Optional[str] = Field(None, title="用户注册的渠道", description="用户注册的来源")
    utm_medium: Optional[str] = Field(None, title="用户注册的媒介", description="用户注册的媒介")
    utm_term: Optional[str] = Field(None, title="用户注册的关键字", description="用户注册的关键字")
    utm_content: Optional[str] = Field(None, title="用户注册的内容", description="用户注册的内容")
    utm_campaign: Optional[str] = Field(None, title="用户注册的活动", description="用户注册的活动")


class UserVO(BaseModel):
    """
    用户VO, 用于接收前端传递的用户信息
    """

    biz_id: Optional[str] = Field(None, title="业务ID", description="用户的业务ID")
    name: Optional[str] = Field(None, min_length=1, max_length=50, title="姓名", description="用户的姓名")
    password: Optional[str] = Field(None, min_length=8, title="密码", description="用户的密码")
    old_password: Optional[str] = Field(None, min_length=8, title="旧密码", description="用户旧的密码")
    email: Optional[str] = Field(None, title="邮箱", description="用户的邮箱地址")
    # 目前仅支持中国大陆的手机号
    mobile: Optional[constr(pattern=r"^(1[3-9]\d{9})$")] = Field(None, title="手机号", description="用户的手机号码")
    sms_code: Optional[str] = Field(None, title="手机短信验证码", description="用户的手机短信验证码")
    google_open_id: Optional[str] = Field(None, title="Google OpenID", description="用户的Google OpenID")
    wx_openid: Optional[str] = Field(None, title="微信OpenID", description="用户的微信OpenID")
    email_code: Optional[str] = Field(None, title="邮箱验证码", description="用户的邮箱验证码")
    utm_source: Optional[str] = Field(None, title="用户注册的渠道", description="用户注册的来源")
    extra_info: Optional[ExtraInfo] = Field(None, title="额外信息", description="用户的额外信息")
    captcha_verify_param: Optional[dict] = Field({}, title="验证码2.0过来的数据")
    user_type: Optional[int] = Field(1, title="验证方式", description="1: 手机号 2: 微信token 3: 其他")
    state: Optional[str] = Field(None, title="state", description="state")
    code: Optional[str] = Field(None, title="code", description="code")


class LoginUserVO(BaseModel):
    email: str = Field(None, min_length=3, max_length=50)
    username: str = Field(None, min_length=3, max_length=50)
    password: str = Field(..., min_length=8)


class UserMergeVO(BaseModel):
    # 绑定类型 1: 微信 2: 邮箱 3: 手机号
    bind_type: Optional[int] = Field(2, title="绑定类型", description="1: 微信 2: 邮箱 3: 手机号")
    # 绑定的值 微信填accessToken,邮箱填验证码，手机号填验证码
    bind_value: str = Field(...)
    # 绑定的邮箱
    email: Optional[str] = Field(None, title="邮箱", description="用户的邮箱地址")
    # 手机号
    mobile: Optional[str] = Field(None, title="手机号", description="用户的手机号码")
    # 绑定的密码
    password: Optional[str] = Field(None, min_length=8, title="密码", description="用户的密码")


def delete_user(user: UserPO):
    """
    删除用户
    :param user:
    :return:
    """
    # 删除
    try:
        UserPO.update(delete_flag=1).where(UserPO.biz_id == user.biz_id).execute()
        if ttl_cache.get(user.biz_id):
            ttl_cache.delete(user.biz_id)
    except Exception:
        logger.exception("delete user occur error")


def generate_token(user: UserPO):
    payload = {
        "sub": user.email,
        "password": user.password,
        "name": user.name,
        "exp": datetime.now() + timedelta(minutes=10),
    }
    return jwt.encode(payload, "9ce3cbd018ecc84f45a68bdb23ee43d2", algorithm="HS256")


def decode_token(token: str):
    return jwt.decode(token, "9ce3cbd018ecc84f45a68bdb23ee43d2", algorithms=["HS256"])


def get_user_by_wx_id(token: str) -> UserPO:
    return UserPO.get_or_none(wx_open_id=token, delete_flag=0)


def get_user(name) -> Optional[UserPO]:
    try:
        return UserPO.select().where(UserPO.name == name, UserPO.delete_flag == 0)
    except Exception:
        logger.exception("Error getting user: ")
        return None


def get_user_by_email(email) -> Optional[UserPO]:
    try:
        return UserPO.select().where(UserPO.email == email, UserPO.delete_flag == 0).first()
    except Exception:
        logger.exception("Error getting user: ")
        return


def get_user_by_mobile(mobile) -> Optional[UserPO]:
    try:
        return UserPO.select().where(UserPO.mobile == mobile, UserPO.delete_flag == 0).first()
    except Exception:
        logger.exception("Error getting user: ")
        return


def get_user_by_id(id) -> Optional[UserPO]:
    try:
        if not id:
            return None
        user = (UserPO.select().where(UserPO.biz_id == id, UserPO.delete_flag == 0)).first()
        return user
    except DoesNotExist:
        logger.exception(f"User not found, id={id}")
        return None
    except Exception:
        logger.exception("Error getting user")
        return None


def update_user(po: UserPO):
    try:
        UserPO.update(mail_access=True, password=po.password).where(UserPO.biz_id == po.biz_id).execute()
    except Exception:
        logger.exception("Error updating user")
        return False
    return True


async def user_updated(data):
    """
    更新用户
    :param data:
    :return:
    """
    email_address = data.get("email_addresses")[0]
    update_user(
        UserPO(
            biz_id=data.get("id"),
            email=email_address.get("email_address"),
            name=data.get("username") or data.get("last_name") + data.get("first_name"),
            google_open_id=data.get("primary_email_address_id"),
            mobile=len(data.get("phone_numbers")) > 0 and data.get("phone_numbers")[0],
        )
    )

    pass


async def user_deleted(data):
    # 如果username为空则 使用姓+名
    await delete_user(UserPO(dict(biz_id=data.get("id"))))


def hash_password(password: str) -> str:
    salt = bcrypt.gensalt(10, prefix=b"2a")
    hashed_password = bcrypt.hashpw(password.encode(), salt)
    return hashed_password.decode()


def verify_password(plain_password: str, hashed_password: str) -> bool:
    if not plain_password or not hashed_password:
        return False
    return bcrypt.checkpw(plain_password.encode(), hashed_password.encode())


def update_password(plain_password: str, po: UserPO):
    try:
        hashed_password = hash_password(plain_password)
        (UserPO.update(password=hashed_password).where(UserPO.biz_id == po.biz_id))
    except Exception:
        logger.exception("Error updating password: ")
        return False
    return True


def check_is_admin(user_id: str) -> bool:
    if settings.APP_DEBUG:
        return True
    if user_id in settings.CUSTOMER_SERVICE_SEND.split(","):
        return True
    raise HTTPException(status_code=401, detail="没有权限")


async def query_user(wx_unionid: str, pc_open_id: str):
    user = UserPO.select().where(UserPO.wx_unionid == wx_unionid, UserPO.delete_flag.in_([0, 2])).first()
    if not user:
        user = UserPO.select().where(UserPO.wx_open_id == pc_open_id, UserPO.delete_flag.in_([0, 2])).first()
    return user


async def query_user_by_unionid(wx_unionid: str):
    user = UserPO.select().where(UserPO.wx_unionid == wx_unionid, UserPO.delete_flag.in_([0, 2])).first()
    return user
