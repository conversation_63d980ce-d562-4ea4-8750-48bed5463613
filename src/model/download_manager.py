from datetime import datetime, timezone
from enum import Enum

from peewee import AutoField, Char<PERSON>ield, DateTimeField, IntegerField, Model

from src.orm.pgrepo import db


class ExportFormatEnum(Enum):
    DOCX = ".docx"
    PDF = ".pdf"
    HTML = ".html"
    MD = ".md"


class LectureAsyncDownload(Model):
    """
    async downloads
    """

    id = AutoField(primary_key=True, help_text="Primary key")
    biz_id = CharField(help_text="Business ID")
    ai_features = CharField(
        null=True,
        help_text="AI features, including AISummary, textOutline",
    )
    reading_mode = CharField(
        null=True,
        help_text="Reading mode, originalText/polishedVersion",
    )
    display_options = CharField(
        null=True,
        help_text="Display options withImages, bilingual, timestamp, speaker",
    )
    export_format = CharField(
        null=True,
        help_text="Export format .docx/.pdf/.html/.md",
    )
    language = CharField(null=True, help_text="Language")
    relate_biz_id = CharField(null=True, help_text="Related business ID")
    download_type = CharField(
        default="article",
        help_text="Type of the task: article, video, audio",
    )
    status = IntegerField(
        default=1,
        help_text="Current status of the task: 1 - Initialized, 2 - In progress, 3 - Completed 4 -Error",
    )
    download_url = CharField(
        null=True,
        max_length=2048,
        help_text="Download URL",
    )
    remark = CharField(
        null=True,
        help_text="Remark",
    )
    create_time = DateTimeField(default=lambda: datetime.now(tz=timezone.utc), help_text="Creation time")
    update_time = DateTimeField(default=lambda: datetime.now(tz=timezone.utc), help_text="Update time")
    delete_flag = IntegerField(default=0, help_text="Delete flag")

    class Meta:
        table_name = "lecture_async_download"
        database = db


def create_async_downloads():
    pass
