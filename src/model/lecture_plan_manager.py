from datetime import datetime, <PERSON><PERSON><PERSON>
from enum import IntEnum
from typing import Optional

from peewee import AutoField, Char<PERSON><PERSON>, DateTimeField, DecimalField, IntegerField, Model

from src.logger.logUtil import get_logger
from src.orm.pgrepo import db
from src.orm.redis_cache import common_cache

logger = get_logger(__name__)


class LecturePlanType(IntEnum):
    SUBSCRIBE_STANDARD = 1
    SUBSCRIBE_VIP = 2
    POINTS = 3


class LecturePlan(Model):
    """
    url详情
    """

    id = AutoField(primary_key=True)
    biz_id = CharField(null=True)
    title = CharField(null=False)  # 大标题
    sub_title = CharField(null=False)  # 副标题
    viewable = IntegerField(default=1)  # 是否可见 0 不可见 1 可见
    type = IntegerField(null=True)  # 类型 0 免费 1 普通会员 2 高级会员 3 普通会员积分 4 高级会员积分 5 免费积分
    time_unit = CharField(null=True)  # 单位 月 年
    times = IntegerField(null=True)  # 时间 数量
    point = IntegerField(null=True)  # 积分
    amount = DecimalField(null=True)  # 金额
    intro = CharField(null=True)  # 简介
    remark = CharField(null=True)
    extra_params = CharField(null=True)  # 额外参数
    create_time = DateTimeField(default=datetime.now)
    update_time = DateTimeField(default=datetime.now)
    delete_flag = IntegerField(default=0)

    ios_iap_id = CharField(null=True)


    class Meta:
        table_name = "lecture_plan"
        database = db

    def day_of_the_product(self) -> int:
        if self.type == 0:
            return 0
        if self.times is None:
            return 0
        d = 1
        if self.time_unit == "year":
            d = 366
        if self.time_unit == "month":
            d = 31
        if self.time_unit == "day":
            d = 1

        return self.times * d

    def need_update_member_day(self) -> bool:
        if self.type == LecturePlanType.SUBSCRIBE_VIP or self.type == LecturePlanType.SUBSCRIBE_STANDARD:
            return True
        return False


DEFAULT_ONE_MONTH_PLAN = "plan_53d6e361-5145-1a45-a61a-0889e30c4a21"


def get_lecture_plan_or_default(biz_id) -> LecturePlan:
    try:
        if biz_id:
            return get_lecture_plan_by_biz_id(biz_id)
    except Exception as e:
        logger.error("get_lecture_plan_or_default error", e)
    return get_lecture_plan_by_biz_id(DEFAULT_ONE_MONTH_PLAN)


def get_lecture_plan_by_biz_id(biz_id) -> Optional[LecturePlan]:
    """
    Get lecture plan by biz id
    :param biz_id: biz id
    :return: lecture plan
    """
    if not biz_id:
        return None
    plan_list = get_plan_list()
    for plan in plan_list:
        if plan.biz_id == biz_id:
            return plan
    return None


@common_cache.cache_on_arguments(
    namespace="plan:",
    expiration_time=timedelta(minutes=10).total_seconds(),
    should_cache_fn=lambda result: result is not None,
)
def get_plan_list(plan_type: int = None):
    """
    Get All plan list
    :return: plan list
    if type is not None, return plan list by type
    """
    query = LecturePlan.select().where(LecturePlan.delete_flag == 0)
    if plan_type is not None:
        query = query.where(LecturePlan.type == plan_type)
    return list(query.order_by(-LecturePlan.type).execute())
