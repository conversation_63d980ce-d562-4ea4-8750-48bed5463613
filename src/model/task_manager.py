from datetime import datetime, timed<PERSON>ta
from typing import Optional

from peewee import AutoField, CharField, DateTimeField, IntegerField, Model
from playhouse.postgres_ext import <PERSON><PERSON><PERSON><PERSON>
from pydantic import BaseModel, Field, HttpUrl

from src.model.user_manager import UserPO,get_user_by_id
from datetime import timedelta
from src.model.folder import Folder
from src.model.article_manager import LectureArticle
from src.model.article_detail_manager import LectureArticleDetail

from src.logger.logUtil import get_logger
from src.mail.send_mail import send_warn_mail
from src.model.folder import Folder
from src.model.lecture_ai import LectureAITask
from src.orm.pgrepo import db
from src.util.dateUtil import get_current_time_in_beijing
from src.util.cacheUtil import ttl_cache

logger = get_logger(__name__)


class UpdateStatusVO(BaseModel):
    biz_id: str = Field(..., title="任务biz_id", description="任务biz_id")  # The unique identifier for the task
    status: str = Field(..., title="状态", description="任务的当前状态")  # The current status of the task
    remark: str = Field(None, title="备注", description="任务的备注")  # Optional remark or comment about the task
    name: str = Field(None, title="任务名称", description="任务的名称")  # Optional name of the task
    duration_minutes: int = Field(
        0, title="时长（分钟）", description="任务的时长，以分钟为单位"
    )  # Duration of the task in minutes, default is 0
    process_percent: int = Field(
        None, title="处理进度", description="任务的处理进度百分比"
    )  # The percentage of the task that has been completed


TASK_SOURCE_CHOICES = {
    "youtube": "youtube",
    "bilibili": "bilibili",
    "youtubeShort": "youtubeShort",
    "bilibiliShort": "bilibiliShort",
    "upload": "upload",
    "recording":"recording",
}


class LectureTask(Model):
    id = AutoField(primary_key=True)
    name = CharField(null=False)
    url = CharField(null=False)
    duration_minutes = IntegerField(null=True)
    task_source = CharField(null=True)
    start_time = DateTimeField(default=datetime.now)
    end_time = DateTimeField(null=True)
    status = CharField(default="confirm", null=False)
    process_percent = IntegerField(null=True)
    token = CharField(null=True)
    user_id = CharField(null=True)
    open_platform_id = CharField(null=True)
    remark = CharField(null=True)
    author = CharField(null=True)
    biz_id = CharField(null=True)
    order = IntegerField(default=0)
    delete_flag = IntegerField(default=0)
    article_biz_id = CharField(null=True)
    url_biz_id = CharField(null=True)
    img_url = CharField(null=True)

    create_time = DateTimeField(default=datetime.now)
    update_time = DateTimeField(default=datetime.now)
    """
     控制参数 ctrl_params :{
                "video_style": "lecture" // lecture 讲座  interview 访谈  audio 音频
                "video_lan": "zh" // auto 自动  zh 中文  en 英文
                "markdown_lan" "zh" // auto 自动  zh 中文  en 英文
            }
    """
    ctrl_params = JSONField(null=True)
    # md5 = CharField(null=True, max_length=64)
    folder_id = IntegerField(null=True)  # 任务所在文件夹
    point_id = CharField(null=True)
    class Meta:
        table_name = "lecture_tasks"
        database = db

    def to_vo(self):
        return {
            "id": self.id,
            "name": self.name,
            "url": self.url,
            "duration_minutes": self.duration_minutes,
            "task_source": self.task_source,
            "start_time": self.start_time.strftime("%Y-%m-%d %H:%M:%S") if self.start_time else None,
            "end_time": self.end_time.strftime("%Y-%m-%d %H:%M:%S") if self.end_time else None,
            "status": self.status,
            "process_percent": self.process_percent,
            "token": self.token,
            "user_id": self.user_id,
            "open_platform_id": self.open_platform_id,
            "remark": self.remark,
            "author": self.author,
            "biz_id": self.biz_id,
            "order": self.order,
            "article_biz_id": self.article_biz_id,
            "url_biz_id": self.url_biz_id,
            "img_url": self.img_url,
            "ctrl_params": self.ctrl_params,
            "folder": self.folder_id if self.folder_id else 0,
        }


class OpenPlatformTaskVo(BaseModel):
    """
    FastAPI 参数校验模型
    """

    url: HttpUrl = Field(..., max_length=500, description="任务资源 URL")
    task_source: str = Field(..., max_length=100, description="任务来源")
    ctrl_params: dict = Field({}, description="控制字段")


class TaskVO(BaseModel):
    id: int = None
    # 业务id 用UUID 生成str
    biz_id: str = None
    task_source: str = None
    duration_minutes: Optional[int] = None
    name: str = Field(None, max_length=500)
    # 校验是否是url
    url: str = None
    oss_key: str = None
    start_time: str = None
    end_time: str = None
    status: str = "confirm"
    process_percent: int = None
    author: str = None
    token: str = None
    user_id: str = None
    article_biz_id: str = None
    ctrl_params: dict = {}
    url_biz_id: str = None
    img_url: Optional[str] = None
    folder_id: Optional[int] = None


def get_latest_task(task_id: str = None) -> Optional[LectureTask]:
    conditions = [
        (LectureTask.status == "confirm"),
        (LectureTask.delete_flag == 0),
    ]
    if task_id:
        conditions.append(LectureTask.biz_id == task_id)
    task = LectureTask.select().where(*conditions).order_by(-LectureTask.order, LectureTask.id.asc()).first()
    return task


def get_by_biz_id(biz_id: str) -> Optional[LectureTask]:
    return LectureTask.select().where(LectureTask.biz_id == biz_id, LectureTask.delete_flag == 0).first()


def get_article_task_biz_id(user_id, article_biz_id):
    return (
        LectureTask.select().where(LectureTask.user_id == user_id, LectureTask.article_biz_id == article_biz_id).first()
    )


def update_recent_task(user_id):
    """
    更新最近任务
    """
    try:
        arr = list(
            LectureTask.select().where(
                LectureTask.user_id == user_id,
                ((LectureTask.status == "confirm") | (LectureTask.status == "processing")),
                LectureTask.delete_flag == 0,
            )
        )
        if arr and len(arr) >= 1:
            return

        # 获取前3条符合条件的记录
        tasks_to_update = (
            LectureTask.select()
            .where(
                (LectureTask.status == "waiting") & (LectureTask.delete_flag == 0) & (LectureTask.user_id == user_id)
            )
            .first()
        )

        # 更新这些记录
        if tasks_to_update:
            tasks_to_update.status = "confirm"
            tasks_to_update.start_time = get_current_time_in_beijing()
            tasks_to_update.save()
            return 1
        # 返回更新的记录数
        return 0
    except Exception:
        logger.exception("update_recent_task, user_id=%s", user_id)
        return 0  # 返回 0 表示更新失败


def update_unfinished_task():
    # 查询30分钟的
    LectureTask.update(status="fail", remark="处理超时").where(
        (LectureTask.status == "processing")
        & (LectureTask.delete_flag == 0)
        & (LectureTask.start_time < (datetime.now() - timedelta(hours=6)))
    ).execute()

    # 创建时间在2小时到2小时03分之间的任务重试1次
    LectureAITask.update(status="confirm", remark="ai任务处理超时").where(
        (LectureAITask.status == "processing")
        & (LectureAITask.delete_flag == 0)
        & (LectureAITask.start_time < (get_current_time_in_beijing() - timedelta(minutes=2 * 60)))
        & (LectureAITask.start_time >= (get_current_time_in_beijing() - timedelta(minutes=2 * 60 + 3)))
    ).execute()
    # 执行中的任务改成失败
    LectureAITask.update(status="fail", remark="ai任务处理超时").where(
        (LectureAITask.status == "processing")
        & (LectureAITask.delete_flag == 0)
        & (LectureAITask.start_time < (get_current_time_in_beijing() - timedelta(hours=6)))
    ).execute()

    # 发送邮件
    # 今天创建的在处理中和等待中的任务的数量大于10，发送邮件
    error_count = (
        LectureTask.select()
        .where(
            (LectureTask.status == "fail")
            & (LectureTask.delete_flag == 0)
            & (LectureTask.start_time > (datetime.now() - timedelta(minutes=5)))
        )
        .count()
    )

    if error_count >= 2:
        send_warn_mail("<EMAIL>")

    waiting_count = (
        LectureTask.select(LectureTask.user_id)
        .where(
            (LectureTask.status == "waiting")
            & (LectureTask.delete_flag == 0)
            & (LectureTask.start_time > (datetime.now() - timedelta(minutes=5)))
        )
        .group_by(LectureTask.user_id)
        .count()
    )
    if waiting_count >= 3:
        send_warn_mail("<EMAIL>")


def merge_task(current_biz_id, target_biz_id):
    LectureTask.update(user_id=current_biz_id).where(
        LectureTask.delete_flag == 0, LectureTask.user_id == target_biz_id
    ).execute()


def merge_folder(current_biz_id, target_biz_id):
    """将 target_biz_id 下的 Folder 和 Task 归并到 current_biz_id，并删除 target_biz_id"""

    # 获取 current_biz_id 下的所有文件夹名称 -> {name: folder_id}
    current_folders = {
        f.name: f.id
        for f in Folder.select(Folder.id, Folder.name).where(Folder.user_id == current_biz_id, Folder.delete_flag == 0)
    }

    # 遍历 target_biz_id 下面的文件夹
    for folder in Folder.select().where(Folder.user_id == target_biz_id, Folder.delete_flag == 0):
        if folder.name in current_folders:
            # 如果名称重复，迁移 Task 并标记 delete_flag=1
            new_folder_id = current_folders[folder.name]
            LectureTask.update(folder_id=new_folder_id).where(LectureTask.folder_id == folder.id).execute()
            Folder.update(delete_flag=1).where(Folder.id == folder.id).execute()
        else:
            # 否则，直接修改 Folder.user_id 归属于 current_biz_id
            Folder.update(user_id=current_biz_id).where(Folder.id == folder.id).execute()


def check_task_has_created(user_id, article_biz_id, ctrl_params: Optional[dict] = None):
    """
    检查用户是否已经创建过任务
    """
    conditions = [
        (LectureTask.status == "confirm") | (LectureTask.status == "finished") | (LectureTask.status == "finish"),
        LectureTask.delete_flag == 0,
        LectureTask.article_biz_id == article_biz_id,
        LectureTask.user_id == user_id,
    ]
    return LectureTask.select().where(*conditions).first()


# 清理user 及user下关联的数据
def cleanup_user_data(biz_id: str):
    # 删除用户及其相关联的数据
    user = get_user_by_id(biz_id)
    if user:
        # 删除用户任务
        LectureTask.update(delete_flag=1).where(LectureTask.user_id == biz_id).execute()
        # 删除用户文件夹
        Folder.update(delete_flag=1).where(Folder.user_id == biz_id).execute()
        # 删除用户文章
        LectureArticle.update(delete_flag=1).where(LectureArticle.user_id == biz_id).execute()
        # 删除用户文章详情
        LectureArticleDetail.update(delete_flag=1).where(LectureArticleDetail.user_id == biz_id).execute()
        # 删除用户
        UserPO.update(delete_flag=1).where(UserPO.biz_id == biz_id).execute()
        

# 更新待注销的用户
def update_offuser_task():
    # 查找用户遭遇7天前正式环境
    three_days_ago = get_current_time_in_beijing() - timedelta(days=7)
    # 暂时测试早于三分分钟前
    # three_days_ago = get_current_time_in_beijing() - timedelta(minutes=3)
    users_to_delete = UserPO.select().where(
        UserPO.delete_flag == 2,
        UserPO.deletion_request_time <= three_days_ago
    )
    if users_to_delete:
        for user in users_to_delete:
            # 清理用户及其相关联的数据
            cleanup_user_data(user.biz_id)
            ttl_cache.delete(user.biz_id)
    else:
        return "没有要更新的数据"