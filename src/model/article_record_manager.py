import random
from datetime import datetime
from uuid import UUID

from peewee import (
    AutoField,
    CharField,
    DatabaseError,
    DateTimeField,
    IntegrityError,
    Model,
    SmallIntegerField,
)
from playhouse.postgres_ext import <PERSON><PERSON><PERSON><PERSON>
from wechatpy.fields import <PERSON><PERSON>ield

from src.logger.logUtil import get_logger
from src.orm.pgrepo import db

logger = get_logger(__name__)


class LectureArticleRecord(Model):
    """
    划线部分记录
    """
    id = AutoField(primary_key=True)
    biz_id = CharField(null=False, unique=True)
    article_detail_id = CharField(null=False)
    type_name = Char<PERSON><PERSON>(null=False, max_length=10)
    user_id = CharField(null=False, default="system")
    record_id = CharField(null=False, unique=True)
    context_json = JSONField(null=True)
    create_time = DateTimeField(default=datetime.now)
    update_time = DateTimeField(default=datetime.now)
    delete_flag = SmallIntegerField(default=0)

    class Meta:
        table_name = "lecture_article_record"
        database = db


async def get_all_details_by_article_biz_id(article_detail_id):
    return LectureArticleRecord.select().where(
        (LectureArticleRecord.article_detail_id == article_detail_id) & (LectureArticleRecord.delete_flag == 0)
    )


async def get_article_record_by_record_id(record_id):
    """根据前端记录Id 查询记录"""
    return (
        LectureArticleRecord.select(LectureArticleRecord.id)
        .where((LectureArticleRecord.record_id == record_id) & (LectureArticleRecord.delete_flag == 0))
        .first()
    )




def create_article_record(**kwargs):
    try:
        biz_id = str(UUID(int=random.getrandbits(128)))

        article_record = LectureArticleRecord.create(
            biz_id=biz_id,
            article_detail_id=kwargs.get("article_detail_id"),
            user_id=kwargs.get('user_id'),
            record_id=kwargs.get('record_id')
        )
        return article_record  # Return the created instance
    except IntegrityError as e:
        logger.exception(f"Error creating article detail，article_id={kwargs.get('article_id')}", e)
        return None
    except DatabaseError as e:
        logger.exception(f"Error creating article detail，article_id={kwargs.get('article_id')}", e)
        return None
    except Exception as e:
        logger.exception(f"Error creating article detail，content={kwargs}", e)
        return None
