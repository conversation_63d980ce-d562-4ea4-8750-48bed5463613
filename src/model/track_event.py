from peewee import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Model
from playhouse.postgres_ext import <PERSON><PERSON><PERSON><PERSON>

from src.orm.pgrepo import db


class TrackEvent(Model):
    """
    前端埋点数据存储到数据库
    """

    id = AutoField(primary_key=True)
    session_id = CharField(null=False)  # 会话 ID
    user_id = CharField(null=True)  # 用户 ID
    event_time = CharField(null=False)  # 请求时间
    event_type = CharField(null=False)  # 事件类型
    channel_id = CharField(null=True)  # 渠道 ID
    event_data = JSONField()
    # 以下字段在 event_data 里
    # ip = IPField(null=False) # IP
    # user_agent = CharField(null=True) # 客户端
    # called_url = CharField(null=True) # 访问 url

    class Meta:
        table_name = "track_event"
        database = db
