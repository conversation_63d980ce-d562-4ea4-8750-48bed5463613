from urllib.parse import urljoin

from fastapi import APIRouter, Depends
from pydantic import BaseModel
from starlette.requests import Request

from config import settings
from src.database import enums, constants
from src.model.lecture_plan_manager import get_lecture_plan_by_biz_id
from src.model.order_manager import (
    LectureOrders,
    OrderStatus,
    PaymentType,
    generate_order_str,
    get_order_from_id,
    order_call_back,
)
from src.orm.redis_cache import common_cache
from src.util.TokenUtil import get_current_user
from src.util.celery_util import sem_to_baidu
from src.util.commonutil import CommonResponse, ErrorCodeEnum, common_enum_error, common_error, common_success
from src.util.dateUtil import get_beijing_time
from .utils import ALIPAY_GATEWAY, alipay, alipay_app, logger

router = APIRouter(prefix="/api/v2/order/alipay", tags=["alipay"])


class OrderRequest(BaseModel):
    product_id: str
    create_timestamp: int


# 支付宝PC端支付
@router.post("/create", response_model=CommonResponse, summary="创建pc端支付订单")
async def create_order(request: Request, req: OrderRequest, user=Depends(get_current_user)):
    user_id = user.biz_id
    plan = get_lecture_plan_by_biz_id(req.product_id)
    if not plan:
        return common_enum_error(ErrorCodeEnum.UNKNOWN_PRODUCT)

    amount = plan.amount

    if settings.APP_DEBUG:
        amount = 0.01

    now = get_beijing_time()
    source = request.headers.get(constants.HEADER_SOURCE, enums.SourceEnum.UNKNOWN.value)
    new_order = LectureOrders.create(
        user_id=user_id,
        product_id=plan.biz_id,
        option=plan.title,
        amount=amount,
        status=OrderStatus.PENDING,
        payment_type=PaymentType.ALIPAY,
        created_at=now,
        updated_at=now,
        source=source
    )

    new_order.order_id = generate_order_str(new_order.id)
    new_order.save()

    try:
        order_string = alipay.api_alipay_trade_page_pay(
            out_trade_no=new_order.order_id,
            total_amount=float(amount),
            subject=plan.title,
            qr_pay_mode=4,
            qrcode_width=150,
            notify_url=urljoin(settings.DOMAIN, "/api/v2/order/alipay/status"),
        )
        pay_url = f"{ALIPAY_GATEWAY}?{order_string}"
    except Exception:
        logger.error("alipay payment error", exc_info=True)
        return common_error(code=ErrorCodeEnum.WECHAT_PAY_ERROR.error_code, message="支付宝支付失败")
    return common_success({"url": pay_url, "order_id": new_order.order_id})


@common_cache.cache_on_arguments(namespace="order:lock:", expiration_time=5)
def get_last_order_lock(timestamp: int, _user_id: str):
    """
    Get All plan list
    :return: plan list
    """
    return timestamp


# 支付宝移动端支付
@router.post("/mobile/create", response_model=CommonResponse, summary="创建移动端支付订单")
async def create_mobile_order(request: Request, req: OrderRequest, user=Depends(get_current_user)):
    user_id = user.biz_id
    plan = get_lecture_plan_by_biz_id(req.product_id)
    if not plan:
        return common_enum_error(ErrorCodeEnum.UNKNOWN_PRODUCT)
    amount = plan.amount
    if req.create_timestamp:
        if common_cache.get(f"order:lock:{req.create_timestamp}:{user_id}"):
            return common_enum_error(ErrorCodeEnum.ORDER_LOCKED)
        get_last_order_lock(req.create_timestamp, user_id)

    if settings.APP_DEBUG:
        amount = 0.01
    now = get_beijing_time()
    source = request.headers.get(constants.HEADER_SOURCE, enums.SourceEnum.UNKNOWN.value)
    new_order = LectureOrders.create(
        user_id=user_id,
        product_id=plan.biz_id,
        option=plan.title,
        amount=amount,
        status=OrderStatus.PENDING,
        payment_type=PaymentType.MOBILE_ALIPAY,
        created_at=now,
        updated_at=now,
        source=source
    )
    new_order.order_id = generate_order_str(new_order.id)
    new_order.save()
    try:
        order_string = alipay_app.api_alipay_trade_app_pay(
            out_trade_no=new_order.order_id,
            total_amount=float(amount),
            subject=plan.title,
            notify_url=urljoin(settings.DOMAIN, "/api/v2/order/alipay/mobile/status"),
            product_code=new_order.option
        )
        notify_url = urljoin(settings.DOMAIN, "/api/v2/order/alipay/mobile/status")
        logger.info(f"mobil notify url:{notify_url}")
        # pay_url = f"{ALIPAY_GATEW}?{order_string}"
    except Exception:
        logger.error("alipay payment error", exc_info=True)
        return common_error(code=ErrorCodeEnum.ALIPAY_MOBILE_ERROR.error_code, message="移动端支付宝支付失败")
    return common_success({"url": order_string, "order_id": new_order.order_id})


# @router.post("/refund", response_model=CommonResponse, summary="取消订单")
# async def refund_order(order_id: str, user=Depends(get_current_user)):
#     order = LectureOrders.get_or_none(LectureOrders.order_id == order_id, LectureOrders.user_id == user.biz_id)
#     if not order:
#         return common_error("订单不存在")
#     if order.payment_type == PaymentType.ALIPAY and order.status == OrderStatus.SUCCESS:
#         refund_data = alipay.api_alipay_trade_refund(
#             refund_amount=float(order.amount), out_trade_no=order.order_id, trade_no=order.transaction_id
#         )
#         if refund_data["msg"] == "Success" and data["fund_change"] == "Y":
#             trade_no = refund_data.get("trade_no")
#             if trade_no:
#                 counts = (
#                     LectureOrders.update(
#                         status=OrderStatus.REFUNDED, transaction_id=trade_no, payment_result=refund_data
#                     )
#                     .where((LectureOrders.order_id == order_id) & (LectureOrders.status == OrderStatus.SUCCESS))
#                     .execute()
#                 )
#                 if counts > 0:
#                     # TODO 回退到用户未付款该订单之前
#                     logger.info("Order updated successfully: %s", order.__data__)
#                 else:
#                     logger.error("Order updated failed: %s", order.__data__)
#                     return common_enum_error(ErrorCodeEnum.ALIPAY_ERROR)
#         return common_success(order.status)


@router.post("/status")
async def alipay_status(request: Request):
    """
    支付宝异步通知接口
    """
    form_data = await request.form()
    data = dict(form_data)
    signature = data.pop("sign", None)
    success = alipay.verify(data, signature)
    if success:
        # 验签成功，处理业务逻辑
        await handle_alipay_success(data)
        sem_to_baidu(order_id=data["out_trade_no"])
        return common_success({"code": "pay successed"})
    return common_success({"code": "pay failed"})


@router.post("/mobile/status")
async def alipay_mobile_status(request: Request):
    """
    支付宝移动端异步通知接口
    """
    form_data = await request.form()
    # query_dat = request.query_params
    data = dict(form_data)
    signature = data.pop("sign", None)
    # keys = ['body', 'buyer_id', 'charset', 'gmt_close', 'gmt_payment', 'notify_time', 'notify_type', 'out_trade_no',
    #  'refund_fee', 'subject', 'total_amount', 'trade_no', 'trade_status', 'version']
    # signa_data = dict()
    # for key, value in data.items():
    #     if key in keys:
    #         signa_data[key] = value
    success = alipay_app.verify(data, signature)
    if success:
        # 验签成功，处理业务逻辑
        finish = await handle_alipay_success(data, True)
        if finish:
            sem_to_baidu(order_id=data["out_trade_no"])
            return 'success'
        return 'fail'
    logger.error(f"验签失败 \n {data}")
    return 'fail'


async def handle_alipay_success(data: dict, mobile: bool = False):
    transaction_id = data["trade_no"]  # 支付宝交易订单
    amount = data["total_amount"]  # 交易金额
    out_trade_no = data["out_trade_no"]  # LectureOrders 订单

    # Log the extracted information
    logger.info("Transaction ID: %s, Amount: %s", transaction_id, amount)

    payment_type = PaymentType.ALIPAY
    if mobile:
        logger.info(f"mobile alipay: transaction_id={transaction_id} \nout_trade_no={out_trade_no}")
        payment_type = PaymentType.MOBILE_ALIPAY
    # Update the order status in the database
    order = get_order_from_id(out_trade_no)
    if order:
        if order.status == OrderStatus.SUCCESS:
            logger.info("Order already updated: %s", order.__data__)
            logger.info(f"Order already updated: \n {data}")
            return False
        # Update the order in the database

        trade_status = data.get('trade_status')
        # 支付成功
        if 'TRADE_SUCCESS' == trade_status or 'TRADE_FINISHED' == trade_status:
            counts = (
                LectureOrders.update(
                    status=OrderStatus.SUCCESS,
                    transaction_id=transaction_id,
                    payment_type=payment_type,
                    payment_result=data,
                )
                .where(
                    (LectureOrders.order_id == out_trade_no)
                    & (LectureOrders.status != OrderStatus.SUCCESS)
                    & (LectureOrders.payment_type == payment_type)
                )
                .execute()
            )

            logger.info("Order updated successfully: %s", order.__data__)

            # Call the order callback
            if counts > 0:
                await order_call_back(order)
            return True
        elif 'TRADE_CLOSED' == trade_status:
            logger.error(f"交易关闭 \n {data}")
            counts = (
                LectureOrders.update(
                    status=OrderStatus.CANCELED,
                    transaction_id=transaction_id,
                    payment_type=payment_type,
                    payment_result=data,
                )
                .where(
                    (LectureOrders.order_id == out_trade_no)
                    & (LectureOrders.status == OrderStatus.PENDING)
                    & (LectureOrders.payment_type == payment_type)
                )
                .execute()
            )
            return True
        else:
            logger.error(f"交易失败 \n {data}")
            return False
    else:
        logger.warn("Order not found for transaction ID: %s", transaction_id)
        return False
