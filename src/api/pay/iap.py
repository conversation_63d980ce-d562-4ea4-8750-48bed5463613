import uuid

from appstoreserverlibrary.api_client import AppStoreServerAPIClient, APIException
from appstoreserverlibrary.models.ConsumptionRequest import ConsumptionRequest
from appstoreserverlibrary.models.DeliveryStatus import DeliveryStatus
from appstoreserverlibrary.models.Environment import Environment
from appstoreserverlibrary.models.NotificationTypeV2 import NotificationTypeV2
from appstoreserverlibrary.models.Platform import Platform
from appstoreserverlibrary.models.UserStatus import UserStatus
from appstoreserverlibrary.signed_data_verifier import SignedDataVerifier
from fastapi import APIRouter, Depends
from pydantic import BaseModel
from starlette.requests import Request

from config import settings
from src.database import constants, enums
from src.logger.logUtil import get_logger
from src.model.lecture_plan_manager import get_lecture_plan_by_biz_id
from src.model.member_manager import LectureMember
from src.model.order_manager import (
    LectureOrders,
    OrderStatus,
    PaymentType,
    get_order_from_id,
    order_call_back, order_refund_call_back, get_order_from_status
)
from src.model.task_manager import LectureTask
from src.util.TokenUtil import get_current_user
from src.util.third_party.apple_client import apple_client_manager
from src.util.third_party.apple_service import apple_pay_service
from src.util.third_party.apple_value import get_account_age_category, get_account_play_time, get_purchased, \
    get_refunded
from src.util.commonutil import CommonResponse, ErrorCodeEnum, common_enum_error, common_error, common_success
from src.util.dateUtil import get_beijing_time
from src.model.user_manager import get_user_by_id

logger = get_logger(__name__)


def load_root_certificates():
    """加载苹果根证书"""
    cert_paths = [
        'static/pay/AppleRootCA-G3.cer',
        'static/pay/AppleRootCA-G2.cer',
        'static/pay/AppleIncRootCertificate.cer',
    ]
    certs = []
    for path in cert_paths:
        try:
            # 证书文件是二进制格式，需要用 'rb' 模式读取
            with open(path, 'rb') as file:
                cert_data = file.read()
                certs.append(cert_data)

        except FileNotFoundError:
            logger.warning(f"⚠️ 证书文件不存在: {path}")
        except Exception as e:
            logger.error(f"❌ 加载证书失败 {path}: {str(e)}")

    return certs


def create_apple_api_client(environment=None):
    """
    创建 App Store Server API 客户端

    Args:
        environment: 环境选择，可选值：
                    - Environment.SANDBOX: 沙盒环境
                    - Environment.PRODUCTION: 生产环境
                    - None: 使用默认环境（根据 settings.APP_DEBUG 判断）
    """
    try:
        with open("static/pay/AuthKey_K2FBY6A2CU.p8", "r") as f:
            private_key = f.read()

        # 确定使用的环境
        if environment is None:
            # 如果没有指定环境，使用默认环境
            target_environment = Environment.SANDBOX if settings.APP_DEBUG else Environment.PRODUCTION
        else:
            # 使用指定的环境
            target_environment = environment

        # 使用官方库创建客户端
        client = AppStoreServerAPIClient(
            signing_key=private_key.encode('utf-8'),  # 转换为 bytes 类型
            key_id=settings.APPLE_SECRET_KEY_ID,
            issuer_id=settings.APPLE_ISSUER_ID,
            bundle_id=settings.APPLE_BUNDLE_ID,
            environment=target_environment
        )

        return client

    except Exception as e:
        logger.error(f"❌ Apple API 客户端创建失败: {str(e)}")
        return None


# 全局 Apple API 客户端实例（使用默认环境）
apple_api_client = create_apple_api_client(Environment.PRODUCTION)


def create_signed_data_verifier(environment=None):
    """创建 SignedDataVerifier 用于验证和解析 JWS"""
    try:
        # 加载苹果根证书
        root_certs = load_root_certificates()

        # 确定使用的环境
        if environment is None:
            # 如果没有指定环境，使用默认环境
            target_environment = Environment.SANDBOX if settings.APP_DEBUG else Environment.PRODUCTION
        else:
            # 使用指定的环境
            target_environment = environment

        # 根据官方文档，SignedDataVerifier 需要苹果的根证书
        verifier = SignedDataVerifier(
            root_certificates=root_certs,
            enable_online_checks=True,
            environment=target_environment,
            bundle_id=settings.APPLE_BUNDLE_ID,
            app_apple_id=settings.APPLE_APP_ID  # 沙盒环境可以为 None
        )

        return verifier
    except Exception as e:
        logger.error(f"❌ SignedDataVerifier 创建失败: {str(e)}")
        logger.error(f"详细错误信息: {repr(e)}")
        import traceback
        logger.error(f"堆栈跟踪: {traceback.format_exc()}")
        return None


# 全局 SignedDataVerifier 实例
signed_data_verifier = create_signed_data_verifier(Environment.PRODUCTION)

router = APIRouter(prefix="/api/v1/order/apple_pay", tags=["apple_pay"])


class OrderRequest(BaseModel):
    product_id: str
    amount: float
    create_timestamp: int


class OrderResponse(BaseModel):
    transaction_id: str
    environment: str
    # order_id: str
    # app_account_token: Optional[str] = None


@router.post("/iap/create", response_model=CommonResponse, summary="创建苹果内购支付订单")
async def create_iap_order(req: OrderRequest, request: Request, user=Depends(get_current_user)):
    try:
        user_id = user.biz_id
        plan = get_lecture_plan_by_biz_id(req.product_id)
        if not plan:
            return common_enum_error(ErrorCodeEnum.UNKNOWN_PRODUCT)

        amount = req.amount  # 使用客户端传入的金额

        now = get_beijing_time()
        order_uuid = uuid.uuid4()

        source = request.headers.get(constants.HEADER_SOURCE, enums.SourceEnum.UNKNOWN.value)
        new_order = LectureOrders.create(
            user_id=user_id,
            product_id=plan.biz_id,
            option=plan.title,
            amount=amount,
            status=OrderStatus.PENDING,
            payment_type=PaymentType.APPLE_PAY,
            created_at=now,
            updated_at=now,
            source=source
        )
        new_order.order_id = order_uuid
        new_order.save()

        # 返回给客户端的数据，包含关联信息
        order_string = {"ios_iap_id": plan.ios_iap_id, "order_id": new_order.order_id}
        logger.info(f"📋 订单信息：：{order_string}")
        return common_success(order_string)
    except Exception as e:
        return common_error("订单创建失败")


@router.post("/iap/verify", response_model=CommonResponse, summary="验证苹果内购支付结果")
async def verify_iap_order(resp: OrderResponse, user=Depends(get_current_user)):
    """
    验证苹果内购支付结果
    """
    try:
        # 提取请求参数
        transaction_id = resp.transaction_id
        environment = resp.environment

        # 验证必要参数
        if not transaction_id:
            logger.error("❌ transaction_id 参数缺失")
            return common_error("transaction_id 参数缺失")

        # 使用服务层验证交易
        transaction_info, parsed_transaction, error = apple_pay_service.verify_transaction(transaction_id, environment)
        if error:
            logger.error(f"❌ 交易验证失败: {error}")
            return common_error(error)

        if not transaction_info or not parsed_transaction:
            logger.error(f"❌ 未能获取到交易信息: {transaction_id}")
            return common_error("无法获取交易信息")

        # 提取关键字段
        app_account_token = parsed_transaction.appAccountToken
        transaction_reason = parsed_transaction.transactionReason
        apple_transaction_id = parsed_transaction.transactionId

        # 验证并处理订单
        if app_account_token and transaction_reason == "PURCHASE" and apple_transaction_id == transaction_id:
            # logger.info("✅ 这是一个有效的购买交易，开始处理订单")

            # 根据 app_account_token 查找订单
            order = get_order_from_id(app_account_token)

            if order:

                if order.status == OrderStatus.SUCCESS:
                    # logger.info("⚠️ 订单已经是成功状态，无需重复处理")

                    return common_success({"message": "订单已处理", "order_id": order.order_id})
                else:
                    # 更新订单状态
                    try:
                        counts = (
                            LectureOrders.update(
                                status=OrderStatus.SUCCESS,
                                transaction_id=apple_transaction_id,
                                payment_result=str(parsed_transaction),
                            ).where((LectureOrders.order_id == app_account_token) & (
                                    LectureOrders.status == OrderStatus.PENDING)
                                    & (LectureOrders.payment_type == PaymentType.APPLE_PAY)).execute()
                        )

                        if counts > 0:
                            await order_call_back(order)
                            # logger.info(f"✅ 订单更新成功，影响行数: {counts}")

                            return common_success({"message": "订单支付成功", "order_id": app_account_token})
                        else:
                            logger.warning("⚠️ 订单更新失败，可能已被其他进程处理")

                            return common_error("订单更新失败")

                    except Exception as e:
                        logger.error(f"❌ 更新订单时发生错误: {str(e)}")

                        return common_error(f"更新订单失败: {str(e)}")
            else:
                logger.error(f"❌ 未找到对应的订单，app_account_token: {app_account_token}")

                return common_error("未找到对应的订单")
        else:
            logger.error(f"❌ 订单校验失败，app_account_token: {app_account_token}")

            return common_error("未找到对应的订单")
    except Exception as e:
        # 其他异常
        logger.error(f"❌ 验证过程中发生未知错误: {str(e)}")
        logger.error(f"错误类型: {type(e).__name__}")
        import traceback

        logger.error(f"堆栈跟踪: {traceback.format_exc()}")
        return common_error(f"验证失败: {str(e)}")


@router.post("/iap/notify", summary="苹果内购支付通知")
async def apple_pay_notify(request: Request):
    """
    处理苹果内购支付通知 - 调试版本
    记录所有接收到的参数以便分析
    """
    try:
        logger.info("🔔 收到苹果支付通知")

        # 初始化变量
        json_data = None

        try:
            json_data = await request.json()
            if json_data and 'signedPayload' in json_data:
                signed_payload = json_data['signedPayload']
                logger.info(f"🔍 开始解析 signedPayload... ")


                # 2. 使用服务层解析通知
                environment = Environment.SANDBOX if settings.APP_DEBUG else Environment.PRODUCTION
                notification_data, error = apple_pay_service.parse_notification(signed_payload, environment)
                if error:
                    logger.error(f"❌ 通知解析失败: {error}")


                if not notification_data:
                    logger.warning("⚠️ 未能解析通知数据")


                notification_uuid = notification_data.notificationUUID
                notification_type = notification_data.rawNotificationType
                logger.info(f"🎯 开始处理通知数据... 类型: {notification_type}, UUID: {notification_uuid}")
                # 5. 提取交易信息
                data = notification_data.data
                transaction_info = data.signedTransactionInfo
                # 使用服务层解析交易信息
                data_verifier = apple_client_manager.get_data_verifier(Environment.SANDBOX if settings.APP_DEBUG else Environment.PRODUCTION)
                transaction_result = data_verifier.verify_and_decode_signed_transaction(data.signedTransactionInfo)

                transactionId = transaction_result.transactionId  # "****************"
                productId = transaction_result.productId  # com.aihaoji.vip_lifetime
                appAccountToken = transaction_result.appAccountToken  # a6b16036-839c-4f00-a3c3-beddeee87430
                environment = transaction_result.environment  # Sandbox
                transactionReason = transaction_result.transactionReason  # PURCHASE
                currency = transaction_result.currency  # "CNY"
                price = transaction_result.price  # 2348000,

                match notification_data.notificationType:
                    case NotificationTypeV2.REFUND:  # 退款已经被处理，用户收到退款
                        logger.info(f"订单: {appAccountToken} 已退款")
                        order_refund(appAccountToken)

                    case NotificationTypeV2.CONSUMPTION_REQUEST:  # 对于消耗品应用内购，请求提供消费信息以便进行退款处理。
                        logger.info("请求提供消费信息以便进行退款处理。")
                        consumption_request(transactionId, appAccountToken, apple_client)

                    case NotificationTypeV2.REVOKE:  # 已撤销订阅或购买，通常是在退款后发生。
                        logger.info("已撤销订阅或购买")
                    case NotificationTypeV2.ONE_TIME_CHARGE:  # 一次性购买通知，表示用户完成了一次性非续订类型的购买
                        logger.info(f"订单:{appAccountToken} 一次性购买通知")

                    case NotificationTypeV2.REFUND_DECLINED:  # 退款被拒绝
                        logger.info(f"订单:{appAccountToken} 退款被拒绝")

                    case NotificationTypeV2.TEST:  # 测试通知
                        logger.info("这是一条测试信息")
                    case _:
                        logger.warning(f"⚠️ 未知通知类型: {notification_data.rawNotificationType}")



        except Exception as json_error:
            logger.error(f"❌ 解析JSON数据失败: {str(json_error)}")
            pass

        # 返回符合苹果要求的响应
        # 苹果只需要 HTTP 200 状态码，响应体可以为空或简单确认
        from fastapi import Response

        # 返回简单的确认响应（符合苹果要求）
        return Response(status_code=200, content="OK")

    except Exception as e:
        logger.error(f"Apple Pay notification debug error: {str(e)}", exc_info=True)
        # 即使出错也要返回 200，避免苹果重试
        from fastapi import Response
        return Response(status_code=200, content="ERROR")


async def consumption_request(transaction_id: str, order_id: str, apple_client: AppStoreServerAPIClient):
    try:
        request_params = ConsumptionRequest()
        order = get_order_from_id(order_id)
        user_id = order.user_id
        order_list = get_order_from_status(user_id, [OrderStatus.SUCCESS, OrderStatus.REFUNDED])
        user = get_user_by_id(order.user_id)
        # 计算总金额和退款总额
        total_amount = 0  # 总支付金额
        refund_amount = 0  # 退款总额
        for order in order_list:
            total_amount += float(order.amount)
            if order.status == OrderStatus.REFUNDED:
                refund_amount += float(order.amount)

        try:
            member = LectureMember.get(LectureMember.user_id == order.user_id)
        except LectureMember.DoesNotExist:
            logger.warning(f'订单：{order_id} 没有找到对应会员：{order.user_id} 的信息')

        if user:
            request_params.accountTenure = get_account_age_category(user.create_time)  # 账户年龄
            request_params.appAccountToken = order_id  # 订单ID
            # request_params.consumptionStatus= #消耗情况
            request_params.customerConsented = True
            request_params.deliveryStatus = DeliveryStatus.DELIVERED_AND_WORKING_PROPERLY
            request_params.lifetimeDollarsPurchased = get_purchased(total_amount)  # 所有平台购买总额
            request_params.lifetimeDollarsRefunded = get_refunded(refund_amount)
            request_params.platform = Platform.APPLE
            request_params.playTime = get_account_play_time(request_params.accountTenure)
            # request_params.refundPreference=#根据运营逻辑决定是否需要退款
            request_params.sampleContentProvided = True
            request_params.userStatus = UserStatus.ACTIVE  # 账户活跃状态

            apple_client.send_consumption_data(transaction_id, request_params)
            logger.info(f"订单{order_id} ")
    except LectureOrders.DoesNotExist:
        logger.warning(f"订单：{order_id} 不存在")


async def order_refund(orderId: str):
    order = get_order_from_id(orderId)

    if order:
        # logger.info(f"📋 找到订单: {order.order_id}, 当前状态: {order.status}")

        if order.status == OrderStatus.SUCCESS:

            # 更新订单状态
            try:
                now = get_beijing_time()
                counts = (
                    LectureOrders.update(status=OrderStatus.REFUNDED, updated_at=now).where(
                        (LectureOrders.order_id == orderId)
                        & (LectureOrders.status == OrderStatus.SUCCESS)
                        & (LectureOrders.payment_type == PaymentType.APPLE_PAY))
                    .execute()
                )

                if counts > 0:
                    await order_refund_call_back(order)
                    logger.info(f"✅ 退款订单: {orderId} 更新成功，影响行数: {counts}")
                else:
                    logger.warning(f"⚠️ 订单{orderId}更新失败，可能已被其他进程处理")



            except Exception as e:
                logger.error(f"❌ 更新订单时发生错误: {str(e)}")


    else:
        logger.error(f"❌ 未找到对应的订单，orderId : {orderId}")
