from fastapi import Request

from src.model.order_manager import LectureOrders, OrderStatus, PaymentType, get_order_from_id, order_call_back
from src.util.commonutil import common_success
from .ali import router as ali_router
from .utils import alipay, logger


@ali_router.post("/status")
async def alipay_status(request: Request):
    """
    支付宝异步通知接口
    """
    form_data = await request.form()
    data = dict(form_data)
    signature = data.pop("sign", None)
    success = alipay.verify(data, signature)
    if success:
        # 验签成功，处理业务逻辑
        await handle_alipay_success(data)
        return common_success({"code": "pay successed"})
    return common_success({"code": "pay failed"})


async def handle_alipay_success(data: dict):
    transaction_id = data["trade_no"]  # 支付宝交易订单
    amount = data["total_amount"]  # 交易金额
    out_trade_no = data["out_trade_no"]  # LectureOrders 订单

    # Log the extracted information
    logger.info("Transaction ID: %s, Amount: %s", transaction_id, amount)

    # Update the order status in the database
    order = get_order_from_id(out_trade_no)
    if order:
        if order.status == OrderStatus.SUCCESS:
            logger.info("Order already updated: %s", order.__data__)
            return
        # Update the order in the database
        counts = (
            LectureOrders.update(
                status=OrderStatus.SUCCESS,
                transaction_id=transaction_id,
                payment_type=PaymentType.ALIPAY,
                payment_result=data,
            )
            .where(
                (LectureOrders.order_id == out_trade_no)
                & (LectureOrders.status != OrderStatus.SUCCESS)
                & (LectureOrders.payment_type == PaymentType.ALIPAY)
            )
            .execute()
        )

        logger.info("Order updated successfully: %s", order.__data__)

        # Call the order callback
        if counts > 0:
            await order_call_back(order)
    else:
        logger.warn("Order not found for transaction ID: %s", transaction_id)
