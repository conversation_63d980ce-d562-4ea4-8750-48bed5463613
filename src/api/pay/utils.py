from alipay import AliP<PERSON>

from src.logger.logUtil import get_logger

ALIPAY_APP_ID = "2021004197684054"
ALIPAY_GATEWAY = "https://openapi.alipay.com/gateway.do"
app_private_key_string = open("static/pay/apiPrivateKey.key").read()
alipay_public_key_string = open("static/pay/alipayPublicCert.pem").read()


ALIPAY_MOBILE_APP_ID="2021005131626388"
app_mobile_private_key_string = open("static/pay/apiPrivateMobileKey.key").read()
alipay_mobile_public_key_string = open("static/pay/alipayPublicCert.pem").read()

logger = get_logger(__name__)

alipay_app = AliPay(
    appid=ALIPAY_MOBILE_APP_ID,
    app_notify_url=None,  # the default notify path
    app_private_key_string=app_mobile_private_key_string,
    # alipay public key, do not use your own public key!
    alipay_public_key_string=alipay_mobile_public_key_string,
    sign_type="RSA2",  # RSA or RSA2
    debug=False,  # False by default
    verbose=False,  # useful for debugging
)

alipay = AliPay(
    appid=ALIPAY_APP_ID,
    app_notify_url=None,  # the default notify path
    app_private_key_string=app_private_key_string,
    # alipay public key, do not use your own public key!
    alipay_public_key_string=alipay_public_key_string,
    sign_type="RSA2",  # RSA or RSA2
    debug=False,  # False by default
    verbose=False,  # useful for debugging
)
