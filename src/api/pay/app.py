from typing import Optional
from urllib.parse import urljoin

from fastapi import APIRouter, Depends, Request
from pydantic import BaseModel, HttpUrl

from config import settings
from src.database import enums, constants
from src.model.lecture_plan_manager import get_lecture_plan_by_biz_id
from src.model.order_manager import LectureOrders, OrderStatus, PaymentType, generate_order_str
from src.util.TokenUtil import get_current_user
from src.util.commonutil import CommonResponse, ErrorCodeEnum, common_enum_error, common_error, common_success
from src.util.dateUtil import get_beijing_time
from src.util.wechat_oauth import WechatBaseClient
from .utils import logger

router = APIRouter(prefix="/api/v2/order/app-pay", tags=["app-pay"])


class OrderRequest(BaseModel):
    product_id: str
    create_timestamp: int
    payment_type: PaymentType
    logid_url: Optional[HttpUrl] = None


@router.post("/create_order", response_model=CommonResponse, summary="创建订单")
async def create_order(request: Request, req: OrderRequest, user=Depends(get_current_user)):
    if req.payment_type not in [PaymentType.ALIPAY, PaymentType.WE_CHAT]:
        return common_error(f"不支付 {req.payment_type} 支付")
    user_id = user.biz_id
    plan = get_lecture_plan_by_biz_id(req.product_id)
    if not plan:
        return common_enum_error(ErrorCodeEnum.UNKNOWN_PRODUCT)

    amount = plan.amount

    if settings.APP_DEBUG:
        amount = 0.01

    now = get_beijing_time()
    source = request.headers.get(constants.HEADER_SOURCE, enums.SourceEnum.UNKNOWN.value)
    new_order = LectureOrders.create(
        user_id=user_id,
        product_id=plan.biz_id,
        option=plan.title,
        amount=amount,
        status=OrderStatus.PENDING,
        payment_type=req.payment_type,
        created_at=now,
        updated_at=now,
        source=source
    )

    new_order.order_id = generate_order_str(new_order.id)
    new_order.save()
    logger.info("Order created: %s, payment_type: %s", new_order.order_id, req.payment_type)

    if req.payment_type == PaymentType.ALIPAY:
        ...
        # 支付宝的暂时没有接入
        # order_string = alipay.api_alipay_trade_wap_pay(
        #     out_trade_no=new_order.order_id,
        #     total_amount=float(amount),
        #     subject=plan.title,
        #     qr_pay_mode=4,
        #     qrcode_width=150,
        #     notify_url=urljoin(settings.DOMAIN, "/api/v2/order/alipay/status"),
        # )
        # pay_url = f"{ALIPAY_GATEWAY}?{order_string}"
    else:
        order = (
            WechatBaseClient()
            .get_wechat_pay()
            .order.create(
                trade_type="APP",  # H5 支付模式
                body=plan.title,
                out_trade_no=new_order.order_id,
                total_fee=int(amount * 100),  # 金额单位为分
                notify_url=urljoin(settings.DOMAIN, "/api/v1/wechat/xml/wechat_notify"),
            )
        )
        prepay_id = order["prepay_id"]
        return common_success(
            data={"prepay_id": prepay_id, "payment_type": req.payment_type, "order_id": new_order.order_id}
        )
