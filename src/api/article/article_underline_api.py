import asyncio
import json
import random
from datetime import datetime
from typing import Literal
from uuid import UUID

from fastapi import APIRouter, Depends, Path, Query
from pydantic import BaseModel, Field

from src.logger.logUtil import get_logger
from src.model.article_detail_manager import (
    LectureArticleDetail,
    get_article_detail_by_biz_id
)
from src.model.article_manager import get_article_by_id
from src.model.article_record_manager import (
    LectureArticleRecord,

)
from src.model.lecture_ai import AITYPE, check_ai_task_has_created
from src.model.task_manager import LectureTask
from src.model.user_manager import UserPO
from src.orm.pgrepo import db
from src.services.article_service import can_access_article, can_access_article_and_task
from src.util.TokenUtil import get_current_user
from src.util.commonutil import common_error, common_success
from src.util.pandoc_util import convert_latex_in_markdown
from src.util import stringify, dateUtil

logger = get_logger(__name__)

router = APIRouter(prefix="/api/v1/article_underline", tags=["article"])


def can_access_article_detail(user_id: str, biz_id: str):
    """验证文章内容属于本人"""
    if not user_id:
        return False, False

    detail_item = (
        LectureArticleDetail.select()
        .where(
            (LectureArticleDetail.biz_id == biz_id)
            & (LectureArticleDetail.delete_flag == 0)
            & (LectureArticleDetail.user_id == "system")
        )
        .first()
    )
    if detail_item is None:
        return False, False
    task_item = (
        LectureTask.select()
        .where(
            (LectureTask.article_biz_id == detail_item.article_id)
            & (LectureTask.delete_flag == 0)
            & (LectureTask.user_id == user_id)
        )
        .first()
    )
    return task_item, detail_item


def can_access_article_record(user_id: str, record_id: str):
    """验证记录属于本人"""
    if not user_id:
        return False, None

    item = (
        LectureArticleRecord.select()
        .where(
            (LectureArticleRecord.record_id == record_id)
            & (LectureArticleRecord.delete_flag == 0)
            & (LectureArticleRecord.user_id == user_id)
        )
        .first()
    )
    if item:
        return True, item
    return False, None


class UnderlineSchema(BaseModel):
    biz_id: str = Field(..., description="content 列表的 biz_id")
    type: Literal['polish', 'original'] = Field(..., description="限定值 polish or original")
    change_text: str = Field(..., description="content 改变后的内容 应该带有 包含 record_id的标签内容")
    record_id: str = Field(..., description="record_id 与划线位置的标签内的 record_id 一致")
    record_context: str = Field(..., description="记录内容")
    language: str = Field('zh',
                          description="如果是 polish ，则记录位置和修改内容 与当前划线时选择的语言对应，不处理翻译对应内容")


async def context_json_create(req: UnderlineSchema, record_detail_list: list, max_index: int = 0):
    for index, context in enumerate(req.record_context):
        biz_id = str(UUID(int=random.getrandbits(128)))
        record_detail_list.append({
            "id": biz_id,
            "context": context,
            "sort": index + max_index + 1
        })
    return record_detail_list


async def context_json_create_one(record_context: str, record_detail_list: list, max_index: int = 0):
    biz_id = str(UUID(int=random.getrandbits(128)))
    record_detail_list.append({
        "id": biz_id,
        "context": record_context,
        "sort": max_index + 1,
        "create_time": dateUtil.get_current_time_in_beijing().strftime('%Y-%m-%d %H:%M:%S'),
        "update_time": dateUtil.get_current_time_in_beijing().strftime('%Y-%m-%d %H:%M:%S')
    })
    return record_detail_list


async def create_record_and_detail(req: UnderlineSchema, user: UserPO):
    old_record = LectureArticleRecord.select().where(
        (LectureArticleRecord.record_id == req.record_id) &
        (LectureArticleRecord.user_id == user.biz_id)
    ).first()
    if old_record:
        record_detail_list = old_record.context_json or []
        max_index = 0
        for item in record_detail_list:
            if item.get('sort') >= max_index:
                max_index = item.get('sort')
        await context_json_create_one(req.record_context, record_detail_list, max_index)
        old_record.context_json = record_detail_list
        old_record.update_time = dateUtil.get_current_time_in_beijing()
        old_record.delete_flag = 0
        old_record.save()
        return old_record.biz_id
    else:
        with (db.atomic() as txn):
            try:
                record_detail_list = list()
                await context_json_create_one(req.record_context, record_detail_list)

                record_biz_id = str(UUID(int=random.getrandbits(128)))
                LectureArticleRecord.insert(**{
                    "biz_id": record_biz_id,
                    "article_detail_id": req.biz_id,
                    "type_name": req.type,
                    "user_id": user.biz_id,
                    "record_id": req.record_id,
                    "context_json": record_detail_list
                }).execute()

                article_detail = LectureArticleDetail.get(LectureArticleDetail.biz_id == req.biz_id)
                translated_modified_text_json = article_detail.translated_modified_text_json or {}
                if req.type == 'polish':
                    translated_modified_text_json[req.language] = req.change_text
                    article_detail.translated_modified_text_json = translated_modified_text_json
                else:
                    article_detail.origin_text = req.change_text
                article_detail.save()
                return record_biz_id
            except Exception as ex:
                raise ex


def query_record_and_detail(article_detail_id):
    record_models = LectureArticleRecord.select(
        LectureArticleRecord.biz_id,
        LectureArticleRecord.record_id,
        LectureArticleRecord.context_json
    ).where(
        (LectureArticleRecord.article_detail_id == article_detail_id)
        & (LectureArticleRecord.delete_flag == 0)
    )
    record_list = list()
    for item in record_models:
        record_detail_list = item.context_json or []
        record_list.append({
            "record_id": item.record_id,
            "record_context": record_detail_list
        })
    return record_list


def query_response(article_detail_id, language: str = 'zh'):
    article_detail = get_article_detail_by_biz_id(article_detail_id)
    article = get_article_by_id(article_detail.article_id)
    article_data = article_detail.to_vo(article.out_language, article.language)
    data = query_record_and_detail(article_detail_id)
    article_data['record'] = data
    article_data['record_count'] = len(data)
    # article_data = {}
    return article_data


@router.post('/record', summary='创建划线记录')
async def create_record(
        req: UnderlineSchema,
        user: UserPO = Depends(get_current_user)):
    try:
        # detail_flag, detail_item = can_access_article_detail(user.biz_id, req.biz_id)
        # if not detail_flag:
        #     return common_error("无权限记录")

        await create_record_and_detail(req, user)

        article_data = query_response(req.biz_id, req.language)
        return common_success(article_data)

    except Exception:
        logger.exception(f"创建划线记录失败，biz_id={req.biz_id}")
        return common_error("创建划线记录失败")


class UpdateUnderlineSchema(BaseModel):
    record_id: str = Field(..., description="记录Id")
    context_id: str = Field(..., description="记录的每一条数据对应的 Id 后台生成的唯一值")
    record_context: str = Field(..., description="新的内容")
    language: str = Field(..., description="当前内容的语言类型, 影响返回结果对应的数据")


@router.put('/record', summary="更新一条记录")
async def update_record(
        req: UpdateUnderlineSchema,
        user: UserPO = Depends(get_current_user)
):
    try:
        detail_flag, record = can_access_article_record(user.biz_id, req.record_id)
        if not detail_flag:
            return common_error("无权限记录")
        context_json_list = record.context_json or []

        if not context_json_list:
            return common_error("没有相关记录")

        update_data = None
        for item in context_json_list:
            if item['id'] == req.context_id:
                item['context'] = req.record_context
                item['update_time'] = dateUtil.get_current_time_in_beijing().strftime('%Y-%m-%d %H:%M:%S')
                update_data = item
                break

        if update_data is None:
            return common_error("没有可以修改的记录")

        record.context_json = context_json_list
        record.update_time = dateUtil.get_current_time_in_beijing()
        record.save()

        article_data = query_response(record.article_detail_id, req.language)
        return common_success(article_data)

    except Exception:
        logger.exception(f"修改划线记录失败，record_id={req.record_id}\ncontext_id={req.context_id}")
        return common_error("修改划线记录失败")


@router.delete('/record/{record_id}/{context_id}', summary="删除一条记录")
async def update_record(
        record_id: str = Path(..., description="记录Id"),
        context_id: str = Path(..., description="记录的每一条数据对应的 Id 后台生成的唯一值"),
        language: str = Query('zh', description="当前内容的语言类型, 影响返回结果对应的数据"),
        user: UserPO = Depends(get_current_user)
):
    try:
        detail_flag, record = can_access_article_record(user.biz_id, record_id)
        if not detail_flag:
            return common_error("无权限记录")
        context_json_list = record.context_json or []

        if not context_json_list:
            return common_error("没有相关记录")

        new_list = list()
        remove_data = None
        for item in context_json_list:
            if item['id'] == context_id:
                remove_data = item
            else:
                new_list.append(item)
        if remove_data is None:
            return common_error("无可删除的记录")

        record.update_time = dateUtil.get_current_time_in_beijing()
        record.context_json = new_list
        if len(new_list) == 0:
            record.delete_flag = 1
        record.save()
        article_data = query_response(record.article_detail_id, language)
        return common_success(article_data)
    except Exception:
        logger.exception(f"删除划线记录失败，record_id={record_id}\ncontext_id={context_id}")
        return common_error("删除划线记录失败")


class HighLightSchema(BaseModel):
    biz_id: str = Field(..., description="content 列表的 biz_id")
    type: Literal['polish', 'original'] = Field(..., description="限定值 polish or original")
    change_text: str = Field(..., description="content 改变后的内容 应该带有 包含 record_id的标签内容")
    language: str = Field('zh',
                          description="如果是 polish ，则记录位置和修改内容 与当前划线时选择的语言对应，不处理翻译对应内容")


@router.put('/hi-light', summary="高亮当前选择的文本内容")
async def highlight_detail(
        req: HighLightSchema,
        user: UserPO = Depends(get_current_user)
):
    try:
        detail_flag, detail_item = can_access_article_detail(user.biz_id, req.biz_id)
        if not detail_flag:
            return common_error("无权限操作")

        translated_modified_text_json = detail_item.translated_modified_text_json or {}
        if req.type == 'polish':
            translated_modified_text_json[req.language] = req.change_text
            detail_item.translated_modified_text_json = translated_modified_text_json
        else:
            detail_item.origin_text = req.change_text
        detail_item.save()
        article_data = query_response(req.biz_id, req.language)
        return common_success(article_data)

    except Exception:
        logger.exception(f"高亮操作失败，biz_id={req.biz_id}")
        return common_error("高亮操作失败")


class CorrectPartSchema(BaseModel):
    biz_id: str = Field(..., description="content 列表的 biz_id")
    type: Literal['polish', 'original'] = Field(..., description="限定值 polish or original")
    change_text: str = Field(..., description="content 纠错后的内容")
    language: str = Field('zh',
                          description="如果是 polish ，则记录位置和修改内容 与当前划线时选择的语言对应，不处理翻译对应内容")


@router.put('/correct/part', summary="局部纠错")
async def correct_part(
        req: CorrectPartSchema,
        user: UserPO = Depends(get_current_user)
):
    try:
        detail_flag, detail_item = can_access_article_detail(user.biz_id, req.biz_id)
        if not detail_flag:
            return common_error("无权限操作")

        translated_modified_text_json = detail_item.translated_modified_text_json or {}
        if req.type == 'polish':
            translated_modified_text_json[req.language] = req.change_text
            detail_item.translated_modified_text_json = translated_modified_text_json
        else:
            detail_item.origin_text = req.change_text
        detail_item.save()
        article_data = query_response(req.biz_id, req.language)
        return common_success(article_data)

    except Exception:
        logger.exception(f"局部纠错失败，biz_id={req.biz_id}")
        return common_error("局部纠错失败")


class CorrectAllSchema(BaseModel):
    biz_id: str = Field(..., description="文章任务的biz_id")
    old_context: str = Field(..., description="旧值")
    language: str = Field('zh',
                          description="如果是 polish ，则记录位置和修改内容 与当前划线时选择的语言对应，不处理翻译对应内容")
    contain: bool = Field(default=True, description="是否包含大纲 true 包含、false 不包含")


class UpdateCorrectAllSchema(CorrectAllSchema):
    new_context: str = Field(..., description="新值")


async def context_count(content, value):
    try:
        count = content.count(value)
        return count
    except Exception:
        return 0


# 如果数据更改需要释放，待熟悉相关引用数据位置，在增加缓存
# @ttl_cache.cache_on_arguments()
def query_article_detail_all(article_id):
    query = LectureArticleDetail.select().where(
        (LectureArticleDetail.user_id == "system")
        & (LectureArticleDetail.delete_flag == 0)
        & (LectureArticleDetail.article_id == article_id)
    )
    details = query.order_by(LectureArticleDetail.start_time, LectureArticleDetail.index)
    detail_data_list = list()
    for article_detail in details:
        detail_data_list.append({
            'biz_id': article_detail.biz_id,
            'index': article_detail.index,
            'start_time': article_detail.start_time,
            'end_time': article_detail.end_time,
            'origin_text': article_detail.origin_text,
            'translated_modified_text_json': article_detail.translated_modified_text_json or {}
        })
    return detail_data_list


async def query_article_detail_all_response(article_id):
    query = LectureArticleDetail.select().where(
        (LectureArticleDetail.user_id == "system")
        & (LectureArticleDetail.delete_flag == 0)
        & (LectureArticleDetail.article_id == article_id)
    )
    details = query.order_by(LectureArticleDetail.start_time, LectureArticleDetail.index)
    return details


@router.post('/correct/all/count', summary="全文纠错出现次数")
async def correct_all(
        req: CorrectAllSchema,
        user: UserPO = Depends(get_current_user)
):
    try:
        access_flag, article_id = can_access_article(user.biz_id, req.biz_id)
        if not access_flag:
            return common_error("无权限操作")
        article = get_article_by_id(article_id)
        if not article:
            return common_error("文章不存在")

        detail_data_list = query_article_detail_all(article_id)

        count_task, modified_task = list(), list()
        for article_detail in detail_data_list:
            # 原文的内容
            count_task.append(context_count(article_detail.get('origin_text', ''), req.old_context))

            # ai润色对应原文语种
            translated_modified_text_json = article_detail.get('translated_modified_text_json', {})
            modified_task.append(
                context_count(translated_modified_text_json.get(req.language, ''), req.old_context))
        context_count_results = await asyncio.gather(*count_task, return_exceptions=True)

        modified_count_results = await asyncio.gather(*modified_task, return_exceptions=True)
        outline_count, summary_count = 0, 0
        if req.contain:
            # 大纲
            translated_outline_json = article.translated_outline_json or {}
            outline_count += translated_outline_json.get(req.language, '').count(req.old_context)
            # ai总结
            translated_total_summary_json = article.translated_total_summary_json or {}
            for key, value in translated_total_summary_json.get(req.language, {}).items():
                str_value = json.dumps(value, indent=4, ensure_ascii=False)
                summary_count += str_value.count(req.old_context)
        response = {
            'origin_count': sum(context_count_results),
            'modified_count': sum(modified_count_results),
            'outline_count': outline_count,
            'summary_count': summary_count,
            'total': sum(context_count_results) + sum(modified_count_results) + outline_count + summary_count
        }
        return common_success(response)
    except Exception:
        logger.exception(f"获取出现次数，biz_id={req.biz_id}")
        return common_error("获取出现次数失败")


async def update_correct_all(
        article_detail: dict,
        req: UpdateCorrectAllSchema
):
    """沉浸式阅读修改内容 包括原文和润色"""
    detail_biz_id = article_detail.get('biz_id')
    origin_text = article_detail.get('origin_text', '')
    origin_text = origin_text.replace(req.old_context, req.new_context)

    translated_modified_text_json = article_detail.get('translated_modified_text_json', {})
    old_context = translated_modified_text_json.get(req.language, '')
    translated_modified_text_json[req.language] = old_context.replace(req.old_context, req.new_context)

    LectureArticleDetail.update(
        origin_text=origin_text,
        translated_modified_text_json=translated_modified_text_json,
        update_time=dateUtil.get_current_time_in_beijing()
    ).where(LectureArticleDetail.biz_id == detail_biz_id).execute()


async def article_info(article_id, article, task):
    author = task.author
    img_url = task.img_url
    only_audio = task.ctrl_params.get("video_style") == "audio" or task.ctrl_params.get("image_mode") == "no"
    has_ai_polishing_finished = False
    ai_task = check_ai_task_has_created(article_biz_id=article_id, task_type=AITYPE.AI_POLISHING.value)
    if ai_task and ai_task.status in ["finish", "finished"]:
        has_ai_polishing_finished = True
    outline = convert_latex_in_markdown((article.translated_outline_json or {}).get(article.out_language) or "")
    enable_speaker_recognition = task.ctrl_params.get("enable_speaker_recognition", False)
    enable_speaker_recognition = stringify.str_to_bool(enable_speaker_recognition)

    data = {
        "biz_id": article.biz_id,
        "name": task.name,
        "author": author,
        "video_url": article.video_url,
        "img_url": img_url,
        "create_time": article.create_time,
        "language": article.language,
        "out_language": article.out_language,
        "has_speaker": enable_speaker_recognition,
        "out_language_outline": outline,
        "out_language_total_summary_json": (article.translated_total_summary_json or {}).get(article.out_language)
                                           or {},
        "podcast_transcript_json": article.podcast_transcript_json,
        "audio_url": article.audio_url,
        "has_ai_polishing_finished": has_ai_polishing_finished,
        "only_audio": only_audio,
    }
    return data


@router.put('/correct/all', summary="全文纠错")
async def correct_all(
        req: UpdateCorrectAllSchema,
        user: UserPO = Depends(get_current_user)
):
    try:
        not_update_str = ['\n', '-']
        for item in not_update_str:
            if item in req.new_context:
                return common_error("新值不能包含‘\n’，‘-’等字符")
        access_flag, article_id, task = can_access_article_and_task(user.biz_id, req.biz_id)
        if not access_flag:
            return common_error("无权限操作")
        article = get_article_by_id(article_id)
        if not article:
            return common_error("文章不存在")

        detail_data_list = query_article_detail_all(article_id)
        update_task = list()
        context_update_count, outline_update_count = 0, 0
        for article_detail in detail_data_list:
            update_count = 0
            update_count += article_detail.get('origin_text', '').count(req.old_context)
            translated_modified_text_json = article_detail.get('translated_modified_text_json', {})
            update_count += translated_modified_text_json.get(req.language, '').count(req.old_context)
            if update_count > 0:
                context_update_count += update_count
                update_task.append(update_correct_all(article_detail, req))

        results = await asyncio.gather(*update_task)
        if req.contain:
            # 大纲
            translated_outline_json = article.translated_outline_json or {}
            old_context = translated_outline_json.get(req.language, '')
            outline_update_count = old_context.count(req.old_context)

            # AI总结
            summary_count = 0
            translated_total_summary_json = article.translated_total_summary_json or {}
            for key, value in translated_total_summary_json.get(req.language, {}).items():
                str_value = json.dumps(value, indent=4, ensure_ascii=False)
                summary_count += str_value.count(req.old_context)

            if outline_update_count > 0 or summary_count > 0:
                translated_outline_json[req.language] = old_context.replace(req.old_context, req.new_context)
                new_translated_total_summary_json_by_language = dict()
                for key, value in translated_total_summary_json.get(req.language, {}).items():
                    str_value = json.dumps(value, indent=4, ensure_ascii=False)
                    new_value = str_value.replace(req.old_context, req.new_context)
                    new_translated_total_summary_json_by_language[key] = json.loads(new_value)
                translated_total_summary_json[req.language] = new_translated_total_summary_json_by_language

                article.translated_outline_json = translated_outline_json
                article.translated_total_summary_json = translated_total_summary_json
                article.update_time = dateUtil.get_current_time_in_beijing()
                article.save()

        article_response = await article_info(article_id, article, task)

        details_response = {"content": [], "total": 0}
        details_models = await query_article_detail_all_response(article_id)
        details_response.update(
            {
                # "content": [article_detail.__dict__ for article_detail in details],
                "content": [article_detail.to_vo(article.out_language, article.language) for article_detail in
                            details_models],
                "total": len(details_models),
            }
        )
        response = {
            'article_data': article_response,
            'details_data': details_response
        }
        return common_success(response)
    except Exception:
        logger.exception(f"全局纠错失败，biz_id={req.biz_id}")
        return common_error("全局纠错失败")
