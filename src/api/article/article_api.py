import io
import urllib.parse
from datetime import datetime
from traceback import format_exc

from fastapi import APIRouter, Depends, Request
from peewee import DoesNotExist
from starlette.responses import StreamingResponse

from src.logger.logUtil import get_logger
from src.model.article_detail_manager import LectureArticleDetail
from src.model.article_manager import LectureArticle, get_article_by_id
from src.model.lecture_ai import AITYPE, check_ai_task_has_created
from src.model.user_manager import UserPO
from src.services.article_service import can_access_article, can_access_article_and_task
from src.util.article_utils import get_article_for_deep_read, get_article_head, only_return_body
from src.util.commonutil import common_error, common_success
from src.util.html_utils.styles_renderer import render_article
from src.util.pandoc_util import convert_latex_in_markdown
from src.util.TokenUtil import download_rate_limit, get_current_user

logger = get_logger(__name__)

router = APIRouter(prefix="/api/v1/article", tags=["article"])


@router.get("/get/{biz_id}")
async def get(request: Request, biz_id: str, user: UserPO = Depends(get_current_user)):
    try:
        access_flag, article_id = can_access_article(user.biz_id, biz_id)
        if not access_flag:
            return common_error("无权限访问")
        data = {}
        try:
            article = (
                LectureArticle.select(LectureArticle.content)
                .where(LectureArticle.biz_id == article_id, LectureArticle.delete_flag == 0)
                .get()
            )
            data["content"] = article.content
        except DoesNotExist:
            pass
        return common_success(data=data)
    except DoesNotExist:
        logger.exception(f"获取文章失败!,bizId={biz_id}")
        return common_error("获取失败")


async def query_article_details(article_id, end_time_str, page_no, page_size, start_time_str):
    query = LectureArticleDetail.select().where(
        (LectureArticleDetail.user_id == "system")
        & (LectureArticleDetail.delete_flag == 0)
        & (LectureArticleDetail.article_id == article_id)
    )
    if start_time_str:
        start_time = datetime.strptime(start_time_str, "%H:%M:%S").time()
        query = query.where(LectureArticleDetail.start_time >= start_time)
    if end_time_str:
        end_time = datetime.strptime(end_time_str, "%H:%M:%S").time()
        query = query.where(LectureArticleDetail.end_time <= end_time)
    total = query.count()
    details = query.order_by(LectureArticleDetail.start_time, LectureArticleDetail.index).paginate(page_no, page_size)
    return details, total


@router.get("/deepRead/{biz_id}")
async def deep_read(
        biz_id: str,
        page_size: int = 5,
        page_no: int = 1,
        start_time_str=None,
        end_time_str=None,
        user: UserPO = Depends(get_current_user),
):
    # 查询数据库
    try:
        access_flag, article_id = can_access_article(user.biz_id, biz_id)
        if not access_flag:
            return common_error("无权限访问")
        article = get_article_by_id(article_id)
        if not article:
            return common_error("文章不存在")

        details, total = await query_article_details(article_id, end_time_str, page_no, page_size, start_time_str)

        if page_no == 1:
            article = get_article_by_id(article_id)
            text = get_article_head(article, summary=False)
            text += "\n\n" + "## 内容回顾\n\n"
        else:
            text = ""
        text += get_article_for_deep_read(details, article.out_language or "zh")
        # Run the render_article calls concurrently
        html_text_details_note = render_article(text)
        content = [only_return_body(html_text_details_note)]

        return common_success({"content": content, "total": total})
    except Exception:
        logger.exception("获取文章详细失败，biz_id=%s", biz_id)
        return common_error("获取详细失败")


def str_to_bool(value):
    if isinstance(value, bool):
        return value
    if isinstance(value, str):
        return value.lower() in ["true", "1", "t", "y", "yes"]
    return False


@router.get("/meta/{biz_id}")
async def article_meta(biz_id: str, user: UserPO = Depends(get_current_user)):
    """
    input: biz_id: task id
    output: task_id 对应的 article 的元信息
    """
    try:
        access_flag, article_id, task = can_access_article_and_task(user.biz_id, biz_id)
        if not access_flag or not task:
            return common_error("无权限访问")
        article = get_article_by_id(article_id)
        if not article:
            return common_error("文章不存在")
        author = task.author
        img_url = task.img_url
        only_audio = task.ctrl_params.get("video_style") == "audio" or task.ctrl_params.get("image_mode") == "no"
        has_ai_polishing_finished = False
        ai_task = check_ai_task_has_created(article_biz_id=article_id, task_type=AITYPE.AI_POLISHING.value)
        if ai_task and ai_task.status in ["finish", "finished"]:
            has_ai_polishing_finished = True
        outline = convert_latex_in_markdown((article.translated_outline_json or {}).get(article.out_language) or "")
        enable_speaker_recognition = task.ctrl_params.get("enable_speaker_recognition", False)
        enable_speaker_recognition = str_to_bool(enable_speaker_recognition)

        data = {
            "task_id": task.id,
            "task_biz_id": task.biz_id,
            "biz_id": article.biz_id,
            "name": task.name,
            "author": author,
            "video_url": article.video_url,
            "img_url": img_url,
            "create_time": article.create_time,
            "language": article.language,
            "out_language": article.out_language,
            "has_speaker": enable_speaker_recognition,
            "out_language_outline": outline,
            "out_language_total_summary_json": (article.translated_total_summary_json or {}).get(article.out_language)
                                               or {},
            "podcast_transcript_json": article.podcast_transcript_json,
            "audio_url": article.audio_url,
            "has_ai_polishing_finished": has_ai_polishing_finished,
            "only_audio": only_audio,
        }
        return common_success(data)
    except Exception:
        logger.exception("获取文章数据失败，biz_id=%s, %s", biz_id, format_exc())
        return common_error("获取文章数据失败")


@router.get("/section/{biz_id}", include_in_schema=False, summary="旧版本文章列表")
async def section_read(
        biz_id: str,
        page_size: int = 5,
        page_no: int = 1,
        start_time_str=None,
        end_time_str=None,
        user: UserPO = Depends(get_current_user),
):
    # 查询数据库
    try:
        access_flag, article_id = can_access_article(user.biz_id, biz_id)
        if not access_flag:
            return common_error("无权限访问")
        article = get_article_by_id(article_id)
        if not article:
            return common_error("文章不存在")
        data = {"content": [], "total": 0}
        details, total = await query_article_details(article_id, end_time_str, page_no, page_size, start_time_str)
        data.update(
            {
                # "content": [article_detail.__dict__ for article_detail in details],
                "content": [article_detail.to_vo(article.out_language, article.language) for article_detail in details],
                "total": total,
            }
        )
        return common_success(data)
    except Exception:
        logger.exception("获取文章详细失败，biz_id=%s", biz_id)
        return common_error("获取详细失败")


@router.get("/download/{biz_id}/")
async def download(
        biz_id: str,
        types: str,
        out_language=True,
        user: UserPO = Depends(get_current_user),
        limited=Depends(download_rate_limit),
):
    # FastAPI 会自动对没有 / 的路径追加 /，或者处理路径规范化时返回 307。所以该 router 访问的路径未包含结尾的 /
    # 为了避免用户重复点击该按钮，设置了 60 秒内下载不超过 2 次
    if limited:
        return common_success("下载请求次数超过限制，请1分钟后重试")
    try:
        access_flag, article_id = can_access_article(user.biz_id, biz_id)
        # 获取文章
        article = get_article_by_id(article_id)
        if not article:
            return common_error("文章不存在")
        # 获取文章列表
        query = LectureArticleDetail.select().where(
            (LectureArticleDetail.delete_flag == 0) & (LectureArticleDetail.article_id == article_id)
        )
        details = query.order_by(LectureArticleDetail.start_time)
        if not details:
            return common_error("文章内容不存在")

        # 处理单个文件类型
        file_type = types  # 只接受一个文件类型
        response = None
        if file_type == "word":
            if article.oss_word_url:
                return common_success({"url": article.oss_word_url})
            else:
                return common_success("word 文件正在生成中...")
        elif file_type == "pdf":
            if article.oss_pdf_url:
                response = common_success(article.oss_pdf_url)
            else:
                return common_success("pdf 文件正在生成中...")
        else:
            text_summary = get_article_head(article)
            text_deep_read = text_summary + get_article_for_deep_read(details, article.out_language)

            if file_type == "md":
                content = io.BytesIO(text_deep_read.encode("utf-8"))
                response = StreamingResponse(
                    content=content,
                    media_type="text/markdown",
                    headers={"Content-Disposition": f"attachment; filename={urllib.parse.quote(article.name)}.md"},
                )
            elif file_type == "html":
                html_text_deep_read = render_article(text_deep_read)
                content = io.BytesIO(only_return_body(html_text_deep_read).encode("utf-8"))
                response = StreamingResponse(
                    content=content,
                    media_type="text/html",
                    headers={"Content-Disposition": f"attachment; filename={urllib.parse.quote(article.name)}.html"},
                )

        # elif file_type == "pdf":
        #     try:
        #         html_text = await render_article(text_deep_read)
        #
        #         # 配置 pdfkit
        #         options = {
        #             "encoding": "UTF-8",
        #             "page-size": "A4",
        #             "margin-top": "0.75in",
        #             "margin-right": "0.75in",
        #             "margin-bottom": "0.75in",
        #             "margin-left": "0.75in",
        #             "enable-local-file-access": True,
        #         }
        #
        #         # 直接使用 pdfkit 转换
        #         content = pdfkit.from_string(html_text, False, options=options)  # 不保存到文件
        #
        #         filename = f"{article.name}.pdf"
        #         return StreamingResponse(
        #             content=io.BytesIO(content),
        #             media_type="application/pdf",
        #             headers={"Content-Disposition": f"attachment; filename={urllib.parse.quote(filename)}"},
        #         )
        #     except Exception as e:
        #         logger.exception("Convert to PDF failed")
        #         raise HTTPException(status_code=500, detail="PDF转换失败")
        # elif file_type == "word":
        #     try:
        #         html_text = await render_article(text_deep_read)
        #         converter = DocumentConverter()
        #
        #         # 简单转换
        #         content = converter.convert(content=html_text, from_format="html", to_format="docx")
        #
        #         filename = f"{article.name}.docx"
        #         return StreamingResponse(
        #             content=io.BytesIO(content),
        #             media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        #             headers={"Content-Disposition": f"attachment; filename={urllib.parse.quote(filename)}"},
        #         )
        #     except Exception as e:
        #         logger.exception("Convert to docx failed")
        #         raise HTTPException(status_code=500, detail="文档转换失败")

        # 如果没有响应，返回错误
        if not response:
            return common_error("不支持的文件类型")

        # 返回响应
        return response

    except Exception:
        logger.exception(f"下载失败!,bizId={biz_id}")
        return common_error("下载失败")
