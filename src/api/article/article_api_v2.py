import uuid
from datetime import datetime
from pathlib import Path
from urllib.parse import quote

from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
from sqlalchemy.orm import joinedload

from src.core import deps
from src.corn.downloads import article_async_download
from src.database import models
from src.logger.logUtil import get_logger
from src.model.article_manager import get_article_by_id
from src.model.download_manager import LectureAsyncDownload
from src.model.user_manager import UserPO
from src.services.article_service import can_access_article
from src.util.TokenUtil import download_rate_limit, get_current_user
from src.util.commonutil import ErrorCodeEnum, common_enum_error, common_error, common_success
from src.util.download_util import file_iterator

logger = get_logger(__name__)

router = APIRouter(prefix="/api/v2/article", tags=["article"])


class DownloadArticleRequest(BaseModel):
    """
        {
            "AIFeatures": [
                "AISummary",
                "textOutline"
            ],
            "readingMode": "polishedVersion",  # or originalText
            "displayOptions": [
                "withImages",
                "bilingual",
                "timestamp",
                "speaker"
            ],
            "language": "zh",
            "exportFormat": ".docx"
    }
    """

    AIFeatures: list[str] = Field([], title="AI 功能", description="AI 功能")
    readingMode: str = Field("", title="阅读模式", description="阅读模式 原文/润色")
    language: str = Field("zh", title="语言", description="语言")
    displayOptions: list[str] = Field(
        [], title="显示选项", description="显示选项 是否显示图片/是否显示双语/是否显示时间戳/是否显示发言者"
    )
    exportFormat: str = Field(..., title="导出格式", description="导出格式 .docx/.pdf/.html/.md")


def get_downloads_by_articles_params(req: DownloadArticleRequest, biz_id=None):
    query = LectureAsyncDownload.select().where(
        LectureAsyncDownload.relate_biz_id == biz_id,
        LectureAsyncDownload.reading_mode == req.readingMode,
        LectureAsyncDownload.export_format == req.exportFormat,
        LectureAsyncDownload.download_type == "article",
    )

    if req.AIFeatures:
        ai_features_str = ",".join(req.AIFeatures)
        query = query.where(LectureAsyncDownload.ai_features == ai_features_str)
    if req.displayOptions:
        display_options_str = ",".join(req.displayOptions)
        query = query.where(LectureAsyncDownload.display_options == display_options_str)

    one = query.get_or_none()
    if not one or one.status == 4 or not one.download_url:
        return None

    file_path = Path(str(one.download_url))
    if not file_path.exists():
        return None
    return one


def create_downloads(biz_id, req):
    downloads = LectureAsyncDownload.create(
        biz_id="down_" + str(uuid.uuid4()),
        ai_features=",".join(req.AIFeatures) if req.AIFeatures else None,
        reading_mode=req.readingMode,
        display_options=",".join(req.displayOptions) if req.displayOptions else None,
        export_format=req.exportFormat,
        language=req.language,
        relate_biz_id=biz_id,
        download_type="article",
        status=1,
    )

    return downloads.biz_id


@router.post("/download/{biz_id}/create", summary="导出-创建导出文件")
async def download(
        biz_id: str,
        req: DownloadArticleRequest,
        user: UserPO = Depends(get_current_user),
        limited=Depends(download_rate_limit),
):
    if limited:
        return common_error("下载请求次数超过限制，请1分钟后重试")
    try:
        access_flag, article_id = can_access_article(str(user.biz_id), biz_id)
        if not access_flag:
            return common_error("无权访问该文章")

        # async_downloader = get_downloads_by_articles_params(req, article_id)
        if async_downloader := get_downloads_by_articles_params(req, article_id):
            return common_success(async_downloader.biz_id)
        # 生成下载任务
        export_id = create_downloads(article_id, req)
        article_async_download.delay(export_id)
        return common_success(export_id)
    except Exception:
        logger.exception(f"下载失败!,bizId={biz_id}")
        return common_error("下载失败")


@router.get("/download/{biz_id}/status", summary="导出-查询创建状态")
async def get_download_status(
        biz_id: str,
        export_id: str,
        user: UserPO = Depends(get_current_user),
):
    try:
        # 检查是否有权限访问该文章
        access_flag, _ = can_access_article(str(user.biz_id), biz_id)
        if not access_flag:
            return common_error("无权访问该文章")

        downloads: LectureAsyncDownload = (
            LectureAsyncDownload.select()
            .where(LectureAsyncDownload.biz_id == export_id, LectureAsyncDownload.delete_flag == 0)
            .get_or_none()
        )

        # 返回文件状态信息
        if not downloads:
            return common_error(f"{export_id} 不存在")
        if not downloads.download_url:
            if downloads.status == 4:
                return common_error("文件生成失败，请联系客服")
            return common_enum_error(ErrorCodeEnum.GENERATING_DOWNLOAD_FILE)

        return common_success(export_id)

    except Exception:
        logger.exception(f"get_download_status!,bizId={biz_id}")
        return common_error("文件生成异常，请联系客服")


@router.get("/download/{biz_id}/get", summary="导出-获取文件内容")
async def get_download_url(
        biz_id: str,
        export_id: str,
        user: UserPO = Depends(get_current_user),
        limited=Depends(download_rate_limit),
):
    try:
        if limited:
            raise HTTPException(status_code=429, detail="下载请求次数超过限制，请1分钟后重试")
        access_flag, _ = can_access_article(str(user.biz_id), biz_id)
        if not access_flag:
            raise HTTPException(status_code=403, detail=f"biz_id: {biz_id} 无权访问该文章")

        downloads: LectureAsyncDownload = (
            LectureAsyncDownload.select()
            .where(LectureAsyncDownload.biz_id == export_id, LectureAsyncDownload.delete_flag == 0)
            .get_or_none()
        )
        if not downloads:
            raise HTTPException(status_code=404, detail=f"export_id: {export_id} 不存在")

        if not downloads.download_url:
            raise HTTPException(status_code=400, detail=f"export_id: {export_id} 文件尚未生成或生成失败")

        file_path = Path(str(downloads.download_url))
        if not file_path.exists():
            raise HTTPException(status_code=400, detail=f"export_id: {export_id} 下载路径生成失败，请联系客服")

        file_size = file_path.stat().st_size
        filename = file_path.name
        ascii_filename = filename.encode("ascii", "ignore").decode("ascii")
        utf8_filename = quote(filename)

        return StreamingResponse(
            file_iterator(file_path),
            media_type="application/octet-stream",
            headers={
                "Content-Length": str(file_size),
                "Content-Disposition": f"attachment; filename=\"{ascii_filename}\"; filename*=UTF-8''{utf8_filename}",
                "Access-Control-Expose-Headers": "Content-Disposition, Content-Type",
            },
        )
    except HTTPException:
        raise
    except Exception:
        logger.exception('下载文件出错')
        raise HTTPException(status_code=500, detail=f"export_id: {export_id} 下载时服务异常，请联系客服")


@router.get("/section/{biz_id}", summary="文章内容列表")
async def section_read(
        biz_id: str,
        page_size: int = 5,
        page_no: int = 1,
        start_time_str=None,
        end_time_str=None,
        user: UserPO = Depends(get_current_user),
        db: deps.DBSessionDep = deps.DBSessionDep
):
    # 查询数据库
    try:
        access_flag, article_id = can_access_article(user.biz_id, biz_id)
        if not access_flag:
            return common_error("无权限访问")
        article = get_article_by_id(article_id)
        if not article:
            return common_error("文章不存在")

        filters = [
            models.LectureArticleDetailModel.user_id == "system",
            models.LectureArticleDetailModel.delete_flag == 0,
            models.LectureArticleDetailModel.article_id == article_id,
        ]

        if start_time_str:
            start_time = datetime.strptime(start_time_str, "%H:%M:%S").time()
            filters.append(models.LectureArticleDetailModel.start_time >= start_time)
        if end_time_str:
            end_time = datetime.strptime(end_time_str, "%H:%M:%S").time()
            filters.append(models.LectureArticleDetailModel.end_time <= end_time)
        model_data = await models.LectureArticleDetailModel.search_model_paginate(
            db,
            page_size=page_size,
            current_page=page_no,
            filters=filters,
            joins=[
                joinedload(models.LectureArticleDetailModel.records)
            ],
            sorts=[models.LectureArticleDetailModel.start_time, models.LectureArticleDetailModel.index]
        )
        data = {"content": [], "total": model_data.get("total")}
        for index, article_detail in enumerate(model_data.get('items')):
            detail_data = article_detail.to_vo(article.out_language, article.language)
            record_list = list()
            for item in article_detail.records:
                record_detail_list = item.context_json or []
                record_list.append({
                    "record_id": item.record_id,
                    "record_context": record_detail_list
                })
            detail_data['record'] = record_list
            detail_data['record_count'] = len(record_list)
            data['content'].append(detail_data)
        return common_success(data)
    except Exception:
        logger.exception("获取文章详细失败，biz_id=%s", biz_id)
        return common_error("获取详细失败")
