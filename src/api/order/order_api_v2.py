import time
from datetime import <PERSON><PERSON><PERSON>
from typing import Optional
from urllib.parse import urljoin

from fastapi import APIRouter, Depends
from pydantic import BaseModel, HttpUrl
from starlette.requests import Request
from wechatpy import WeChatPayException
from wechatpy.utils import random_string

from config import settings
from src.api.pay.utils import alipay
from src.database import enums, constants
from src.logger.logUtil import get_logger
from src.model.lecture_plan_manager import get_lecture_plan_by_biz_id, get_plan_list
from src.model.order_manager import LectureOrders, OrderStatus, PaymentType, generate_order_str, order_call_back
from src.model.user_manager import check_is_admin
from src.orm.redis_cache import common_cache
from src.services.wechat_service import (
    direct_access_token_by_code
)
from src.util.TokenUtil import get_current_user
from src.util.cacheUtil import open_api_cache
from src.util.celery_util import sem_to_baidu
from src.util.commonutil import CommonResponse, Error<PERSON>odeEnum, common_enum_error, common_error, common_success
from src.util.dateUtil import get_beijing_time
from src.util.wechat_oauth import WechatBaseClient

logger = get_logger(__name__)

router = APIRouter(prefix="/api/v2/order", tags=["order"])


@router.get("/plan-list", summary="购买列表", response_model=CommonResponse)
async def plan_list(_type: Optional[int] = None):
    plans = get_plan_list(_type)
    data = [plan.__data__ for plan in plans if plan.viewable == 1]
    return common_success(data=data)


@router.get("/record", response_model=CommonResponse)
async def record(page_size: int = 10, page_no: int = 1, user=Depends(get_current_user)):
    user_id = user.biz_id

    order_count = (
        LectureOrders.select()
        .where(LectureOrders.user_id == user_id, LectureOrders.status == 1 or LectureOrders.status == 3)
        .count()
    )

    orders = (
        LectureOrders.select()
        .where(LectureOrders.user_id == user_id, LectureOrders.status == 1 or LectureOrders.status == 3)
        .order_by(-LectureOrders.id)
        .paginate(page_no, page_size)
    )
    # 返回出去的时候过滤掉payment_result
    for order in orders:
        order.payment_result = None
    return common_success({"orders": [task.__data__ for task in orders], "total": order_count})


class OrderRequest(BaseModel):
    product_id: str
    create_timestamp: int


@router.get("/pay-result", response_model=CommonResponse, summary="查询支付结果")
async def pay_result(order_id: str, user=Depends(get_current_user)):
    """
    查询支付结果
    """
    order = LectureOrders.get_or_none(LectureOrders.order_id == order_id, LectureOrders.user_id == user.biz_id)

    if not order:
        return common_error("订单不存在")
    if order.payment_type == PaymentType.WE_CHAT:
        if order.status == OrderStatus.SUCCESS:
            return common_success(message="已支付成功")
        data = WechatBaseClient().get_wechat_pay().order.query(out_trade_no=order.order_id)
        logger.info("查询微信支付订单", data)
        if data["return_code"] == "SUCCESS" and data["result_code"] == "SUCCESS":
            trans_id = data.get("transaction_id")
            if trans_id:
                counts = (
                    LectureOrders.update(status=OrderStatus.SUCCESS, transaction_id=trans_id, payment_result=data)
                    .where((LectureOrders.order_id == order_id) & (LectureOrders.status != OrderStatus.SUCCESS))
                    .execute()
                )
                if counts > 0 and data["trade_state"] == "SUCCESS":
                    await order_call_back(order)
                    logger.info("Order updated successfully: %s", order.__data__)
                else:
                    logger.error("Order updated failed: %s", order.__data__)
                    return common_enum_error(ErrorCodeEnum.WECHAT_PAY_ERROR)
                sem_to_baidu(order_id=order.order_id)
                return common_success(order.status)
    elif order.payment_type == PaymentType.ALIPAY:
        if order.status == OrderStatus.SUCCESS:
            return common_success(message="已支付成功")
        data = alipay.api_alipay_trade_query(out_trade_no=order.order_id, trade_no=order.transaction_id)
        if data["msg"] == "Success" and data["trade_status"] == "TRADE_SUCCESS":
            trade_no = data.get("trade_no")
            if trade_no:
                counts = (
                    LectureOrders.update(status=OrderStatus.SUCCESS, transaction_id=trade_no, payment_result=data)
                    .where((LectureOrders.order_id == order_id) & (LectureOrders.status != OrderStatus.SUCCESS))
                    .execute()
                )
                if counts > 0:
                    await order_call_back(order)
                    logger.info("Order updated successfully: %s", order.__data__)
                else:
                    logger.error("Order updated failed: %s", order.__data__)
                    return common_enum_error(ErrorCodeEnum.ALIPAY_ERROR)
                sem_to_baidu(order_id=order.order_id)
                return common_success(order.status)
    return common_enum_error(ErrorCodeEnum.WECHAT_PAY_LATTER)


@router.get("/pay-result-by-userid", response_model=CommonResponse, summary="查询支付结果")
async def pay_result_by_userid(order_id: str, user_id: str, user=Depends(get_current_user)):
    """
    order_id: 被查询用户的 order_id
    user_id: 被查询用户的 user_id
    user: admin 管理员，需要管理员登录的 Authorization
    """
    if not check_is_admin(user.biz_id):
        return common_enum_error(ErrorCodeEnum.USER_UNAUTHORIZED)

    order = LectureOrders.get_or_none(LectureOrders.order_id == order_id, LectureOrders.user_id == user_id)

    if not order:
        return common_error("订单不存在")
    if order.payment_type == PaymentType.WE_CHAT:
        if order.status == OrderStatus.SUCCESS:
            return common_success(message="已支付成功")
        data = WechatBaseClient().get_wechat_pay().order.query(out_trade_no=order.order_id)
        logger.info("查询微信支付订单", data)
        if data["return_code"] == "SUCCESS" and data["result_code"] == "SUCCESS":
            trans_id = data.get("transaction_id")
            if trans_id:
                counts = (
                    LectureOrders.update(status=OrderStatus.SUCCESS, transaction_id=trans_id, payment_result=data)
                    .where((LectureOrders.order_id == order_id) & (LectureOrders.status != OrderStatus.SUCCESS))
                    .execute()
                )
                if counts > 0 and data["trade_state"] == "SUCCESS":
                    await order_call_back(order)
                    logger.info("Order updated successfully: %s", order.__data__)
                else:
                    logger.error("Order updated failed: %s", order.__data__)
                    return common_enum_error(ErrorCodeEnum.WECHAT_PAY_ERROR)
                sem_to_baidu(order_id=order.order_id)
                return common_success(order.status)
    elif order.payment_type == PaymentType.ALIPAY:
        if order.status == OrderStatus.SUCCESS:
            return common_success(message="已支付成功")
        data = alipay.api_alipay_trade_query(out_trade_no=order.order_id, trade_no=order.transaction_id)
        logger.info("查询支付宝支付订单", data)
        if data["msg"] == "Success" and data["trade_status"] == "TRADE_SUCCESS":
            trade_no = data.get("trade_no")
            if trade_no:
                counts = (
                    LectureOrders.update(status=OrderStatus.SUCCESS, transaction_id=trade_no, payment_result=data)
                    .where((LectureOrders.order_id == order_id) & (LectureOrders.status != OrderStatus.SUCCESS))
                    .execute()
                )
                if counts > 0:
                    await order_call_back(order)
                    logger.info("Order updated successfully: %s", order.__data__)
                else:
                    logger.error("Order updated failed: %s", order.__data__)
                    return common_enum_error(ErrorCodeEnum.ALIPAY_ERROR)
                sem_to_baidu(order_id=order.order_id)
                return common_success(order.status)
    return common_enum_error(ErrorCodeEnum.WECHAT_PAY_LATTER)


@common_cache.cache_on_arguments(namespace="order:lock:", expiration_time=5)
def get_last_order_lock(timestamp: int, _user_id: str):
    """
    Get All plan list
    :return: plan list
    """
    return timestamp


@router.post("/create", response_model=CommonResponse, summary="创建订单")
async def create_order(request: Request, req: OrderRequest, user=Depends(get_current_user)):
    user_id = user.biz_id
    plan = get_lecture_plan_by_biz_id(req.product_id)
    if not plan:
        return common_enum_error(ErrorCodeEnum.UNKNOWN_PRODUCT)

    amount = plan.amount

    if settings.APP_DEBUG:
        amount = 0.01

    now = get_beijing_time()
    source = request.headers.get(constants.HEADER_SOURCE, enums.SourceEnum.UNKNOWN.value)
    new_order = LectureOrders.create(
        user_id=user_id,
        product_id=plan.biz_id,
        option=plan.title,
        amount=amount,
        status=OrderStatus.PENDING,
        payment_type=PaymentType.WE_CHAT,
        created_at=now,
        updated_at=now,
        source=source
    )

    new_order.order_id = generate_order_str(new_order.id)

    new_order.save()

    try:
        resp = (
            WechatBaseClient()
            .get_wechat_pay()
            .post(
                url="/v3/pay/transactions/native",
                params={
                    "description": plan.title,
                    "appid": WechatBaseClient().get_wechat_pay().app_id,
                    "out_trade_no": new_order.order_id,
                    "time_expire": (now + timedelta(hours=3)).strftime("%Y-%m-%dT%H:%M:%S+08:00"),
                    "notify_url": urljoin(settings.DOMAIN, "/api/v2/order/wechat_notify"),
                    "amount": {"total": int(amount * 100), "currency": "CNY"},
                },
            )
        )
    except WeChatPayException as e:
        logger.error("WeChat payment result xml parsing error", exc_info=True)
        return common_error(code=ErrorCodeEnum.WECHAT_PAY_ERROR.error_code, message=e.errmsg)

    return common_success({"url": resp["code_url"], "order_id": new_order.order_id})


@router.post("/xml/create", response_model=CommonResponse, summary="创建订单")
async def xml_create_order(request: Request, req: OrderRequest, user=Depends(get_current_user)):
    user_id = user.biz_id
    plan = get_lecture_plan_by_biz_id(req.product_id)
    if not plan:
        return common_enum_error(ErrorCodeEnum.UNKNOWN_PRODUCT)

    amount = plan.amount
    if req.create_timestamp:
        if common_cache.get(f"order:lock:{req.create_timestamp}:{user_id}"):
            return common_enum_error(ErrorCodeEnum.ORDER_LOCKED)
        get_last_order_lock(req.create_timestamp, user_id)

    if settings.APP_DEBUG:
        amount = 0.01

    now = get_beijing_time()
    source = request.headers.get(constants.HEADER_SOURCE, enums.SourceEnum.UNKNOWN.value)
    new_order = LectureOrders.create(
        user_id=user_id,
        product_id=plan.biz_id,
        option=plan.title,
        amount=amount,
        status=OrderStatus.PENDING,
        payment_type=PaymentType.WE_CHAT,
        created_at=now,
        updated_at=now,
        source=source
    )

    new_order.order_id = generate_order_str(new_order.id)
    new_order.save()

    try:
        resp = (
            WechatBaseClient()
            .get_wechat_pay()
            .order.create(
                trade_type="NATIVE",
                body=plan.title,
                total_fee=int(amount * 100),
                notify_url=urljoin(settings.DOMAIN, "/api/v1/wechat/xml/wechat_notify"),
                out_trade_no=new_order.order_id,
            )
        )
        return common_success({"url": resp["code_url"], "order_id": new_order.order_id})
    except WeChatPayException as e:
        logger.error("WeChat payment result xml parsing error", exc_info=True)
        return common_error(code=ErrorCodeEnum.WECHAT_PAY_ERROR.error_code, message=e.errmsg)


class UnionPayVO(BaseModel):
    product_id: str = ""
    create_timestamp: Optional[int] = None
    order_id: str
    client: str = ""
    logid_url: Optional[HttpUrl] = None
    code: str = None
    state: str = None


@router.post("/union/pay", response_model=CommonResponse, summary="支付订单")
async def pay_union_order(request: Request, union_pay_vo: UnionPayVO):
    try:
        logger.info(f"union_pay: {union_pay_vo.__dict__}")
        if union_pay_vo.client not in ["AlipayClient", "MicroMessenger"]:
            return common_error("不支持的支付方式")
        order = LectureOrders.get_or_none(LectureOrders.order_id == union_pay_vo.order_id)
        if not order:
            return common_error("订单不存在")

        if order.status == OrderStatus.SUCCESS:
            return common_success(message="已支付成功")

        plan = get_lecture_plan_by_biz_id(order.product_id)
        if not plan:
            return common_enum_error(ErrorCodeEnum.UNKNOWN_PRODUCT)

        amount = plan.amount
        if settings.APP_DEBUG:
            amount = 0.01

        logger.info(f"union/pay logid_url: {str(union_pay_vo.logid_url)}, key: order_id_{union_pay_vo.order_id}")
        if union_pay_vo.logid_url:
            open_api_cache.set(f"order_id_{union_pay_vo.order_id}", str(union_pay_vo.logid_url))
        try:
            # 根据request header中的user-agent判断是微信还是支付宝
            user_agent = request.headers.get("User-Agent") or ""
            if not user_agent:
                return common_error("不安全的请求")
            if union_pay_vo.client == "MicroMessenger" or "MicroMessenger" in user_agent:
                if union_pay_vo.code is None:
                    return common_enum_error(ErrorCodeEnum.WECHAT_NEED_CODE)
                token = direct_access_token_by_code("pc", union_pay_vo.code)
                if not token:
                    return common_enum_error(ErrorCodeEnum.WECHAT_UNAUTHORIZED)
                pc_open_id = token["openid"]
                resp = (
                    WechatBaseClient()
                    .get_wechat_pay()
                    .order.create(
                        trade_type="JSAPI",
                        body=plan.title,
                        user_id=pc_open_id,
                        product_id=plan.biz_id,
                        total_fee=int(amount * 100),
                        notify_url=urljoin(settings.DOMAIN, "/api/v1/wechat/xml/wechat_notify"),
                        out_trade_no=order.order_id,
                    )
                )
                order.payment_type = PaymentType.WE_CHAT
                order.save()

                prepay_id = resp.get("prepay_id")
                if not prepay_id:
                    return common_error("微信支付预下单失败")
                timestamp = str(int(time.time()))
                nonce_str = random_string(32)
                params = (
                    WechatBaseClient()
                    .get_wechat_pay()
                    .jsapi.get_jsapi_params(prepay_id=prepay_id, timestamp=timestamp, nonce_str=nonce_str)
                )
                return common_success({"channel": "WECHAT", "params": params, "order_id": order.order_id})
            elif union_pay_vo.client == "AlipayClient" or "AlipayClient" in user_agent:
                # 支付宝支付
                order_string = alipay.api_alipay_trade_wap_pay(
                    out_trade_no=order.order_id,
                    total_amount=float(amount),
                    subject=plan.title,
                    qr_pay_mode=4,
                    qrcode_width=150,
                    notify_url=urljoin(settings.DOMAIN, "/api/v2/order/alipay/status"),
                )
                order.payment_type = PaymentType.ALIPAY
                order.save()
                return common_success({"channel": "ALIPAY", "params": order_string, "order_id": order.order_id})
            else:
                return common_error("不支持的支付方式")
        except WeChatPayException as e:
            logger.error("WeChat payment result xml parsing error", exc_info=True)
            return common_error(code=ErrorCodeEnum.WECHAT_PAY_ERROR.error_code, message=e.errmsg)
    except Exception as e:
        logger.error("union pay error", exc_info=True)
        return common_error(f"union pay error, {e}")


@router.post("/union/create", response_model=CommonResponse, summary="创建订单")
async def create_union_order(request: Request, req: OrderRequest, user=Depends(get_current_user)):
    try:
        user_id = user.biz_id
        plan = get_lecture_plan_by_biz_id(req.product_id)
        if not plan:
            return common_enum_error(ErrorCodeEnum.UNKNOWN_PRODUCT)

        amount = plan.amount
        if req.create_timestamp:
            if common_cache.get(f"order:lock:{req.create_timestamp}:{user_id}"):
                return common_enum_error(ErrorCodeEnum.ORDER_LOCKED)
            get_last_order_lock(req.create_timestamp, user_id)

        if settings.APP_DEBUG:
            amount = 0.01

        now = get_beijing_time()
        source = request.headers.get(constants.HEADER_SOURCE, enums.SourceEnum.UNKNOWN.value)
        new_order = LectureOrders.create(
            user_id=user_id,
            product_id=plan.biz_id,
            option=plan.title,
            amount=amount,
            status=OrderStatus.PENDING,
            created_at=now,
            updated_at=now,
            source=source
        )

        new_order.order_id = generate_order_str(new_order.id)
        new_order.save()
        return common_success({"order_id": new_order.order_id, "user_id": user.biz_id})
    except Exception:
        logger.error("create order error", exc_info=True)
        return common_error("create order error")
