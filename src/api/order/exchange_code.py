import uuid
from datetime import datetime, timedelta, timezone

from fastapi import APIRouter, Body, Depends, HTTPException, Request

from src.database import enums, constants
from src.logger.logUtil import get_logger
from src.model.exchange_code import ExchangeCode, ExchangeRecord, ProvidedType
from src.model.lecture_plan_manager import get_lecture_plan_or_default
from src.model.order_manager import create_free_order
from src.model.user_manager import check_is_admin
from src.util.TokenUtil import get_current_user
from src.util.commonutil import ErrorCodeEnum, common_enum_error, common_success
from src.util.redis_util import get_redis_client

router = APIRouter(prefix="/api/v1/exchange_codes", tags=["exchange_codes"])

logger = get_logger(__name__)

"""
NOTES, 以下代码有使用 GPT 生成并在修改中...

场景一: 已经注册用户主动兑换
    step1. user 提供 exchange_code, 调用兑换接口，写入一条 exchange_record, types 为实时兑换，并注意一个用户兑换的数量
           (endpoint: /exchange)
场景二: 未注册用户注册 or 已注册用户登录 被动兑换
    step1. admin 生成一批兑换码，写入 exchange_codes (endpoint: /generate)
    step2. admin 分发一批兑换码给某一批用户，每个用户写入一条 exchange_records,
           types 为预先申请，代表用户注册成功后就自动兑换 (endpoint /distribute and /distributes 为一个或者一批分发)
    (optional, step1 或者 step2 可合成一步)
    step3. user 注册成功 or 登录成功，查 exchange_records, 有预先申请则进行兑换，更新额度等数据
场景三: 合作企业（购买企业产品，为该用户 被动兑换
    step1. 合作企业通过 API 为某个用户分发一个码
    step2. 场景二类似
场景四: 多个用户同时兑换同一个码，这个码要限制兑换次数
"""


# 1. 生成一批兑换券
@router.post("/generate")
def generate_exchange_codes(
        nums: int,
        plan_id: str = "",
        exchange_code="",
        exchange_type: int = 0,
        batch_id: str = str(uuid.uuid4()),
        user=Depends(get_current_user),
):
    if not check_is_admin(user.biz_id):
        return common_enum_error(ErrorCodeEnum.USER_UNAUTHORIZED)

    if nums <= 0:
        raise HTTPException(status_code=400, detail="兑换码数量必须为正整数")
    expired_date = datetime.now(timezone.utc) + timedelta(days=365)  # 默认365天有效期
    plan = get_lecture_plan_or_default(plan_id)
    codes = []
    if exchange_type == 0:
        for _ in range(nums):
            # TODO 这个需要生成 6 位满足 [A-Z0-9]{6} 的 exchange_code, 并且唯一
            exchange_code = str(uuid.uuid4()).replace("-", "")[:12].upper()  # 简单生成唯一兑换码
            code = ExchangeCode(
                exchange_code=exchange_code,
                exchange_code_type=plan.biz_id,
                batch_id=batch_id,
                expired_date=expired_date,
            )
            code.save()
            codes.append(code)
    else:
        if exchange_code == "":
            exchange_code = str(uuid.uuid4()).replace("-", "")[:12].upper()  # 简单生成唯一兑换码
        code = ExchangeCode(
            exchange_code=exchange_code,
            exchange_code_type=plan.biz_id,
            batch_id=batch_id,
            expired_date=expired_date,
            exchange_code_times=nums,
        )
        code.save()
    return common_success(
        {
            "batch_id": batch_id,
            "total_generated": nums,
            "expired_date": expired_date.isoformat(),
        }
    )


# 2. 分发未注册用户的兑换码
@router.post("/distribute")
def distribute_exchange_code(batch_id: str, user_ids: list[str] = Body(..., embed=True)):
    check_is_admin("")
    records = []
    for user_id in user_ids:
        # 找到未分发的兑换码
        exchange = (
            ExchangeCode.select()
            .where((ExchangeCode.batch_id == batch_id) & (ExchangeCode.has_provided is False))
            .first()
        )
        if not exchange:
            raise HTTPException(status_code=404, detail="没有可分发的兑换码")
        # 更新兑换码为已分发
        exchange.has_provided = True
        exchange.save()

        # 创建兑换记录
        record = ExchangeRecord(
            user_id=user_id,
            exchange_code=exchange.exchange_code,
            batch_id=batch_id,
            types="pre",
            has_exchanged=False,
            provided_type=ProvidedType.CUSTOMER_SERVICE,
            provided_at=datetime.now(timezone.utc),
        )
        record.save()
        records.append(record)

    return common_success(data={"total_distributed": len(records), "records": [r.id for r in records]})


# 用户注册后自动更新兑换记录, 该函数在新用户注册成功后自动触发, 后面可移动到公共模块处
def handle_user_registration(user_id: str):
    record = ExchangeRecord.get_or_none(ExchangeRecord.user_id == user_id, ExchangeRecord.has_exchanged is False)
    if record:
        # TODO, 这里有一个公共函数，实现给某个用户添加多少额度
        # SOME_FUNCTION()
        record.has_exchanged = True
        record.exchanged_at = datetime.now(timezone.utc)
        record.save()


# 3. 用户兑换码兑换
@router.post("/exchange")
async def redeem_exchange_code(request: Request, exchange_code: str, user=Depends(get_current_user)):
    user_id = user.biz_id
    exchange_entity: ExchangeCode = ExchangeCode.get_or_none(
        ExchangeCode.exchange_code == exchange_code, ExchangeCode.delete_flag == 0
    )
    if exchange_entity is None:
        logger.error(f"exchange_code: {exchange_code} not found")
        return common_enum_error(ErrorCodeEnum.EXCHANGE_CODE_ERROR)
    if exchange_entity.expired_date.replace(tzinfo=timezone.utc) < datetime.now(timezone.utc):
        logger.error(f"exchange_code: {exchange_code} has expired")
        return common_enum_error(ErrorCodeEnum.EXCHANGE_CODE_EXPIRED)
    if exchange_entity.has_provided:
        if exchange_entity.exchange_code_times <= 1:
            # 预先生成好了的对应code的兑换记录，只要判断是否已经兑换过
            record = ExchangeRecord.get_or_none(
                ExchangeRecord.exchange_code == exchange_code,
                ExchangeRecord.has_exchanged is False,
                ExchangeRecord.user_id == user_id,
                ExchangeRecord.delete_flag == 0,
            )
            if not record:
                logger.error(f"exchange_code: {exchange_code} has been used")
                return common_enum_error(ErrorCodeEnum.EXCHANGE_CODE_USED)

            record.has_exchanged = True
            record.exchanged_at = datetime.now(timezone.utc)
            record.save()
        else:
            # 大于等于1 的次数，用redis限制兑换次数，防止并发兑换
            redisClient = get_redis_client()
            if not redisClient:
                logger.error("redis connection error")
                return common_enum_error(ErrorCodeEnum.FAILED)

            key = f"exchange_code:{exchange_code}"
            # Increment the exchange count
            count = redisClient.incr(key)
            if count == 1:
                # Set the expiration time for the key
                redisClient.expireat(key, exchange_entity.expired_date)

            # Check if the exchange count exceeds the allowed times
            if count > exchange_entity.exchange_code_times:
                logger.error(f"exchange_code: {exchange_code} has exceeded the allowed exchange times")
                return common_enum_error(ErrorCodeEnum.EXCHANGE_CODE_USED)

            # # 暂时先加这个限制， 后续要用活动表去限制code的用途而不是直接写死在代码
            count = (
                ExchangeRecord.select()
                .where(
                    ExchangeRecord.user_id == user_id,
                    ExchangeRecord.delete_flag == 0,
                    ExchangeRecord.batch_id == exchange_entity.batch_id,
                )
                .count()
            )
            if count >= 1:
                logger.error(f"user: {user_id} has exchanged more than once")
                return common_enum_error(ErrorCodeEnum.EXCHANGE_CODE_HAS_CHARGE)

            # Create a new exchange record
            record = ExchangeRecord(
                user_id=user_id,
                exchange_code=exchange_code,
                types="real",
                batch_id=exchange_entity.batch_id,
                has_exchanged=True,
                exchanged_at=datetime.now(timezone.utc),
                provided_type=ProvidedType.REAL_TIME_EXCHANGE,
                provided_at=datetime.now(timezone.utc),
            )
            record.save()
    else:
        # 未预先生成的兑换码，需要实时兑换
        # 先判断是否已经兑换过
        record = ExchangeRecord.get_or_none(
            ExchangeRecord.exchange_code == exchange_code,
            ExchangeRecord.delete_flag == 0,
        )
        if record:
            logger.error(f"exchange_code: {exchange_code} has been used")
            return common_enum_error(ErrorCodeEnum.EXCHANGE_CODE_USED)
        # # 暂时先加这个限制， 后续要用活动表去限制code的用途而不是直接写死在代码
        count = (
            ExchangeRecord.select()
            .where(
                ExchangeRecord.user_id == user_id,
                ExchangeRecord.delete_flag == 0,
                ExchangeRecord.batch_id == exchange_entity.batch_id,
            )
            .count()
        )
        if count >= 1:
            logger.error(f"user: {user_id} has exchanged more than once")
            return common_enum_error(ErrorCodeEnum.EXCHANGE_CODE_HAS_CHARGE)

        # 实时兑换，直接写入兑换记录
        record = ExchangeRecord(
            user_id=user_id,
            exchange_code=exchange_entity.exchange_code,
            types="real",
            batch_id=exchange_entity.batch_id,
            has_exchanged=True,
            exchanged_at=datetime.now(timezone.utc),
            provided_type=ProvidedType.REAL_TIME_EXCHANGE,
            provided_at=datetime.now(timezone.utc),
        )
        exchange_entity.has_provided = True
        exchange_entity.save()
        record.save()
    source = request.headers.get(constants.HEADER_SOURCE, enums.SourceEnum.UNKNOWN.value)
    # 更新兑换记录
    create_free_order(user_id, exchange_entity.exchange_code_type, exchange_code,
                      source=source)

    return common_success(data={"message": "兑换成功"})


@router.get("/latest_code")
async def query_latest_code(batch_id, nums: int = 10, user=Depends(get_current_user)):
    check_is_admin(user.biz_id)

    List = (
        ExchangeCode.select()
        .where(
            ExchangeCode.batch_id == batch_id,
            ExchangeCode.has_provided == False,  # noqa E712
        )
        .order_by(ExchangeCode.created_at.desc())
        .paginate(1, nums)
    )
    if not List:
        return common_enum_error(ErrorCodeEnum.NOT_FOUND)

    return common_success(data={"exchange_code": [exchange.exchange_code for exchange in List]})
