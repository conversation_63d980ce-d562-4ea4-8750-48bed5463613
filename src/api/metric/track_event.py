import json
from typing import Optional, Any

from fastapi import APIRouter, Request
from pydantic import BaseModel

from config import settings
from src.util.commonutil import common_error, common_success
from src.util.redis_util import get_redis_client

router = APIRouter(prefix="/api/v1/track", tags=["track-event"])


# 埋点数据模型
class EventData(BaseModel):
    ip: Optional[str]
    user_agent: str
    called_url: str
    information: Any = None


class TrackEvent(BaseModel):
    session_id: str
    user_id: Optional[str]
    event_time: str  # 时间
    event_type: str  # 事件类型
    channel_id: Optional[str]  # 渠道 ID
    event_data: EventData


@router.post("/event")
async def event(request: Request, event: TrackEvent):
    try:
        # 将事件推送到 Redis 频道
        redis_client = get_redis_client(request.app.state.redis_pool)
        if redis_client:
            redis_client.publish(settings.REDIS_EVENT_CHANNEL, json.dumps(event.model_dump()))
            return common_success({"message": "Event published successfully"})
        else:
            return common_error("Failed create Redis pubsub")
    except Exception:
        return common_error("Track event failed")
