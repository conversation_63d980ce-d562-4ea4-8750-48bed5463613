from typing import Optional

from fastapi import APIRouter, Depends
from pydantic import BaseModel

from src.logger.logUtil import get_logger
from src.model.user_manager import get_user_by_mobile, hash_password
from src.util.commonutil import common_error, common_success
from src.util.TokenUtil import verify_key
from src.util.wechat_oauth import wechat_base_client

logger = get_logger(__name__)

router = APIRouter(prefix="/api/v1/help", tags=["help"])


@router.post("/wechat-official-account")
async def wechat_offical_account(key: str = Depends(verify_key)):
    images = wechat_base_client.material.batchget("image")
    media_id = images["item"][0]["media_id"]

    resp = wechat_base_client.menu.create(
        {
            "button": [
                {
                    "name": "商务合作",
                    "sub_button": [
                        {
                            "type": "media_id",
                            "name": "商务合作",
                            "media_id": media_id,
                        }
                    ],
                },
                {"type": "view", "name": "视频总结", "url": "https://aihaoji.com/zh"},
                {
                    "type": "media_id",
                    "name": "用户群",
                    "media_id": media_id,
                },
            ]
        }
    )
    # resp is a dict
    return common_success(data={"media_id": media_id, "resp": resp})


class BindVO(BaseModel):
    phone: int
    email: str
    password: Optional[str] = None


@router.post("/bind-email-for-phone-user")
async def bind_email_for_phone_user(bind_vo: BindVO, key: str = Depends(verify_key)):
    po = get_user_by_mobile(bind_vo.phone)

    if not po:
        return common_error(f"手机号用户：{bind_vo.phone} 不存在")
    # 更新是否有验证邮箱
    password = bind_vo.password
    if not password:
        password = bind_vo.email
    po.mail_access = True
    po.email = bind_vo.email
    po.password = hash_password(password)
    po.save()
    return common_success(data={"message": f"Bind email success for phone: {bind_vo.phone}"})
