import asyncio
import json
import random
import string
import threading
from uuid import UUID

from fastapi import APIRouter, Depends, File, Request, UploadFile

from config import settings
from src.core import deps
from src.api.task.task_api import handle_content
from src.logger.logUtil import get_logger
from src.model.User_action_record_manager import (
    UserActionProcess,
    UserActionType,
    add_user_action_record,
    handle_task_duration_record,
    rollback_user_action_record,
)
from src.model.article_manager import LectureArticle
from src.model.member_manager import (
    MembershipTaskRights,
    get_lecture_member_by_user_id,
    get_member_info,
    sub_member_lock_count,
    sub_member_point,
)
from src.model.oplatform_manager import sub_open_platform_point
from src.model.task_manager import (
    TASK_SOURCE_CHOICES,
    LectureTask,
    TaskVO,
    UpdateStatusVO,
    get_by_biz_id,
    get_latest_task,
    update_recent_task,
)
from src.orm.pgrepo import db
from src.oss.tencent_oss_upload import get_credential
from src.util.TokenUtil import get_current_user, verify_key
from src.util.commonutil import CommonResponse, assert_field, common_error, common_success
from src.util.dateUtil import get_current_time_in_beijing
from src.util.url_handle import SourceHandler, UrlResult
from src.services.lock_points_services import points_revert_source_task, points_rectification_source_task

logger = get_logger(__name__)

router = APIRouter(prefix="/api/v2/task", tags=["task"])


@router.post("/getLatest", include_in_schema=False)
async def get_latest(task_id: str = None, key: str = Depends(verify_key)):
    try:
        task = get_latest_task(task_id)
        if task:
            data = {k: v for k, v in task.__data__.items() if v is not None}
            if not task.open_platform_id:
                member = get_member_info(task.user_id)
                if member:
                    rights = MembershipTaskRights.from_membership_type(member.membership_type)
                    data.update({"rights": rights or {}})
            else:
                data.update({"rights": MembershipTaskRights.VIP})
            ctrl_params = task.ctrl_params
            if ctrl_params:
                try:
                    customers = settings.CUSTOMER_SERVICE_SEND.split(",")
                    all_flag = customers and task.user_id in customers
                    if all_flag:
                        ctrl_params.update({"publishArticle": True})
                    data.update({"ctrl_params": ctrl_params})
                except json.decoder.JSONDecodeError as e:
                    logger.error(f"JSON decode error for ctrl_params: {ctrl_params}, error: {e}")
            return common_success(data=data)
        else:
            return common_success()
    except Exception:
        logger.exception("任务获取失败")
        return common_error("获取失败")


locks = {}


def get_lock(task_id):
    if task_id not in locks:
        locks[task_id] = threading.Lock()
    return locks[task_id]


def handle_point_and_record(user_id, biz_id, new_point, action_type, process_status, process_remark):
    tasks = get_by_biz_id(biz_id)
    if tasks.open_platform_id:
        update_flag = sub_open_platform_point(open_platform_id=tasks.open_platform_id, point=new_point)
    else:
        update_flag = sub_member_point(user_id=user_id, point=new_point)

    if not update_flag:
        logger.error(
            f"积分扣除失败, user_id={user_id},open_platform_id= {tasks.open_platform_id} biz_id={biz_id}, new_point={new_point}"
        )
        return common_error("您剩余使用额度不足以转录当前内容，请充值。", code=10002)
    # 新增积分的记录
    add_user_action_record(
        user_id=user_id,
        user_action_type=action_type,
        process_status=process_status,
        process_remark=process_remark,
        points=abs(new_point),
        action_relate_id=biz_id,
    )
    return None


@router.post(
    "/status",
    response_model=CommonResponse,
    summary="更新任务状态",
    description="更新任务状态，如果任务完成，需要传递完成时间",
)
async def update_status(
        update_status_model: UpdateStatusVO, key: str = Depends(verify_key),
        redis_: deps.RedisSessionDep = deps.RedisSessionDep
):
    # 创建一个字典来存储需要更新的字段
    update_fields = update_status_model.dict(exclude_unset=True)
    # 使用 ** 运算符将字典传递给 update 方法
    update_data = dict()
    for key, value in update_fields.items():
        if key == "biz_id":
            continue
        if value:
            update_data[key] = value
    if update_status_model.status == "fail":
        # 返还积分
        success, message, user_id = await points_revert_source_task(
            task_id=update_status_model.biz_id,
            reason=update_status_model.remark,
            user_action_type=UserActionType.UNLOCK_VIDEO
        )
        line_number = (
            LectureTask.update(**update_data).where(LectureTask.biz_id == update_status_model.biz_id).execute()
        )
        if user_id:
            update_recent_task(user_id)
        if line_number == 0:
            return common_error("更新失败")
        return common_success()
    elif update_status_model.duration_minutes and update_status_model.duration_minutes > 0:
        # 如果带了时间，需要判断下任务的扣分是否合理
        try:
            await points_rectification_source_task(
                task_id=update_status_model.biz_id,
                duration_minutes=update_status_model.duration_minutes
            )
        except Exception:
            logger.exception("积分修正异常")

    line_number = 0
    if len(update_data.keys()) > 0:
        line_number = LectureTask.update(**update_data).where(
            LectureTask.biz_id == update_status_model.biz_id).execute()
    if line_number == 0:
        logger.error(f"更新失败：\n{update_fields}")
        return common_error("更新失败")
    else:
        return common_success()


@router.post("/finish", response_model=CommonResponse, include_in_schema=False)
async def finish(
        request: Request, biz_id: str, oss_path: str, key: str = Depends(verify_key), file: UploadFile = File(...)
):
    assert_field(biz_id, "biz_id is required")
    if file.filename.endswith(".zip"):
        # 后面的内容全部异步处理：
        contents = await file.read()
        # 你可以在这里处理 ZIP 文件的内容，例如保存到磁盘或解压缩
        # 解压缩
        asyncio.create_task(handle_content(biz_id, oss_path, contents))

        return common_success()
    else:
        return common_error("file type is not valid")


@router.get("/verify", summary="历史创建", include_in_schema=False)
async def pre_verify(request: Request, url: str, task_source: str, duration: int = 0, user=Depends(get_current_user)):
    assert_field(url, "url is required")
    assert_field(task_source, "task_source is required")
    member = get_lecture_member_by_user_id(user.biz_id)

    if task_source and task_source == TASK_SOURCE_CHOICES["upload"]:
        # if member.membership_type != MembershipType.VIP:
        #     return common_error("只有高级VIP才能使用上传功能", code=403)
        if member.points < duration:
            return common_error("您剩余使用额度不足以转录当前内容，请充值。", code=10002)
        return common_success()

    # 判断有没有人创建过对应任务，如果创建过，就直接提示已经创建过
    task: LectureTask = (
        LectureTask.select()
        .where((LectureTask.url == url) & (LectureTask.status == "finish") & (LectureTask.delete_flag == 0))
        .first()
    )
    # 如果存在就返回
    if task:
        return common_success(
            UrlResult(
                url=task.url,
                title=task.name,
                flag=True,
                source=task.task_source,
                author=task.author,
                duration=task.duration_minutes,
            ).to_json()
        )

    result = SourceHandler.handle_source(url, task_source)
    if not result.flag:
        return common_error("非法链接", code=400)

    if member.points < duration:
        return common_error("您剩余使用额度不足以转录当前内容，请充值。", code=10002)

    return common_success(result.to_json())


@router.post("/create", response_model=CommonResponse, include_in_schema=False)
async def create_task(request: Request, lec: TaskVO, user=Depends(get_current_user)):
    assert lec.url, "The 'url' field of 'TaskVO' is required."
    lec.user_id = user.biz_id

    try:
        # 判断会员
        member = get_lecture_member_by_user_id(user.biz_id)
        if lec.task_source == TASK_SOURCE_CHOICES["upload"]:
            lec.author = user.name
            # 如果是upload任务，就校验是否有上传文件
            # if not member or member.membership_type != MembershipType.VIP:
            #     return common_error("高级VIP用户才能使用上传解析")
            if not lec.oss_key:
                return common_error("请上传文件")
            # 这个时候的url 和 osskey 要换一下
            lec.url = lec.oss_key

        # 如果duration_minutes < 0，就直接返回
        if lec.duration_minutes < 0:
            return common_error("视频时长不合法")

        # 如果会员时长不足
        if member.points < lec.duration_minutes:
            return common_error("您剩余使用额度不足以转录当前内容，请充值。")
        query = lec.ctrl_params.replace("'", '"').replace(" ", "")
        task_arr = list(
            LectureTask.select().where(
                LectureTask.url == lec.url, LectureTask.ctrl_params == query, LectureTask.delete_flag == 0
            )
        )
        if task_arr:
            for idx in task_arr:
                if idx and idx.user_id == lec.user_id:
                    return common_error("任务已经创建过")

        # 判断是否有任务已经完成
        lec.biz_id = str(UUID(int=random.getrandbits(128)))
        lec.token = "".join(random.sample(string.ascii_letters + string.digits, 6))
        task = task_arr[0] if task_arr else None
        if task and task.status and task.status == "finish":
            lec.article_biz_id = task.article_biz_id
            lec.status = "finished"
            lec.process_percent = 100
            lec.ctrl_params = task.ctrl_params
            lec.start_time = get_current_time_in_beijing()
            lec.end_time = get_current_time_in_beijing()
            lec.ctrl_params = task.ctrl_params
            lec.name = task.name
        else:
            lec.start_time = lec.start_time or get_current_time_in_beijing()
            lec.name = lec.name or lec.url

            # 判断没有就插入一条新的， 有就查询出来这条数据
            article = LectureArticle.create(
                delete_flag=0,
                biz_id=str(UUID(int=random.getrandbits(128))),
                name=lec.name,
                article_source=lec.task_source,
                language="zh",
                create_time=lec.start_time,
                update_time=lec.start_time,
                video_url=lec.url,
                img_url=lec.img_url,
                author=lec.author,
            )
            lec.article_biz_id = article.biz_id
        with get_lock(user.biz_id):
            if lec.status and lec.status == "finished":
                lock_count_flag = sub_member_lock_count(user.biz_id)
                if not lock_count_flag:
                    # 如果解锁次数不足，就视频时长的1/2时长，最少为1
                    lec.duration_minutes = max(lec.duration_minutes // 2, 1)
                else:
                    add_user_action_record(
                        user.biz_id, UserActionType.UNLOCK_ARTICLE, UserActionProcess.SUB, "用户解锁文章", 1, lec.biz_id
                    )
                    LectureTask.create(**lec.__dict__)
                    lec.duration_minutes = 0
                    return common_success(data={"token": lec.token})
            if lec.duration_minutes > 0:
                points_sub_flag = sub_member_point(user.biz_id, lec.duration_minutes)
                if not points_sub_flag:
                    return common_error("您剩余使用额度不足以转录当前内容，请充值。")
            add_user_action_record(
                user.biz_id,
                UserActionType.UNLOCK_VIDEO,
                UserActionProcess.SUB,
                "用户转换视频",
                lec.duration_minutes,
                lec.biz_id,
            )
            if member:
                lec.__dict__["order"] = member.membership_type
            arr = list(
                LectureTask.select().where(
                    LectureTask.user_id == user.biz_id,
                    ((LectureTask.status == "confirm") | (LectureTask.status == "processing")),
                    LectureTask.delete_flag == 0,
                )
            )
            if arr and len(arr) >= 3:
                lec.status = "waiting"
            LectureTask.create(**lec.__dict__)
        return common_success(data={"biz_id": lec.biz_id, "duration": lec.duration_minutes})
    except Exception:
        logger.exception("create task error")
        return common_error("创建失败")


@router.get("/upload/credential")
async def get_upload_credential(request: Request, user=Depends(get_current_user)):
    return common_success(data=get_credential())
