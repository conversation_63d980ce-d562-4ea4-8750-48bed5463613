import random
from collections import defaultdict
from typing import Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query
from playhouse.shortcuts import model_to_dict
from pydantic import BaseModel

from src.logger.logUtil import get_logger
from src.model.folder import Folder, get_folder_by_id, get_folder_by_name
from src.model.task_manager import LectureTask, get_by_biz_id
from src.orm.pgrepo import db
from src.util.commonutil import ErrorCodeEnum, common_enum_error, common_success,CommonResponse
from src.util.TokenUtil import get_current_user

router = APIRouter(prefix="/api/v1/folder")
logger = get_logger(__name__)

DEFAULT_FOLDER = "默认笔记本"
DEFAULT_CONTENTS = 4  # 首页默认展示的 content 个数(sub_folders + tasks <=4)
MAX_FOLDER_DEPTH = 4


def get_folder_depth(folder_id: Optional[int]) -> int:
    """
    获取文件夹的层级深度，根级为 1，依次递增
    :param folder_id: 当前文件夹 ID
    :return: 当前文件夹的层级
    """
    depth = 1
    current_folder = Folder.get_or_none(Folder.id == folder_id)

    while current_folder and current_folder.parent_id is not None:
        depth += 1
        current_folder = Folder.get_or_none(Folder.id == current_folder.parent_id)

    return depth


def get_folder_content_count(f: Folder, user_id, keyword=None) -> int:
    query_conditions = [
        Folder.parent_id == f.id,
        Folder.delete_flag == 0,
        Folder.user_id == user_id,
    ]
    task_conditions = [
        LectureTask.folder_id == f.id,
        LectureTask.delete_flag == 0,
        LectureTask.user_id == user_id,
    ]

    # 关键字搜索, 查找所有文件夹与任务
    if keyword:
        query_conditions.append((Folder.name ** f"%{keyword}%"))
        task_conditions.append((LectureTask.name ** f"%{keyword}%"))
    folders_count = Folder.select().where(*query_conditions).count()
    tasks_count = LectureTask.select().where(*task_conditions).count()
    return folders_count + tasks_count


class CreateVO(BaseModel):
    name: str
    parent_id: Optional[int] = None


@router.post("/create", summary="创建笔记本")
async def create_folder(create_vo: CreateVO, user=Depends(get_current_user)):
    parent_folder = get_folder_by_id(create_vo.parent_id or None, user.biz_id)
    if create_vo.parent_id not in [None, 0] and not parent_folder:
        return common_enum_error(ErrorCodeEnum.FOLDER_NOT_EXISTS)
    if not parent_folder:
        depth = 1
    else:
        depth = parent_folder.depth + 1
    if depth >= MAX_FOLDER_DEPTH:
        return common_enum_error(ErrorCodeEnum.FOLDER_NESTED_ERROR)
    folder, created = Folder.get_or_create(
        user_id=user.biz_id,
        name=create_vo.name,
        parent_id=create_vo.parent_id or None,
        delete_flag=0,
        defaults={
            "biz_id": f"folder_{UUID(int=random.getrandbits(128))}",
            "depth": depth,
        },
    )
    if not created:
        return common_enum_error(ErrorCodeEnum.FOLDER_EXISTS_ERROR)
    elif parent_folder:
        parent_folder.total += 1
        parent_folder.save()
    return common_success(data=model_to_dict(folder, exclude=[Folder.delete_flag]))


class RenameVO(BaseModel):
    folder_id: int
    name: str


@router.post("/rename", summary="重命名笔记本")
async def rename_folder(rename_vo: RenameVO, user=Depends(get_current_user)):
    # 目前只支持在根目录上创建文件，不支持嵌套创建
    current_folder = get_folder_by_id(rename_vo.folder_id, user.biz_id)
    if not current_folder:
        return common_enum_error(ErrorCodeEnum.FOLDER_NOT_EXISTS)
    # 默认笔记本 不支持重命名
    if current_folder.name == DEFAULT_FOLDER:
        return common_enum_error(ErrorCodeEnum.FOLDER_CAN_NOT_RENAME)
    if get_folder_by_name(rename_vo.name, current_folder.parent_id or None, user.biz_id):
        return common_enum_error(ErrorCodeEnum.FOLDER_EXISTS_ERROR)
    current_folder.name = rename_vo.name
    current_folder.save()
    return common_success(data=model_to_dict(current_folder, exclude=[Folder.delete_flag]))


def get_folder_tree(parent_id, user_id, depth: Optional[int] = None, current_depth: int = 0):
    """递归获取文件夹及其子文件夹，支持最大递归深度"""
    folders = (
        Folder.select()
        .where((Folder.parent_id == (parent_id or None)) & (Folder.user_id == user_id) & (Folder.delete_flag == 0))
        .order_by(-Folder.created_at)
    )
    result = []

    for folder in folders:
        folder_dict = model_to_dict(folder, exclude=[Folder.delete_flag])

        # 递归获取子文件夹，受 depth 控制
        if depth is None or current_depth < depth:
            folder_dict["children"] = get_folder_tree(folder.id, user_id, depth, current_depth + 1)

        result.append(folder_dict)

    return result


@router.get("/tree", summary="笔记本列表-tree")
async def get_user_folders(
    user: dict = Depends(get_current_user),
    depth: Optional[int] = Query(None, ge=0, description="递归的最大深度，为空表示不限制"),
):
    """
    获取当前用户的所有文件夹，支持嵌套返回，增加可选的最大递归深度控制
    """
    # 默认文件夹一定要有
    Folder.get_or_create(
        name=DEFAULT_FOLDER,
        parent_id=None,
        user_id=user.biz_id,
        delete_flag=0,
        defaults={"biz_id": f"folder_{UUID(int=random.getrandbits(128))}"},
    )
    try:
        folder_tree = get_folder_tree(parent_id=None, user_id=user.biz_id, depth=depth)
        return common_success(data=folder_tree)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取文件夹失败: {str(e)}")


def get_ancestor_folders_by_parent_id(parent_id: Optional[int]) -> list:
    """
    根据传入的 parent_id 递归获取上级目录，返回列表，
    列表中第一个元素为直接父目录，第二个为上一级，以此类推，
    当 parent_id 为 None 时终止查找。
    """
    ancestors = []
    if not parent_id:
        return ancestors
    # 根据传入的 parent_id获取父目录对象
    folder = Folder.get_or_none(Folder.id == parent_id)
    while folder is not None:
        ancestors.append(
            {
                "type": "folder",
                **model_to_dict(folder, exclude=[Folder.delete_flag]),
            }
        )
        # 如果当前文件夹没有父目录，则退出循环
        if folder.parent_id is None:
            break
        folder = Folder.get_or_none(Folder.id == folder.parent_id)
    return ancestors


@router.get("/content",response_model=CommonResponse, summary="笔记本内容")
async def get_folders(
    folder_id: int = 0,
    page_no: int = 1,
    page_size: int = 4,
    keyword: Optional[str] = None,
    user=Depends(get_current_user),
    source:Optional[str] = "pc"):
    if folder_id == 0:
        # 查询默认文件夹且depth为1的内容
        default_folder = get_folder_by_name(DEFAULT_FOLDER, None, user.biz_id)
        if default_folder and default_folder.depth == 1:
            folder_id = default_folder.id
        else:
            return common_enum_error(ErrorCodeEnum.FOLDER_NOT_EXISTS)

    current_folder = get_folder_by_id(folder_id, user.biz_id)
    if folder_id is not None and not current_folder:
        return common_enum_error(ErrorCodeEnum.FOLDER_NOT_EXISTS)
    query_conditions = [
        Folder.parent_id == folder_id,
        Folder.delete_flag == 0,
        Folder.user_id == user.biz_id,
    ]
    task_conditions = [
        LectureTask.folder_id == folder_id,
        LectureTask.delete_flag == 0,
        LectureTask.user_id == user.biz_id,
    ]

    # 关键字搜索, 查找所有文件夹与任务
    if keyword:
        query_conditions.append((Folder.name ** f"%{keyword}%"))
        task_conditions.append((LectureTask.name ** f"%{keyword}%"))
    folders = list(Folder.select().where(*query_conditions))
    tasks = list(LectureTask.select().where(*task_conditions).order_by(-LectureTask.start_time))
    # 合并并排序（文件夹在前）
    if source == "mobile":
        combined_items = sorted(
            [
                {
                    "type": "folder",
                    **model_to_dict(f, exclude=[Folder.delete_flag]),
                }
                for f in folders
            ] + [
                {"type": "task", **t.to_vo()}
                for t in tasks
            ],
            key=lambda x: (0 if x.get("type") == "folder" else 1, -x["id"]),
        )
    else:
        combined_items = [
            {"type": "task", **t.to_vo()}
            for t in tasks
        ]

    start_idx = (page_no - 1) * page_size
    end_idx = start_idx + page_size
    paginated_items = combined_items[start_idx:end_idx]
    current_folder = (
        model_to_dict(current_folder, exclude=[Folder.delete_flag]) if current_folder else {"id": 0, "name": "root"}
    )
    if source == 'mobile':
        # if keyword:
        # sum_li = folders+tasks
        current_folder["total"] = len(tasks)
    else:
        current_folder["total"] = len(tasks)
    return common_success(
        data={
            "ancestors": get_ancestor_folders_by_parent_id(current_folder.get("parent_id")),
            "current_folder": current_folder,
            "content": paginated_items,
            "page_no": page_no,
            "page_size": page_size,
        }
    )


def get_keyword_result(user_id, keyword, page_no, page_size):
    data = defaultdict(list)
    folder_conditions = [
        Folder.delete_flag == 0,
        Folder.user_id == user_id,
        (Folder.name ** f"%{keyword}%"),
    ]
    folders = Folder.select().where(*folder_conditions).order_by(-Folder.id)
    for folder in folders:
        data[folder.parent_id].append({"type": "folder", **model_to_dict(folder, exclude=[Folder.delete_flag])})
    task_conditions = [
        LectureTask.delete_flag == 0,
        LectureTask.user_id == user_id,
        (LectureTask.name ** f"%{keyword}%"),
    ]
    tasks = LectureTask.select().where(*task_conditions).order_by(-LectureTask.id)
    for task in tasks:
        data[task.folder_id].append({"type": "task", **task.to_vo()})
    result = []
    for folder_id, content in data.items():
        if folder_id is None:
            result.append(
                {
                    "folder": content[0],
                    "content": data.get(content[0].get('id', '-'), [])[:DEFAULT_CONTENTS],
                }
            )
        elif folder := get_folder_by_id(folder_id, user_id):
            folder_content = model_to_dict(folder, exclude=[Folder.delete_flag])
            folder_content["total"] = len(content)
            result.append(
                {
                    "folder": folder_content,
                    "content": content[:DEFAULT_CONTENTS],
                }
            )
    return result


@router.get("/first_page")
async def get_folder_tasks(
    page_no: int = 1,
    page_size: int = 4,
    keyword: Optional[str] = None,
    user=Depends(get_current_user),
):
    if keyword:
        result = get_keyword_result(user.biz_id, keyword, page_no, page_size)
        return common_success(
            data={
                "result": result[(page_no - 1) * page_size : page_no * page_size],
                "page_no": page_no,
                "page_size": page_size,
                "total": len(result),
            }
        )
    # 顶级文件夹查询条件
    folder_conditions = [
        Folder.delete_flag == 0,
        Folder.parent_id == None,  # noqa
        Folder.user_id == user.biz_id,
    ]
    # 查询顶级文件夹（分页）
    query = Folder.select().where(*folder_conditions)
    total_folder = query.count()
    top_folders = query.paginate(page_no, page_size)

    result = []
    for folder in top_folders:
        # 查询当前文件夹的前 4 个子文件夹
        sub_f_conditions = [
            Folder.delete_flag == 0,
            Folder.user_id == user.biz_id,
            Folder.parent_id == folder.id,
        ]

        sub_folders = list(Folder.select().where(*sub_f_conditions).order_by(-Folder.id).limit(DEFAULT_CONTENTS))

        # 查询当前文件夹的前 4 个任务
        sub_t_conditions = [
            LectureTask.delete_flag == 0,
            LectureTask.user_id == user.biz_id,
            LectureTask.folder_id == folder.id,
        ]
        tasks = list(LectureTask.select().where(*sub_t_conditions).order_by(-LectureTask.id).limit(DEFAULT_CONTENTS))
        # 合并并排序（文件夹在前）
        folder_contents = sorted(
            [
                {
                    "type": "folder",
                    **model_to_dict(f, exclude=[Folder.delete_flag]),
                }
                for f in sub_folders
            ]
            + [{"type": "task", **t.to_vo()} for t in tasks],
            key=lambda x: (0 if x.get("type") == "folder" else 1, -x["id"]),
        )[:DEFAULT_CONTENTS]
        result.append(
            {
                "folder": model_to_dict(folder, exclude=[Folder.delete_flag]),
                "content": folder_contents,
            }
        )
    return common_success(
        data={
            "result": result,
            "page_no": page_no,
            "page_size": page_size,
            "total": total_folder,
        }
    )


class MoveTaskVO(BaseModel):
    task_id: str
    folder_id: int


@router.post("/task/move", summary="移动文章到指定文件夹")
async def move_task(move_task_vo: MoveTaskVO, user=Depends(get_current_user)):
    folder = get_folder_by_id(move_task_vo.folder_id, user.biz_id)
    if not folder:
        return common_enum_error(ErrorCodeEnum.FOLDER_NOT_EXISTS)
    task = get_by_biz_id(move_task_vo.task_id)
    if not task:
        return common_enum_error(ErrorCodeEnum.TASK_NOT_EXISTS)
    with db.atomic():
        old_folder = get_folder_by_id(task.folder_id, user.biz_id)
        if old_folder:
            old_folder.total -= 1
            old_folder.save()
        folder.total += 1
        folder.save()
        # 这里要注意顺序，这个最后执行
        task.folder_id = move_task_vo.folder_id
        task.save()
    return common_success(data={"task_id": task.biz_id, "folder_id": task.folder_id})


class DeleteVO(BaseModel):
    folder_id: int
    name: str


@router.post("/delete")
async def delete_folder(delete_vo: DeleteVO, user=Depends(get_current_user)):
    if delete_vo.folder_id == 0 or delete_vo.name == DEFAULT_FOLDER:
        return common_enum_error(ErrorCodeEnum.FOLDER_CAN_NOT_DELETE)
    folder = (
        Folder.select()
        .where(
            Folder.user_id == user.biz_id,
            Folder.id == delete_vo.folder_id,
            Folder.name == delete_vo.name,
            Folder.delete_flag == 0,
        )
        .first()
    )
    if not folder:
        return common_enum_error(ErrorCodeEnum.FOLDER_NOT_EXISTS)
    default_folder = get_folder_by_name(DEFAULT_FOLDER, None, user.biz_id)
    if not default_folder:
        return common_enum_error((ErrorCodeEnum.FOLDER_NOT_EXISTS))
    # 将该目录下的所有 tasks 及子文件夹移动到 DEFAULT_FOLDER
    with db.atomic():
        # 移动任务
        tasks = LectureTask.select().where(
            LectureTask.user_id == user.biz_id,
            LectureTask.folder_id == delete_vo.folder_id,
            LectureTask.delete_flag == 0,
        )

        total = tasks.count()
        for task in tasks:
            task.folder_id = default_folder.id
            task.save()
        default_folder.total += total
        default_folder.save()

        # 移动子文件夹
        sub_f_conditions = [
            Folder.delete_flag == 0,
            Folder.user_id == user.biz_id,
            Folder.parent_id == folder.id,
        ]
        sub_folders = Folder.select().where(*sub_f_conditions)
        sub_folders_count = sub_folders.count()
        for sub_f in sub_folders:
            if get_folder_by_name(sub_f.name, default_folder.id, user.biz_id):
                sub_f.name = sub_f.name + f"({folder.name})"
            sub_f.parent_id = default_folder.id
            sub_f.depth = default_folder.depth + 1
            sub_f.save()
        default_folder.total += sub_folders_count
        default_folder.save()

        folder.delete_flag = 1
        folder.total = 0
        folder.save()
    return common_success()
