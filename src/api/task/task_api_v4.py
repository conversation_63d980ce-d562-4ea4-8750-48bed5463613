import asyncio
import json
import random
import string
from typing import Annotated, List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Request
from peewee import DoesNotExist
from playhouse.postgres_ext import fn
from pydantic import BaseModel, Field, constr

from config import settings
from src.api.task.task_api_v2 import get_lock
from src.logger.logUtil import get_logger
from src.mail.send_mail import send_finished_mail
from src.model.article_detail_manager import create_article_detail_by_section, update_article_detail_by_section
from src.model.article_manager import LectureArticle, get_article_by_id
from src.model.folder import get_folder_by_id
from src.model.lecture_ai import AITYPE, create_ai_task_with_task_type, find_ai_task
from src.model.member_manager import (
    MembershipTaskRights,
    get_lecture_member_by_user_id,
    get_member_info,
    sub_member_lock_count,
    sub_member_point,
)
from src.model.task_manager import (
    TASK_SOURCE_CHOICES,
    LectureTask,
    TaskVO,
    get_by_biz_id,
    get_latest_task,
    update_recent_task,
)
from src.model.User_action_record_manager import UserActionProcess, UserActionType, add_user_action_record
from src.model.user_manager import UserPO
from src.orm.pgrepo import db
from src.services.url_service import UrlService
from src.util.article_utils import do_outline_summary_tasks
from src.util.commonutil import CommonResponse, ErrorCodeEnum, common_enum_error, common_error, common_success
from src.util.dateUtil import get_current_time_in_beijing
from src.util.TokenUtil import get_current_user, verify_key

logger = get_logger(__name__)

router = APIRouter(prefix="/api/v4/task", tags=["tasks"])


class Section(BaseModel):
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    text: Optional[str] = ""
    speaker: Optional[str] = ""
    biz_id: Optional[str] = None
    origin_text: Optional[str] = None
    modified_text: Optional[str] = None
    out_language_modified_text: Optional[str] = None
    oss_pic_path: List[str] = []
    local_pic_path: List[str] = []
    index: Optional[int] = None
    pic_keywords: Optional[str] = None


class TaskInputVO(BaseModel):
    task_id: str
    video_lan: str = "zh"
    markdown_lan: str = "zh"
    outline: Optional[str] = None
    out_language_outline: Optional[str] = None
    total_summary: Optional[str] = None
    out_language_total_summary: Optional[str] = None
    total_summary_json: Optional[dict] = None
    oss_path: Optional[str] = None
    corpus: Optional[str] = None


@router.post("/getLatest", include_in_schema=False)
async def get_latest(task_id: Optional[str] = None, key: str = Depends(verify_key)):
    try:
        task = get_latest_task(task_id)
        if task:
            data = {k: v for k, v in task.__data__.items() if v is not None}
            if not task.open_platform_id:
                member = get_member_info(task.user_id)
                if member:
                    rights = MembershipTaskRights.from_membership_type(member.membership_type)
                    data.update({"rights": rights or {}})
            else:
                data.update({"rights": MembershipTaskRights.VIP})
            ctrl_params = task.ctrl_params
            if ctrl_params:
                try:
                    customers = settings.CUSTOMER_SERVICE_SEND.split(",")
                    all_flag = customers and task.user_id in customers
                    if all_flag:
                        ctrl_params.update({"publishArticle": True})
                    data.update({"ctrl_params": ctrl_params})
                except json.decoder.JSONDecodeError as e:
                    logger.error(f"JSON decode error for ctrl_params: {ctrl_params}, error: {e}")
            return common_success(data=data)
        else:
            return common_success()
    except Exception:
        logger.exception("任务获取失败")
        return common_error("获取失败")


@router.post("/create", response_model=CommonResponse, include_in_schema=False)
async def create_task(request: Request, lec: TaskVO, user=Depends(get_current_user)):
    assert lec.url, "The 'url' field of 'TaskVO' is required."
    lec.user_id = user.biz_id

    try:
        # 判断会员
        member = get_lecture_member_by_user_id(user.biz_id)
        if lec.task_source == TASK_SOURCE_CHOICES["upload"]:
            lec.author = user.name
            # 如果是upload任务，就校验是否有上传文件
            # if not member or member.membership_type != MembershipType.VIP:
            #     return common_error("高级VIP用户才能使用上传解析")
            if not lec.oss_key:
                return common_error("请上传文件")
            # 这个时候的url 和 osskey 要换一下
            lec.url = lec.oss_key
        elif lec.url_biz_id:
            urls = UrlService.get_url_by_biz_id(lec.url_biz_id)
            if urls:
                lec.img_url = urls.pic_path
                lec.url = urls.origin_url

        if lec.duration_minutes is None:
            lec.duration_minutes = 0
        # 如果duration_minutes < 0，就直接返回, 但允许 null
        if lec.duration_minutes < 0:
            return common_error("视频时长不合法")

        # 如果会员时长不足
        if member.points < lec.duration_minutes:
            return common_error("您剩余使用额度不足以转录当前内容，请充值。")
        folder = get_folder_by_id(lec.folder_id, user.biz_id)
        if not folder:
            return common_error("用户文件夹不存在")
        task_arr = list(
            LectureTask.select().where(
                LectureTask.url == lec.url,
                fn.json_extract_path_text(LectureTask.ctrl_params, "generate_mode")
                == lec.ctrl_params.get("generate_mode"),
                fn.json_extract_path_text(LectureTask.ctrl_params, "video_style") == lec.ctrl_params.get("video_style"),
                fn.json_extract_path_text(LectureTask.ctrl_params, "video_lan") == lec.ctrl_params.get("video_lan"),
                fn.json_extract_path_text(LectureTask.ctrl_params, "markdown_lan")
                == lec.ctrl_params.get("markdown_lan"),
                LectureTask.delete_flag == 0,
            )
        )
        if task_arr:
            for idx in task_arr:
                if idx and idx.user_id == lec.user_id:
                    return common_error("任务已经创建过")

        # 判断是否有任务已经完成
        lec.biz_id = str(UUID(int=random.getrandbits(128)))
        lec.token = "".join(random.sample(string.ascii_letters + string.digits, 6))
        task = task_arr[0] if task_arr else None
        if task and task.status and task.status == "finish":
            lec.article_biz_id = task.article_biz_id
            lec.status = "finished"
            lec.process_percent = 100
            lec.ctrl_params = task.ctrl_params
            lec.start_time = get_current_time_in_beijing()
            lec.end_time = get_current_time_in_beijing()
            lec.name = task.name
        else:
            lec.start_time = lec.start_time or get_current_time_in_beijing()
            lec.name = lec.name or lec.url

            # 判断没有就插入一条新的， 有就查询出来这条数据
            article = LectureArticle.create(
                delete_flag=0,
                biz_id=str(UUID(int=random.getrandbits(128))),
                name=lec.name,
                article_source=lec.task_source,
                language="zh",
                create_time=lec.start_time,
                update_time=lec.start_time,
                video_url=lec.url,
                img_url=lec.img_url,
                author=lec.author,
            )
            lec.article_biz_id = article.biz_id
        with get_lock(user.biz_id):
            if lec.status and lec.status == "finished":
                lock_count_flag = sub_member_lock_count(user.biz_id)
                if not lock_count_flag:
                    # 如果解锁次数不足，就视频时长的1/2时长，最少为1
                    lec.duration_minutes = max(lec.duration_minutes // 2, 1)
                else:
                    add_user_action_record(
                        user.biz_id, UserActionType.UNLOCK_ARTICLE, UserActionProcess.SUB, "用户解锁文章", 1, lec.biz_id
                    )
                    LectureTask.create(**lec.__dict__)
                    lec.duration_minutes = 0
                    folder.total += 1
                    folder.save()
                    return common_success(data={"token": lec.token})
            if lec.duration_minutes > 0:
                points_sub_flag = sub_member_point(user.biz_id, lec.duration_minutes)
                if not points_sub_flag:
                    return common_error("您剩余使用额度不足以转录当前内容，请充值。")
            add_user_action_record(
                user.biz_id,
                UserActionType.UNLOCK_VIDEO,
                UserActionProcess.SUB,
                "用户转换视频",
                lec.duration_minutes,
                lec.biz_id,
            )
            if member:
                lec.__dict__["order"] = member.membership_type
            arr = list(
                LectureTask.select().where(
                    LectureTask.user_id == user.biz_id,
                    (
                        (LectureTask.status == "waiting")
                        | (LectureTask.status == "confirm")
                        | (LectureTask.status == "processing")
                    ),
                    LectureTask.delete_flag == 0,
                )
            )
            if arr and len(arr) >= 1:
                lec.status = "waiting"

            LectureTask.create(**lec.__dict__)
            folder.total += 1
            folder.save()
        return common_success(data={"biz_id": lec.biz_id, "duration": lec.duration_minutes})
    except Exception:
        logger.exception("create task error")
        return common_error("创建失败")


class RenameTaskVO(BaseModel):
    task_id: int
    new_name: Annotated[str, constr(min_length=1, max_length=50)]


@router.post("/rename", include_in_schema=False)
def rename_task(
    rename_task_vo: RenameTaskVO,
    current_user: dict = Depends(get_current_user),
):
    """
    任务重命名，确保 name + folder_id 的唯一性
    """
    try:
        # 查询任务
        task = LectureTask.get_or_none(LectureTask.id == rename_task_vo.task_id, LectureTask.delete_flag == 0)
        if not task:
            return common_enum_error(ErrorCodeEnum.TASK_NOT_EXISTS)

        # 确保当前用户有权限修改该任务
        if task.user_id != current_user.biz_id:
            return common_enum_error(ErrorCodeEnum.USER_UNAUTHORIZED)

        # 检查 name + folder_id 是否唯一
        exists = LectureTask.get_or_none(
            (LectureTask.name == rename_task_vo.new_name)
            & (LectureTask.folder_id == task.folder_id)
            & (LectureTask.id != task.id)  # 排除自己
            & (LectureTask.delete_flag == 0)
        )
        if exists:
            return common_enum_error(ErrorCodeEnum.TASK_REPEAT)

        # 更新任务名称
        with db.atomic():
            task.name = rename_task_vo.new_name
            task.save()
        return common_success(data={"task_id": task.id, "new_name": rename_task_vo.new_name})

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Unexpected error: {str(e)}")


@router.post("/finish", response_model=CommonResponse, include_in_schema=False)
async def finish(tasks: TaskInputVO, key: str = Depends(verify_key)):
    """
    任务完成
    :param tasks:
    :param key:
    :return:
    """
    task = get_by_biz_id(tasks.task_id)
    if not task:
        return common_error("task not found")
    if task.status in ["finish", "finished"]:
        return common_enum_error(ErrorCodeEnum.TASK_REPEAT)

    # 获取文章id
    article = get_article_by_id(task.article_biz_id)

    if not article:
        return common_error("article not found")

    # 保存任务
    article.out_language = tasks.markdown_lan
    article.language = tasks.video_lan
    article.corpus = tasks.corpus
    article.save()

    # 更新任务状态
    task.status = "finish"
    task.process_percent = 100
    task.end_time = get_current_time_in_beijing()
    task.remark = ""
    task.save()

    # 创建 AI polishing 任务
    create_ai_task_with_task_type(
        user_id=task.user_id or task.open_platform_id,
        article_biz_id=task.article_biz_id,
        task_type=AITYPE.AI_POLISHING.value,
        ctrl_params=task.ctrl_params,
    )

    asyncio.create_task(do_outline_summary_tasks(article, out_language=tasks.markdown_lan))
    # 更新最近任务列表
    update_recent_task(task.user_id)
    # 发个邮件通知
    await send_email(task)
    return common_success()


class UpdateSectionDTO(BaseModel):
    task_id: str = Field(..., description="任务id")
    status: int = Field(..., description="section状态,1 创建 2 更新")
    sections: List[Section] = Field(..., description="任务sections")
    video_lan: str = Field(..., description="sections 里原视频的语言")
    markdown_lan: str = Field(..., description="sections 里用户选择的语言")


@router.post(
    "/update-sections",
    response_model=CommonResponse,
    summary="更新任务sections",
    description="更新任务状态sections",
    include_in_schema=False
)
async def update_sections(update_status_model: UpdateSectionDTO, key: str = Depends(verify_key)):
    """更新任务sections"""
    # 使用 ** 运算符将字典传递给 update 方法
    if update_status_model.status == 1:
        task = get_by_biz_id(update_status_model.task_id)
    else:
        task = find_ai_task(update_status_model.task_id)
    if not task:
        return common_error("task not found")
    # 获取文章id
    for section in update_status_model.sections:
        if update_status_model.status == 1:
            create_article_detail_by_section(section, task.article_biz_id)
        else:
            update_article_detail_by_section(
                section,
                task.article_biz_id,
                input_language=update_status_model.video_lan,
                output_language=update_status_model.markdown_lan,
            )
    return common_success()


async def send_email(task: LectureTask):
    try:
        if task.user_id:
            user = UserPO.get(UserPO.biz_id == task.user_id, UserPO.delete_flag == 0)
            if user.email:
                # 邮件通知
                send_finished_mail(user.email, task.name, task.biz_id, user.name)
    except DoesNotExist:
        logger.exception(f"获取用户信息失败，邮件发送失败!,bizId={task.user_id}")
    except Exception:
        logger.exception(f"Error processing user information for biz_id={task}")
