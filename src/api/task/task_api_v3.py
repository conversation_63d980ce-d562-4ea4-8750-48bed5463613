import random
import string
from typing import List
from uuid import UUID

from fastapi import APIRouter, Depends, Request
from peewee import DoesNotExist
from playhouse.postgres_ext import fn
from pydantic import BaseModel

from src.api.task.task_api_v2 import get_lock, handle_point_and_record
from src.logger.logUtil import get_logger
from src.mail.send_mail import send_finished_mail
from src.model.article_detail_manager import create_article_detail_by_section_list
from src.model.article_manager import LectureArticle, get_article_by_id
from src.model.member_manager import get_lecture_member_by_user_id, sub_member_lock_count, sub_member_point
from src.model.oplatform_manager import sub_open_platform_point
from src.model.task_manager import (
    TASK_SOURCE_CHOICES,
    LectureTask,
    TaskVO,
    UpdateStatusVO,
    get_by_biz_id,
    update_recent_task,
)
from src.model.User_action_record_manager import (
    UserActionProcess,
    UserActionType,
    add_user_action_record,
    handle_task_duration_record,
    rollback_user_action_record,
)
from src.model.user_manager import UserPO
from src.services.url_service import UrlService
from src.util.article_utils import insert_img
from src.util.commonutil import CommonResponse, assert_field, common_error, common_success
from src.util.dateUtil import get_current_time_in_beijing, getTimeBySeconds
from src.util.TokenUtil import get_current_user, verify_key

logger = get_logger(__name__)

router = APIRouter(prefix="/api/v3/task", tags=["tasks"])


class Section(BaseModel):
    start_time: float
    end_time: float
    text: str
    modified_text: str
    out_language_modified_text: str
    oss_pic_path: List[str]
    out_language_section_note: str
    section_note: str
    speaker: str = ""


class TaskInputVO(BaseModel):
    sections: List[Section]
    video_lan: str = "zh"
    markdown_lan: str = "zh"
    outline: str
    out_language_outline: str
    total_summary: str
    out_language_total_summary: str
    oss_path: str
    biz_id: str


def verify_tasks(tasks):
    assert_field(tasks, "task cannot be empty")
    assert_field(tasks.sections, "sections cannot be empty")
    assert_field(tasks.biz_id, "task_id cannot be empty")


def change2content(task: TaskInputVO, originTask: LectureTask):
    text = (
        f"# {originTask.name}\n## 视频来源\n\n{originTask.url}\n\n## 大纲\n\n{task.out_language_outline}\n\n"
        f"## 总结\n\n{task.out_language_total_summary}\n\n"
    )
    if not task.sections:
        return
    for section in task.sections:
        time_range = "*" + getTimeBySeconds(section.start_time) + " - " + getTimeBySeconds(section.end_time) + "*\n\n"
        oss_pic_path = insert_img(section.oss_pic_path) + "\n\n" if section.oss_pic_path else ""
        modified_text = section.modified_text + "\n\n" if section.modified_text else ""
        out_language_modified_text = (
            section.out_language_modified_text + "\n\n" if section.out_language_modified_text else ""
        )
        text += time_range + oss_pic_path + modified_text + out_language_modified_text
    return text


@router.post("/create", response_model=CommonResponse, include_in_schema=False)
async def create_task(request: Request, lec: TaskVO, user=Depends(get_current_user)):
    assert lec.url, "The 'url' field of 'TaskVO' is required."
    lec.user_id = user.biz_id

    try:
        # 判断会员
        member = get_lecture_member_by_user_id(user.biz_id)
        if lec.task_source == TASK_SOURCE_CHOICES["upload"]:
            lec.author = user.name
            # 如果是upload任务，就校验是否有上传文件
            # if not member or member.membership_type != MembershipType.VIP:
            #     return common_error("高级VIP用户才能使用上传解析")
            if not lec.oss_key:
                return common_error("请上传文件")
            # 这个时候的url 和 osskey 要换一下
            lec.url = lec.oss_key
        if lec.url_biz_id:
            urls = UrlService.get_url_by_biz_id(lec.url_biz_id)
            if urls:
                lec.img_url = urls.pic_path
                lec.url = urls.origin_url

        # 如果duration_minutes < 0，就直接返回
        if lec.duration_minutes < 0:
            return common_error("视频时长不合法")

        # 如果会员时长不足
        if member.points < lec.duration_minutes:
            return common_error("您剩余使用额度不足以转录当前内容，请充值。")
        task_arr = list(
            LectureTask.select().where(
                LectureTask.url == lec.url,
                fn.json_extract_path_text(LectureTask.ctrl_params, "generate_mode")
                == lec.ctrl_params.get("generate_mode"),
                fn.json_extract_path_text(LectureTask.ctrl_params, "generate_mode")
                == lec.ctrl_params.get("video_style"),
                fn.json_extract_path_text(LectureTask.ctrl_params, "generate_mode") == lec.ctrl_params.get("video_lan"),
                fn.json_extract_path_text(LectureTask.ctrl_params, "generate_mode")
                == lec.ctrl_params.get("markdown_lan"),
                LectureTask.delete_flag == 0,
            )
        )
        if task_arr:
            for idx in task_arr:
                if idx and idx.user_id == lec.user_id:
                    return common_error("任务已经创建过")

        # 判断是否有任务已经完成
        lec.biz_id = str(UUID(int=random.getrandbits(128)))
        lec.token = "".join(random.sample(string.ascii_letters + string.digits, 6))
        task = task_arr[0] if task_arr else None
        if task and task.status and task.status == "finish":
            lec.article_biz_id = task.article_biz_id
            lec.status = "finished"
            lec.process_percent = 100
            lec.ctrl_params = task.ctrl_params
            lec.start_time = get_current_time_in_beijing()
            lec.end_time = get_current_time_in_beijing()
            lec.ctrl_params = task.ctrl_params
            lec.name = task.name
        else:
            lec.start_time = lec.start_time or get_current_time_in_beijing()
            lec.name = lec.name or lec.url

            # 判断没有就插入一条新的， 有就查询出来这条数据
            article = LectureArticle.create(
                delete_flag=0,
                biz_id=str(UUID(int=random.getrandbits(128))),
                name=lec.name,
                article_source=lec.task_source,
                language="zh",
                create_time=lec.start_time,
                update_time=lec.start_time,
                video_url=lec.url,
                img_url=lec.img_url,
                author=lec.author,
            )
            lec.article_biz_id = article.biz_id
        with get_lock(user.biz_id):
            if lec.status and lec.status == "finished":
                lock_count_flag = sub_member_lock_count(user.biz_id)
                if not lock_count_flag:
                    # 如果解锁次数不足，就视频时长的1/2时长，最少为1
                    lec.duration_minutes = max(lec.duration_minutes // 2, 1)
                else:
                    add_user_action_record(
                        user.biz_id, UserActionType.UNLOCK_ARTICLE, UserActionProcess.SUB, "用户解锁文章", 1, lec.biz_id
                    )
                    LectureTask.create(**lec.__dict__)
                    lec.duration_minutes = 0
                    return common_success(data={"token": lec.token})
            if lec.duration_minutes > 0:
                points_sub_flag = sub_member_point(user.biz_id, lec.duration_minutes)
                if not points_sub_flag:
                    return common_error("您剩余使用额度不足以转录当前内容，请充值。")
            add_user_action_record(
                user.biz_id,
                UserActionType.UNLOCK_VIDEO,
                UserActionProcess.SUB,
                "用户转换视频",
                lec.duration_minutes,
                lec.biz_id,
            )
            if member:
                lec.__dict__["order"] = member.membership_type
            arr = list(
                LectureTask.select().where(
                    LectureTask.user_id == user.biz_id,
                    ((LectureTask.status == "confirm") | (LectureTask.status == "processing")),
                    LectureTask.delete_flag == 0,
                )
            )
            if arr and len(arr) >= 3:
                lec.status = "waiting"

            LectureTask.create(**lec.__dict__)
        return common_success(data={"biz_id": lec.biz_id, "duration": lec.duration_minutes})
    except Exception:
        logger.exception("create task error")
        return common_error("创建失败")


@router.post("/finish", response_model=CommonResponse, include_in_schema=False)
async def finish(tasks: TaskInputVO, key: str = Depends(verify_key)):
    """
    任务完成
    :param tasks:
    :param key:
    :return:
    """
    verify_tasks(tasks)

    task = get_by_biz_id(tasks.biz_id)

    if not task:
        return common_error("task not found")

    # 获取文章id
    article = get_article_by_id(task.article_biz_id)

    if not article:
        return common_error("article not found")
    # 保存任务
    article.outline = tasks.outline
    article.out_language = tasks.markdown_lan
    article.language = tasks.video_lan
    article.out_language_outline = tasks.out_language_outline
    article.total_summary = tasks.total_summary
    article.oss_url = tasks.oss_path or "https://aihaoji.com/api/v1/article/download/" + task.biz_id
    article.out_language_total_summary = tasks.out_language_total_summary
    article.content = change2content(tasks, task)
    article.save()

    # 保存到任务详细里
    await create_article_detail_by_section_list(tasks.sections, article.biz_id)

    # 更新任务状态
    task.status = "finish"
    task.process_percent = 100
    task.end_time = get_current_time_in_beijing()
    task.remark = ""
    task.save()

    # 更新最近任务列表
    update_recent_task(task.user_id)

    # 发个邮件通知
    await send_email(task)

    return common_success()


@router.post(
    "/update-section",
    response_model=CommonResponse,
    summary="更新任务状态",
    description="更新任务状态，如果任务完成，需要传递完成时间",
    include_in_schema=False
)
async def update_section(update_status_model: UpdateStatusVO, key: str = Depends(verify_key)):
    if update_status_model.status == "fail":
        # 先写数据库，方便校正
        user_id, point = rollback_user_action_record(
            UserActionType.UNLOCK_VIDEO, update_status_model.biz_id, update_status_model.remark
        )
        # 回滚会员的积分
        if user_id:
            tasks = get_by_biz_id(update_status_model.biz_id)
            if tasks and tasks.open_platform_id:
                sub_open_platform_point(tasks.open_platform_id, -point)
            else:
                sub_member_point(user_id=user_id, point=-point)
    if update_status_model.duration_minutes and update_status_model.duration_minutes > 0:
        # 如果带了时间，需要判断下任务的扣分是否合理
        with get_lock(update_status_model.biz_id):
            # 处理已有任务时长记录
            new_point, user_id = handle_task_duration_record(
                update_status_model.duration_minutes, update_status_model.biz_id
            )
            if not user_id:
                return common_error("积分记录不存在", code=10001)
            if new_point < 0:
                error_response = handle_point_and_record(
                    user_id,
                    update_status_model.biz_id,
                    new_point,
                    UserActionType.UNLOCK_VIDEO,
                    UserActionProcess.ADD.value,
                    "视频时长小于任务时长,返回积分",
                )
                if error_response:
                    return error_response
            elif new_point > 0:
                handle_point_and_record(
                    user_id,
                    update_status_model.biz_id,
                    new_point,
                    UserActionType.UNLOCK_VIDEO,
                    UserActionProcess.SUB.value,
                    "视频时长大于任务时长,扣除积分",
                )

    # 创建一个字典来存储需要更新的字段
    update_fields = update_status_model.dict(exclude_unset=True)
    # 使用 ** 运算符将字典传递给 update 方法
    line_number = LectureTask.update(**update_fields).where(LectureTask.biz_id == update_status_model.biz_id).execute()
    if line_number == 0:
        return common_error("更新失败")
    else:
        return common_success()


async def send_email(task: LectureTask):
    try:
        if task.user_id:
            user = UserPO.get(UserPO.biz_id == task.user_id, UserPO.delete_flag == 0)
            # 邮件通知
            send_finished_mail(user.email, task.name, task.biz_id, user.name)
    except DoesNotExist:
        logger.exception(f"获取用户信息失败，邮件发送失败!,bizId={task.user_id}")
    except Exception:
        logger.exception(f"Error processing user information for biz_id={task}")
