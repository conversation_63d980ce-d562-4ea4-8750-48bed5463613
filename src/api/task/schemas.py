from pydantic import BaseModel


class UrlSchema(BaseModel):
    url: str
    task_source: str
    ctrl_params: dict = {}
    folder_id: int


class CreateTaskSchema(UrlSchema):
    name: str | None = None
    duration_minutes: int = 0
    url_biz_id: str | None = None
    author: str | None = None
    img_url: str | None = None
    oss_key: str | None = None


class BatchCreateTaskSchema(BaseModel):
    """批量创建任务"""
    task_list: list[CreateTaskSchema]


class BatchUrlTaskSchema(BaseModel):
    """不用解析 批量创建任务"""
    task_list: list[UrlSchema]


class AliDataSchema(BaseModel):
    drive_id: str
    file_id: str


class BoxTaskSchema(BaseModel):
    """网盘"""
    ctrl_params: dict = {}
    folder_id: int
    access_token: str
    fs_ids: list[int] = None
    ali_data: list[AliDataSchema] = None
