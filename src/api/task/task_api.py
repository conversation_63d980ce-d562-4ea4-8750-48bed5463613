import io
import json
import random
import threading
import zipfile
from datetime import datetime
from typing import Optional
from uuid import UUID

import pytz
from fastapi import APIRouter, Depends, Request
from peewee import DoesNotExist

from config import settings
from src.core import deps
from src.database import enums
from src.logger.logUtil import get_logger
from src.mail.send_mail import send_finished_mail
from src.model.User_action_record_manager import (
    UserActionProcess,
    UserActionType,
    add_user_action_record,
)
from src.model.article_detail_manager import (
    create_article_detail,
    delete_article_detail_by_id,
    get_article_detail_by_id,
)
from src.model.article_manager import LectureArticle
from src.model.folder import get_folder_by_id, get_folder_dict_by_id
from src.model.member_manager import sub_member_point, verify_user_account
from src.model.task_manager import LectureTask
from src.model.user_manager import UserPO
from src.orm.pgrepo import db
from src.services.lock_points_services import points_revert_source_task, points_expend_source_retry_task
from src.util.TokenUtil import get_current_user
from src.util.commonutil import (
    CommonResponse,
    assert_field,
    common_error,
    common_success,
)
from src.util.commonutil import ErrorCodeEnum, common_enum_error
from src.util.dateUtil import get_current_time_in_beijing
from src.util.redis_keys_util import key_source_task_block

logger = get_logger(__name__)

router = APIRouter(prefix="/api/v1/task", tags=["task"])


@router.get("/list", summary="原文任务列表")
async def get_task_list(
        request: Request,
        page_size: int = 10,
        page_no: int = 1,
        keyword=None,
        user=Depends(get_current_user),
):
    customers = settings.CUSTOMER_SERVICE_SEND.split(",")
    all_flag = customers and user.biz_id in customers
    # 如果keyword 存在，就查询关键字。 如果不存在，就查询所有
    total = (
        LectureTask.select()
        .where(
            all_flag or LectureTask.user_id == user.biz_id,
            (LectureTask.name ** f"%{keyword}%" if keyword is not None and keyword != "" else True),
            LectureTask.delete_flag == 0,
        )
        .count()
    )
    tasks = (
        LectureTask.select()
        .where(
            all_flag or LectureTask.user_id == user.biz_id,
            (LectureTask.name ** f"%{keyword}%" if keyword is not None and keyword != "" else True),
            LectureTask.delete_flag == 0,
        )
        .order_by(-LectureTask.id)
        .paginate(page_no, page_size)
    )
    folder_id_list = []

    for task in tasks:
        task.start_time = task.start_time.strftime("%Y-%m-%d %H:%M:%S")
        task.__data__["task_img"] = task.img_url
        if task.end_time:
            task.end_time = task.end_time.strftime("%Y-%m-%d %H:%M:%S")
        folder_id_list.append(task.folder_id)

    folds_list = get_folder_dict_by_id(folder_id_list, user.biz_id)

    return common_success(
        {
            "tasks": [
                {
                    "folder_name": folds_list.get(task.folder_id) or "root",
                    **task.__data__,
                }
                for task in tasks
                # 这里有些 open 标识的已经删除的要过滤掉
                if task.delete_flag == 0
            ],
            "total": total,
        }
    )


@router.get("/retry/{biz_id}", summary="重新创建一条文章")
async def retry_task(
        request: Request,
        biz_id: str,
        ctrl_params: Optional[dict] = None,
        user=Depends(get_current_user),
        redis_: deps.RedisSessionDep = deps.RedisSessionDep,
        async_db: deps.DBSessionDep = deps.DBSessionDep
):
    """重新创建一条文章"""
    exists = await redis_.sismember(key_source_task_block, user.biz_id)
    if exists:
        return common_enum_error(ErrorCodeEnum.ACCOUNT_ERROR)
    try:
        success, message = await points_expend_source_retry_task(async_db, biz_id, user.biz_id)
        if success:
            return common_success(data=message)
        return common_error(message)
    except Exception:
        logger.exception(f"获取任务失败!,bizId={biz_id},user_id={user.biz_id}")
        return common_error("任务不存在")


@router.get("/verify/upload", summary="上传校验校验")
async def upload_count(user=Depends(get_current_user)):
    """
    上传校验校验
    :param user: 用户信息
    :return: 上传校验结果
    """

    _ = verify_user_account(user.biz_id)

    # if not verify_flag:
    #     return common_error("高级VIP用户才能使用上传解析")

    return common_success()


locks = {}


def get_lock(user_id):
    if user_id not in locks:
        locks[user_id] = threading.Lock()
    return locks[user_id]


@router.get("/items/{biz_id}", response_model=CommonResponse, include_in_schema=False)
async def get(request: Request, biz_id: str, user=Depends(get_current_user)):
    try:
        item = LectureTask.get(
            LectureTask.biz_id == biz_id,
            LectureTask.delete_flag == 0,
            LectureTask.user_id == user.biz_id,
        )
        item.start_time = item.start_time.strftime("%Y-%m-%d %H:%M:%S")
        if item.end_time:
            item.end_time = item.end_time.strftime("%Y-%m-%d %H:%M:%S")
        data = item.__data__
        if (item.status == "finish" or item.status == "finished") and item.article_biz_id:
            try:
                article: LectureArticle = (
                    LectureArticle.select()
                    .where(
                        LectureArticle.biz_id == item.article_biz_id,
                        LectureArticle.delete_flag == 0,
                    )
                    .get()
                )
                data["oss_url"] = article.oss_word_url
            except DoesNotExist:
                pass
            # try:
            #     url_detail = (LectureUrlDetail
            #                   .select()
            #                   .where(LectureUrlDetail.origin_url == item.url,
            #                          LectureArticle.delete_flag == 0).first())
            #     data["url_detail"] = url_detail.__data__
            # except Exception:
            #     pass
        return common_success(data=data)
    except DoesNotExist:
        logger.exception(f"获取任务详细失败!,bizId={biz_id}")
        return common_error("获取失败")


@router.delete("/del/{biz_id}", summary="删除原文任务")
async def delete(request: Request, biz_id: str, user=Depends(get_current_user)):
    assert_field(biz_id, "biz_id is required")
    line_number = (
        LectureTask.update(delete_flag=1)
        .where(LectureTask.biz_id == biz_id, LectureTask.user_id == user.biz_id)
        .execute()
    )

    if line_number == 0:
        return common_error("删除失败")
    else:
        task = LectureTask.select().where(LectureTask.biz_id == biz_id, LectureTask.user_id == user.biz_id).first()
        folder = get_folder_by_id(task.folder_id, user.biz_id)
        folder.total -= 1
        folder.save()
        try:
            arr = list(
                LectureTask.select().where(
                    LectureTask.user_id == user.biz_id,
                    ((LectureTask.status == "confirm") | (LectureTask.status == "processing")),
                    LectureTask.delete_flag == 0,
                )
            )
            if not arr or len(arr) < 1:
                subquery = (
                    LectureTask.select(LectureTask.id)
                    .where(
                        LectureTask.user_id == user.biz_id,
                        LectureTask.delete_flag == 0,
                        LectureTask.status == "waiting",
                    )
                    .limit(1)
                )
                if subquery.exists():
                    (LectureTask.update(status="confirm").where(LectureTask.id.in_(subquery)).execute())
                else:
                    logger.error(f"No tasks found to update. user={user.biz_id}")
        except Exception:
            logger.error(f"更新任务状态失败!,user_id={user.biz_id}")
        # 返还积分
        try:
            if task.status in [
                enums.TaskStatusEnum.WAITING.value,
                enums.TaskStatusEnum.CONFIRM.value
            ]:
                success, message, user_id = await points_revert_source_task(
                    task_id=biz_id, reason="用户删除任务", user_action_type=UserActionType.UNLOCK_VIDEO,
                    delete_flag=1
                )
                if not success:
                    logger.error(f"删除返回积分错误：{message}")
        except Exception:
            logger.exception(f"删除返回积分异常")
        return common_success()


async def handle_content(biz_id: str, oss_path: str, contents: bytes):
    with zipfile.ZipFile(io.BytesIO(contents)) as z:
        # 解压到temp目录
        z.extractall("/tmp/" + biz_id + "/")
        # 读取文件内容
    with open("/tmp/" + biz_id + "/article.md", encoding="utf-8") as f:
        result = f.read()
        # 读取大纲
    with open("/tmp/" + biz_id + "/summary.md", encoding="utf-8") as f:
        outline = f.read()
    with open("/tmp/" + biz_id + "/slides.json", encoding="utf-8") as f:
        # 读取总结
        article_json_arr = f.read()
        article_json_arr = json.loads(article_json_arr)
        # 保存
    # 获取文章信息
    item: LectureTask = LectureTask.select().where(LectureTask.biz_id == biz_id, LectureTask.delete_flag == 0).first()

    try:
        if item.user_id:
            user = UserPO.get(UserPO.biz_id == item.user_id, UserPO.delete_flag == 0)
            # 邮件通知
            send_finished_mail(user.email, item.name, item.biz_id, user.name)
    except DoesNotExist:
        logger.exception(f"获取用户信息失败，邮件发送失败!,bizId={item.user_id}")
    except Exception as e:
        logger.exception(f"Error processing user information for biz_id={biz_id}: {e}")

    # 更新文章信息
    try:
        if item:
            item.end_time = get_current_time_in_beijing()

            # update task by article id
            query = LectureTask.update(status="finish", process_percent=100, end_time=item.end_time, remark="").where(
                LectureTask.article_biz_id == item.article_biz_id,
                LectureTask.delete_flag == 0,
            )
            row_update = query.execute()
            if row_update == 0:
                logger.error(f"更新任务到完成失败!,bizId={biz_id}")
            # 更新文章内容
            LectureArticle.update(content=result, outline=outline, oss_url=oss_path, name=item.name).where(
                LectureArticle.biz_id == item.article_biz_id
            ).execute()
            # 更新文章详细
            if article_json_arr:
                articles = get_article_detail_by_id(item.article_biz_id)
                if articles:
                    delete_article_detail_by_id(item.article_biz_id)
                for article_detail in article_json_arr:
                    article_detail.__setitem__("article_id", item.article_biz_id)
                    create_article_detail(**article_detail)
    except Exception:
        logger.exception(f"更新任务失败!,bizId={biz_id}")
        return False


def get_current_time_in_tz(timezone_str):
    tz = pytz.timezone(timezone_str)
    now_utc = datetime.now(pytz.utc)
    return now_utc.astimezone(tz)
