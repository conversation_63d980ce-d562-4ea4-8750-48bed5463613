import asyncio

from fastapi import APIRouter, Depends
from fastapi_limiter.depends import RateLimiter
from pydantic import BaseModel

from src.core import deps
from src.logger.logUtil import get_logger
from src.model.task_manager import TASK_SOURCE_CHOICES
from src.services.user_service import user_task_batch_limit
from src.util.TokenUtil import get_current_user
from src.util.commonutil import assert_field, ErrorCodeEnum, common_enum_error, common_error, common_success
from src.util.redis_keys_util import key_source_task_block
from src.util.url_handle_v2 import SourceHandler

logger = get_logger(__name__)

router = APIRouter(prefix="/api/v1/url", tags=["task"])


class DetailUrlSchema(BaseModel):
    url: str
    task_source: str


class DictDetailUrlSchema(BaseModel):
    url_list: list[DetailUrlSchema]


@router.get("/detail", dependencies=[Depends(RateLimiter(times=3, seconds=10))], summary="解析url")
async def pre_verify(url: str, task_source: str):
    """
    ### 限流 3次/10s
    """
    assert_field(url, "url is required")
    assert_field(task_source, "task_source is required")

    if task_source and task_source == TASK_SOURCE_CHOICES["upload"]:
        return common_success()

    # 判断有没有人创建过对应任务，如果创建过，就直接提示已经创建过
    detail = SourceHandler.handle_source(url, task_source)

    if not detail:
        return common_error("非法链接", code=400)

    return common_success(detail.__data__)


@router.post("/detail", dependencies=[Depends(RateLimiter(times=2, seconds=20))], summary="解析url-批量")
async def pre_verify_batch(
        request_param: DictDetailUrlSchema,
        user=Depends(get_current_user),
        redis_: deps.RedisSessionDep = deps.RedisSessionDep,
        async_db: deps.DBSessionDep = deps.DBSessionDep
):
    """
    ### 限流 2次/20s
    """
    params = request_param.url_list
    exists = await redis_.sismember(key_source_task_block, user.biz_id)
    if exists:
        return common_enum_error(ErrorCodeEnum.ACCOUNT_ERROR)
    # 过滤重复连接
    repeat_data = set()
    no_repeat_data = list()
    for item in params:
        repeat_key = f'{item.url}::{item.task_source}'
        if repeat_key not in repeat_data:
            no_repeat_data.append(item)
            repeat_data.add(repeat_key)

    if len(params) == 0:
        return common_error("至少一组数据")

    # 批量限制
    batch_count, member = await user_task_batch_limit(async_db, user)

    if batch_count < len(no_repeat_data):
        return common_error(f"超过批量上限 {batch_count}")

    results = await asyncio.gather(
        *[
            SourceHandler.handle_source_list(item.url, item.task_source)
            for item in no_repeat_data
        ],
        return_exceptions=True,
    )
    data = list()
    for item in results:
        data.append({
            "url": item[-1],
            "data": item[0].__data__ if item[0] else None,
            "error": False if item[0] else True,
            "message": "" if item[0] else "非法链接"
        })
    return common_success(data)
