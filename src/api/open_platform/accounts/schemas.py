from typing import Optional, Literal

from pydantic import BaseModel, Field


class SendCaptchaSchema(BaseModel):
    username: str = Field(..., min_length=3, max_length=50)


class PlatformLoginPasswordSchema(SendCaptchaSchema):
    password: str = Field(..., min_length=8)


class PlatformLoginCaptchaSchema(SendCaptchaSchema):
    code: str


class UpdatePasswordSchema(BaseModel):
    username: str
    code: str
    password: Optional[str] = Field(None, min_length=8, title="密码", description="用户的密码")


class PlatformUserSchema(BaseModel):
    name: str
    email: str = None
    mobile: str = None
    mail_cert: bool = False
    password: str
    level: Literal[300, 500, 600, 1000]
    points: int = Field(default=0, ge=0)


class PlatformEnterpriseSchema(PlatformUserSchema):
    head_img: str = None
    enterprise_cert: bool = False
    extra_info: dict = {}


class PlatformKeySchema(BaseModel):
    user_id: str
    name: str = Field(..., description="key的名称")


class PlatformUserPasswordSchema(BaseModel):
    password: str = Field(..., min_length=8)


class PlatformUserPointSchema(BaseModel):
    points: int = Field(..., ge=0)
    action_type: Literal[3, 7, 9]
