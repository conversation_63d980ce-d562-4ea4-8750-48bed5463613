from typing import Literal

from fastapi import APIRouter, Security, Path
from peewee import <PERSON><PERSON><PERSON>, fn

from src.api.open_platform.accounts.schemas import (
    UpdatePasswordSchema
)
from src.core.authorize import get_platform_user
from src.logger.logUtil import get_logger
from src.model.oplatform_manager import OpenPlatform
from src.model.platform_user_manager import (
    PlatformUser
)
from src.model.task_manager import LectureTask
from src.services.platform_user_service import (
    update_user,
    get_platform_user_info,
    platform_level_info,
    platform_user_transaction_records,
    platform_user_summary
)
from src.util.cacheUtil import ttl_cache
from src.util.commonutil import CommonResponse, common_error, common_success, common_enum_error, ErrorCodeEnum
from src.util.redis_keys_util import key_open_captcha
from src.util.stringify import hash_password, is_phone, base64str
from src.util.user_util import is_email

logger = get_logger(__name__)

router = APIRouter(prefix="/my", tags=["我的信息"])


@router.get("/info", response_model=CommonResponse, summary="我的信息")
async def platform_user_info(current_user=Security(get_platform_user, scopes=[])):
    conditions = [
        (PlatformUser.delete_flag == 0),
        PlatformUser.biz_id == current_user.biz_id
    ]
    user = await get_platform_user_info(conditions)
    if not user:
        return common_error("user not exist")
    user_data = user.to_vo()
    user_data.update(await platform_level_info(user.level))
    return common_success(data=user_data)


@router.put("/{category}/password", response_model=CommonResponse, summary="修改个人密码")
async def platform_update_password(
        param: UpdatePasswordSchema,
        category: Literal['mobile', 'email'] = Path(title="验证方式， mobile 手机号， email 邮箱"),
        current_user=Security(get_platform_user, scopes=[])
):
    if category == 'mobile':
        phone = param.username
        if phone is None:
            return common_error("手机号必填")
        if not is_phone(phone):
            return common_enum_error(ErrorCodeEnum.MOBILE_ERROR)
        if current_user.mobile != phone:
            return common_error("手机号与当前用户手机号不匹配")
    elif category == 'email':
        email = param.username
        if email is None:
            return common_error("邮箱必填")
        if not is_email(email):
            return common_enum_error(ErrorCodeEnum.EMAIL_ERROR)
        if current_user.email != email:
            return common_error("验证邮箱与当前用户邮箱不匹配")
    else:
        return common_enum_error(ErrorCodeEnum.METHOD_NOT_FOUND)
    redis_key = key_open_captcha.format(username=base64str(param.username), typename='password')
    cache_code = ttl_cache.get(redis_key)
    if not cache_code:
        return common_error("验证码已过期或未发送")
    if cache_code != param.code:
        return common_error("验证码错误")
    update_data = {
        "password": hash_password(param.password)
    }
    conditions = [
        PlatformUser.biz_id == current_user.biz_id,
        PlatformUser.delete_flag == 0
    ]
    user = await update_user(update_data, conditions, operate_user_id=current_user.biz_id)
    user_data = user.to_vo()
    user_data.update(await platform_level_info(user.level))
    ttl_cache.delete(redis_key)
    return common_success(user.to_vo())


@router.put("/info", response_model=CommonResponse, summary="修改个人信息")
async def platform_update_password(
        param: dict,
        current_user=Security(get_platform_user, scopes=[])
):
    try:
        keys = ["name", "head_img"]
        update_data = dict()
        for key, value in param.items():
            if key in keys:
                update_data[key] = value
        conditions = [
            PlatformUser.biz_id == current_user.biz_id,
            PlatformUser.delete_flag == 0
        ]
        user = await update_user(update_data, conditions, operate_user_id=current_user.biz_id)
        user_data = user.to_vo()
        user_data.update(await platform_level_info(user.level))
        return common_success(user_data)
    except Exception:
        logger.exception("修改个人信息失败")
        return common_error("修改个人信息失败")


@router.get("/secret", summary="我的APPID列表")
async def section_pagination(
        page_size: int = 5,
        page_no: int = 1,
        current_user=Security(get_platform_user, scopes=[])
):
    try:
        last_task = (
            LectureTask.select(
                LectureTask.open_platform_id, fn.Max(LectureTask.start_time).alias('start_time')
            ).group_by(LectureTask.open_platform_id).alias('last_task')
        )
        query = OpenPlatform.select(
            OpenPlatform, last_task.c.start_time.alias('start_time')
        ).join(
            last_task, join_type=JOIN.LEFT_OUTER, on=(last_task.c.open_platform_id == OpenPlatform.biz_id)
        ).where(
            (OpenPlatform.user_id == current_user.biz_id)
            & (OpenPlatform.delete_flag == 0)
        ).switch(OpenPlatform)
        total = query.count()
        secret_list = query.order_by(OpenPlatform.id).paginate(page_no, page_size)
        data = {"content": [], "total": 0}
        for secret in secret_list:
            secret_data = secret.to_vo()
            task_exist = hasattr(secret, 'lecturetask')
            secret_data["start_time"] = secret.lecturetask.start_time if task_exist else None
            data['content'].append(secret_data)
        data['total'] = total
        return common_success(data)
    except Exception:
        logger.exception("获取key列表失败")
        return common_error("获取key列表失败")


@router.get("/transaction/records", summary="我的交易记录列表")
async def transaction_records_pagination(
        page_size: int = 5,
        page_no: int = 1,
        current_user=Security(get_platform_user, scopes=[])
):
    try:
        user_id = current_user.biz_id
        user_points = OpenPlatform.select(OpenPlatform.points).where(
            (OpenPlatform.user_id == user_id)
            &(OpenPlatform.delete_flag == 0)
        ).first()
        if not user_points:
            user_points = PlatformUser.select(
                PlatformUser.points
            ).where(PlatformUser.biz_id == user_id).first()

        data, app_ids = await platform_user_transaction_records(user_id, page_size, page_no)
        summary = await platform_user_summary(user_id, app_ids)
        summary.update({
            'remaining_points': user_points.points
        })
        return common_success({"pagination": data, "summary": summary})
    except Exception:
        logger.exception("获取我的交易记录列表失败")
        return common_error("获取我的交易记录列表失败")
