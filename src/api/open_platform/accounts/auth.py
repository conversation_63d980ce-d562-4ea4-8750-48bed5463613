from typing import Literal

from fastapi import APIRouter, Depends, Path

from src.core.authorize import get_platform_user
from src.core.security_token import create_access_token
from src.logger.logUtil import get_logger
from src.model.platform_user_manager import (
    PlatformUser,
)
from src.mail.send_mail import send_verify_code_mail, send_verify_link_mail
from src.util.cacheUtil import DotDict, ttl_cache, set_cache
from src.util.commonutil import (
    common_enum_error, ErrorCodeEnum,
    CommonResponse, common_error, common_success
)
from src.util.redis_keys_util import key_open_user, key_open_captcha
from src.util.sms_utils import send_sms_by_aliyun
from src.util.stringify import is_email, is_phone, verify_password, random_number, base64str
from src.api.open_platform.accounts.schemas import (
    SendCaptchaSchema,
    PlatformLoginPasswordSchema,
    PlatformLoginCaptchaSchema
)

logger = get_logger(__name__)

router = APIRouter(prefix="/auth", tags=["open api login"])


@router.post("/send/{category}/captcha/{typename}", response_model=CommonResponse, summary="发送验证码")
async def send_captcha(
        user: SendCaptchaSchema,
        category: Literal['mobile', 'email'] = Path(title="验证方式， mobile 手机号， email 邮箱"),
        typename: Literal['login', 'password'] = Path(title="用途， login 登录， password 修改密码"),
):
    """
    发送手机、邮箱验证码
    """
    if category == 'mobile':
        phone = user.username
        if phone is None:
            return common_error("手机号必填")
        if not is_phone(phone):
            return common_enum_error(ErrorCodeEnum.MOBILE_ERROR)

        redis_key = key_open_captcha.format(username=base64str(phone), typename=typename)
        remaining_seconds = ttl_cache.backend.writer_client.ttl(redis_key)
        if remaining_seconds >= 0:
            return common_error(f"短信验证码发送失败: 上个验证码 {remaining_seconds} 秒内有效")
        code = random_number(6)
        try:
            response = send_sms_by_aliyun(phone, code)
            if response and b'"Message":"OK"' in response and b'"Code":"OK"' in response:
                logger.info(f"Sueecessed send {phone} a sms_code: {code}")
            else:
                logger.error(f"Faild send {phone} a sms_code: {code}, reason: {response.decode()}")
                raise Exception(response.decode())
        except Exception as e:
            return common_error(f"短信验证码发送失败: {e}")
        else:
            # 缓存验证码
            set_cache(ttl_cache, redis_key, code, expires=60 * 5)
            return common_success("验证码已发送，请注意查收。5 分钟有效")
    elif category == 'email':
        email = user.username
        if not is_email(email):
            return common_enum_error(ErrorCodeEnum.EMAIL_ERROR)
        redis_key = key_open_captcha.format(username=base64str(email), typename=typename)
        remaining_seconds = ttl_cache.backend.writer_client.ttl(redis_key)
        if remaining_seconds >= 0:
            return common_error(f"邮箱验证码发送失败: 上个验证码 {remaining_seconds} 秒内有效")
        code = random_number(6)
        send_finish = send_verify_code_mail(email, email, code)
        if send_finish:
            # 缓存验证码
            set_cache(ttl_cache, redis_key, code, expires=60 * 5)
            return common_success("验证码已发送，请注意查收。5 分钟有效")
        return common_enum_error(ErrorCodeEnum.METHOD_NOT_FOUND)
    return common_enum_error(ErrorCodeEnum.METHOD_NOT_FOUND)


@router.post("/login", response_model=CommonResponse, summary="用户密码登录")
async def login(user: PlatformLoginPasswordSchema):
    username = user.username
    if username is None or user.password is None:
        return common_error("please provide email and password")

    conditions = [
        (PlatformUser.delete_flag == 0)
    ]
    username_is_email = False
    if is_email(username):
        conditions.append(PlatformUser.email == username)
        username_is_email = True
    elif is_phone(username):
        conditions.append(PlatformUser.mobile == username)
    else:
        return common_error("支持邮箱和手机号登录")

    current_user = PlatformUser.select().where(*conditions).first()
    if not current_user:
        return common_error("user not exist")

    # if username_is_email and not current_user.mail_cert:
    #     return common_error("please verify email first")

    if not verify_password(user.password, current_user.password):
        return common_error("password error")

    access_token, expires_delta = create_access_token(current_user.biz_id)
    ttl_cache.set(key_open_user.format(biz_id=current_user.biz_id), DotDict(current_user.to_vo()))
    return common_success({"jwtToken": access_token, "expires_delta": expires_delta})


@router.post("/login/{category}/captcha", response_model=CommonResponse, summary="验证码登录")
async def login_captcha(
        param: PlatformLoginCaptchaSchema,
        category: Literal['mobile', 'email'] = Path(title="验证方式， mobile 手机号， email 邮箱"),
):
    typename = 'login'
    if param.username is None or param.code is None:
        return common_error("please provide username and code")

    if category == 'mobile':
        phone = param.username
        if phone is None:
            return common_error("手机号必填")
        if not is_phone(phone):
            return common_enum_error(ErrorCodeEnum.MOBILE_ERROR)
        conditions = [
            PlatformUser.mobile == phone,
            PlatformUser.delete_flag == 0
        ]
    elif category == 'email':
        email = param.username
        if email is None:
            return common_error("邮箱必填必填")
        if not is_email(email):
            return common_enum_error(ErrorCodeEnum.EMAIL_ERROR)
        conditions = [
            PlatformUser.email == email,
            PlatformUser.delete_flag == 0
        ]
    else:
        return common_enum_error(ErrorCodeEnum.METHOD_NOT_FOUND)

    redis_key = key_open_captcha.format(username=base64str(param.username), typename=typename)
    cache_code = ttl_cache.get(redis_key)
    if not cache_code:
        return common_error("验证码已过期或未发送")
    if cache_code != param.code:
        return common_error("验证码错误")

    current_user = PlatformUser.select().where(*conditions).first()
    if not current_user:
        return common_enum_error(ErrorCodeEnum.USER_NO_EXIST_OR_LOGGED_OUT)
    access_token, expires_delta = create_access_token(current_user.biz_id)
    ttl_cache.set(key_open_user.format(biz_id=current_user.biz_id), DotDict(current_user.to_vo()))
    ttl_cache.delete(redis_key)
    return common_success({"jwtToken": access_token, "expires_delta": expires_delta})


@router.post("/logout", response_model=CommonResponse, summary="退出登录")
async def logout(user=Depends(get_platform_user)):
    print(user.biz_id)
    print(user)
    return common_success()
