import operator
from functools import reduce
from typing import Literal

from fastapi import APIRouter, Security, Query

from src.api.open_platform.accounts.schemas import (
    PlatformEnterpriseSchema,
    PlatformKeySchema,
    PlatformUserPasswordSchema,
    PlatformUserPointSchema
)
from src.core.authorize import get_platform_user
from src.database import enums
from src.logger.logUtil import get_logger
from src.model.User_action_record_manager import (
    add_user_action_record,
    UserActionProcess,
    UserActionType,
    get_action_name
)
from src.model.oplatform_manager import OpenPlatform
from src.model.platform_user_manager import (
    PlatformUser
)
from src.orm.pgrepo import db
from src.services.action_entity import SchemaSearchEntity, get_filters, get_sorts, delete_flag_filters, FilterEnum
from src.services.platform_user_service import (
    update_user,
    get_platform_user_info,
    platform_level_info
)
from src.util.commonutil import CommonResponse, common_error, common_success, common_enum_error, ErrorCodeEnum
from src.util.stringify import hash_password, generate_secure_random_hex, get_uid

logger = get_logger(__name__)

router = APIRouter(prefix="/user", tags=["用户"])


@router.get("/info/{user_id}", response_model=CommonResponse, summary="用户信息")
async def platform_user_info(
        user_id: str,
        current_user=Security(get_platform_user, scopes=[enums.PlatformLevelEnum.EMPLOYEE.mark])
):
    conditions = [
        (PlatformUser.delete_flag == 0),
        PlatformUser.biz_id == user_id
    ]
    user = await get_platform_user_info(conditions)
    if not user:
        return common_error("user not exist")
    user_data = user.to_vo()
    user_data.update(await platform_level_info(user.level))
    return common_success(data=user_data)


@router.put("/info/{user_id}", response_model=CommonResponse, summary="修改用户信息")
async def platform_user_info_update(
        user_id: str,
        param: dict,
        current_user=Security(get_platform_user, scopes=[enums.PlatformLevelEnum.EMPLOYEE.mark])
):
    try:
        keys = ["name", "head_img", "email", "mobile", "head_img", "extra_info"]
        update_data = dict()
        for key, value in param.items():
            if key in keys:
                update_data[key] = value
        conditions = [
            PlatformUser.biz_id == user_id,
            PlatformUser.delete_flag == 0
        ]
        user = await update_user(update_data, conditions, operate_user_id=current_user.biz_id)
        user_data = user.to_vo()
        user_data.update(await platform_level_info(user.level))
        return common_success(user_data)
    except Exception:
        logger.exception("修改个人信息失败")
        return common_error("修改个人信息失败")


@router.post("/_search", summary="用户列表")
async def section_pagination(
        param: SchemaSearchEntity,
        page_size: int = 5,
        page_no: int = 1,
        category: Literal['internal', 'external'] = Query(default=None,
                                                          description="internal 内部用户，external 外部用户"),
        current_user=Security(get_platform_user, scopes=[enums.PlatformLevelEnum.EMPLOYEE.mark])
):
    try:
        filters = list()
        if param.filters:
            filters = param.filters
        filters.append(delete_flag_filters)

        if category:
            if category == 'internal':
                filters.append(['level', FilterEnum.GE.code, enums.PlatformLevelEnum.EMPLOYEE.code])
            else:
                filters.append(['level', FilterEnum.LT.code, enums.PlatformLevelEnum.EMPLOYEE.code])
        conditions = get_filters(PlatformUser, filters)

        sorts = get_sorts(PlatformUser, param.sorts)
        if not sorts:
            sorts.append(PlatformUser.id)
        query = PlatformUser.select().where(*conditions)
        total = query.count()
        secret_list = query.order_by(*sorts).paginate(page_no, page_size)
        data = {"content": [], "total": 0}
        for secret in secret_list:
            secret_data = secret.to_vo()
            secret_data.update(await platform_level_info(secret.level))
            data['content'].append(secret_data)
        data['total'] = total
        return common_success(data)
    except Exception:
        logger.exception("获取用户列表失败")
        return common_error("获取用户列表失败")


@router.post("/", summary="创建用户")
async def generate_internal_user(
        params: PlatformEnterpriseSchema,
        current_user=Security(get_platform_user, scopes=[enums.PlatformLevelEnum.EMPLOYEE.mark])
):
    try:
        code = enums.PlatformLevelEnum.EMPLOYEE.code

        if params.level >= current_user.level or code > current_user.level:
            return common_enum_error(ErrorCodeEnum.NO_PERMISSION)

        if params.email is None and params.mobile is None:
            return common_error("至少添加邮箱或手机中的一个")

        filters = list()
        if params.email:
            filters.append((PlatformUser.email == params.email))
        if params.mobile:
            filters.append((PlatformUser.mobile == params.mobile))

        or_condition = reduce(operator.or_, filters)
        user = PlatformUser.select().where(or_condition)
        if user:
            return common_enum_error(ErrorCodeEnum.USER_EXIST)

        with db.atomic():
            user = PlatformUser.create(
                biz_id=f'open_user_{get_uid()}',
                name=params.name,
                email=params.email,
                password=hash_password(params.password),
                mobile=params.mobile,
                points=params.points,
                level=params.level,
                enterprise_cert=params.enterprise_cert,
                create_by=current_user.biz_id
            )
            app_secret = None
            app_key = generate_secure_random_hex(10)
            secret_key = generate_secure_random_hex(16)
            if params.level < code:
                app_secret = OpenPlatform.create(
                    name=params.name,
                    user_id=user.biz_id,
                    app_key=app_key,
                    secret_key=secret_key,
                    points=params.points,
                    create_by=current_user.biz_id
                )

            if params.points > 0:
                mark = get_action_name(UserActionType.REGISTER.value)
                add_user_action_record(
                    user.biz_id,
                    UserActionType.REGISTER,
                    UserActionProcess.ADD,
                    mark,
                    params.points
                )
        data = user.to_vo()
        data.update(await platform_level_info(user.level))
        if app_secret:
            data.update({
                "app_key": app_secret.app_key,
                "secret_key": app_secret.secret_key
            })
        return common_success(data)
    except Exception:
        logger.exception("创建平台用户失败")
        return common_error("创建平台用户失败")


@router.post("/secret_key", summary="创建用户key")
async def generate_internal_user_key(
        params: PlatformKeySchema,
        current_user=Security(get_platform_user, scopes=[enums.PlatformLevelEnum.EMPLOYEE.mark])
):
    try:
        if True:
            return common_error("当前只支持一对秘钥")
        code = enums.PlatformLevelEnum.EMPLOYEE.code

        target_user = PlatformUser.select(
            PlatformUser.biz_id,
            PlatformUser.name,
            PlatformUser.level
        ).where(*[
            PlatformUser.biz_id == params.user_id,
            PlatformUser.delete_flag == 0
        ]).first()

        if target_user is None:
            return common_enum_error(ErrorCodeEnum.USER_NO_EXIST_OR_LOGGED_OUT)

        if target_user.level >= code:
            return common_error("内部用户无需创建Key")
        app_key = generate_secure_random_hex(10)
        secret_key = generate_secure_random_hex(16)
        app_secret = OpenPlatform.create(
            name=params.name,
            user_id=target_user.biz_id,
            app_key=app_key,
            secret_key=secret_key,
            create_by=current_user.biz_id
        )

        return common_success(app_secret.to_vo())
    except Exception:
        logger.exception("创建平台用户Key失败")
        return common_error("创建平台用户Key失败")


@router.put("/password/{user_id}", response_model=CommonResponse, summary="修改用户密码")
async def platform_update_password_to_user(
        user_id: str,
        param: PlatformUserPasswordSchema,
        current_user=Security(get_platform_user, scopes=[enums.PlatformLevelEnum.EMPLOYEE.mark])
):
    try:
        conditions = [
            PlatformUser.biz_id == user_id,
            PlatformUser.delete_flag == 0
        ]
        target_user = PlatformUser.select(
            PlatformUser.biz_id,
            PlatformUser.level,
            PlatformUser.password
        ).where(*conditions).first()

        if target_user is None:
            return common_enum_error(ErrorCodeEnum.USER_NO_EXIST_OR_LOGGED_OUT)

        if target_user.level > current_user.level:
            return common_enum_error(ErrorCodeEnum.NO_PERMISSION)

        await update_user(
            {"password": hash_password(param.password)},
            conditions,
            current_user.biz_id
        )
        return common_success()
    except Exception:
        logger.exception("修改用户密码失败")
        return common_error("修改用户密码失败")


@router.post("/recharge/{user_id}", summary="充值积分")
async def generate_internal_user_point(
        user_id: str,
        params: PlatformUserPointSchema,
        current_user=Security(get_platform_user, scopes=[enums.PlatformLevelEnum.EMPLOYEE.mark])
):
    try:
        if True:
            return common_error("充值积分需提交审批申请")
        if params.points > 0:
            with db.atomic():
                rows = (PlatformUser.update(
                    points=PlatformUser.points + params.points
                ).where(*[
                    PlatformUser.biz_id == user_id,
                    PlatformUser.delete_flag == 0
                ]).execute())
                if rows > 0:
                    mark = get_action_name(params.action_type)
                    add_user_action_record(
                        user_id,
                        UserActionType.RECHARGE,
                        UserActionProcess.ADD,
                        mark,
                        params.points
                    )
            return common_success()
        else:
            return common_error("积分为零 未做处理")

    except Exception:
        logger.exception("为用户充值积分失败")
        return common_error("为用户充值积分失败")
