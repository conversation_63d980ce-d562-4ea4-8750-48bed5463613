from src.api.open_platform.accounts import auth, myinfo, users
from src.api.open_platform.approvals import approvals, approvals_request
from src.api.open_platform.static_data import static_data
from src.api.open_platform.SDK import articles, folders


def open_register_blue(app):
    version = '/api/v1'
    app.include_router(static_data.router, prefix=f'{version}')
    app.include_router(auth.router, prefix=f'{version}')
    app.include_router(myinfo.router, prefix=f'{version}')
    app.include_router(users.router, prefix=f'{version}')

    app.include_router(approvals_request.router, prefix=f'{version}')
    app.include_router(approvals.router, prefix=f'{version}')

    app.include_router(folders.router, prefix=f'{version}')
    app.include_router(articles.router, prefix=f'{version}')
