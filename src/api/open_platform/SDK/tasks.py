"""
原文任务
"""
import json

from fastapi import APIRouter, Depends
from fastapi_limiter.depends import RateLimiter
from urllib3.util.url import url_attrs

from src.core import deps, auth_open
from src.logger.logUtil import get_logger, LoggerName
from src.database import constants
from src.services import url_service, redis_service, task_open_service, action_schema
from src.util import stringify
from src.util.commonutil import common_success, CommonResponse, common_enum_error, ErrorCodeEnum
from src.util.redis_keys_util import key_folder_tree
import asyncio

from tests.性能测试数据.原文任务 import url_detail

router = APIRouter(prefix="/task", tags=["原文任务"])

logger = get_logger(LoggerName.openapi)


@router.post("/address/detail", dependencies=[Depends(RateLimiter(times=2, seconds=30))], summary="url地址解析-批量")
async def url_batch(
        param: url_service.BatchDetailUrlSchema,
        user: auth_open.OpenUser = Depends(auth_open.get_open_user),
        async_db: deps.DBSessionDep = deps.DBSessionDep,
        redis_: deps.RedisSessionDep = deps.RedisSessionDep
):
    """url地址解析-批量"""
    error = await redis_service.user_black_list(redis_, user.biz_id)
    if isinstance(error, CommonResponse):
        return error

    url_list = url_service.repeat_url(param.url_list)
    if len(url_list) == 0:
        return common_enum_error(ErrorCodeEnum.PARAM_BATCH_NOT_DATA)

    if len(url_list) > constants.OPEN_BATCH_MAX_COUNT:
        return common_enum_error(ErrorCodeEnum.BATCH_TOP_LIMIT, formats=[f"{constants.OPEN_BATCH_MAX_COUNT}"])

    results = await asyncio.gather(
        *[
            url_service.create_url_analysis(redis_, async_db, item.url, item.task_source)
            for item in url_list
        ],
        return_exceptions=True,
    )
    response_data, model_data_list = list(), list()
    for item in results:
        model_data = item[0]
        url = item[-1]
        if isinstance(model_data, CommonResponse):
            response_data.append({
                "url": url,
                "data": model_data.data,
                "error": True,
                "message": model_data.message
            })
        else:
            model_data_list.append(model_data)
            response_data.append({
                "url": url,
                "data": model_data.data,
                "error": False,
                "message": model_data.to_dict()
            })
    if len(model_data_list) == len(url_list):
        return common_success(response_data)
    elif 0 < len(model_data_list) < len(url_list):
        return common_enum_error(ErrorCodeEnum.BATCH_PART_ERROR, data=response_data)
    else:
        return common_enum_error(ErrorCodeEnum.BATCH_ALL_ERROR, data=response_data)


# @router.post("/batch",
#              response_model=CommonResponse,
#              dependencies=[Depends(RateLimiter(times=2, seconds=30))],
#              summary="创建笔记-批量")
# async def view_create_task_batch(
#         param: action_schema.BatchCreateTaskSchema,
#         user: auth_open.OpenUser = Depends(auth_open.get_open_user),
#         redis_: deps.RedisSessionDep = deps.RedisSessionDep,
#         async_db: deps.DBSessionDep = deps.DBSessionDep
# ):
#     error = await redis_service.user_black_list(redis_, user.biz_id)
#     if isinstance(error, CommonResponse):
#         return error
#     # 过滤重复连接
#     repeat_data = url_service.repeat_url(param.task_list)
#
#     if not no_repeat_data:
#         return common_error("至少一条任务")
#
#     submit_param = no_repeat_data[0]
#     folder = await tasks_service.async_get_folder_by_id(async_db, user, submit_param.folder_id)
#     if not folder:
#         return common_error("文件夹不存在")
#
#     # 批量限制
#     batch_count, member = await user_task_batch_limit(async_db, user)
#
#     if batch_count < len(no_repeat_data):
#         return common_error(f"超过批量上限 {batch_count}")
#
#     # 当天限制
#     success, message = await user_task_day_limit(async_db, user, no_repeat_data, member)
#     if not success:
#         return common_error(message)
#
#     success, message = await tasks_service.create_task_batch(async_db, user, no_repeat_data, folder)
#     if not success:
#         return common_error(message)
#     return common_success(data=message)
