"""
笔记本
"""
import json

from fastapi import APIRouter, Depends

from src.core import deps, auth_open
from src.database import models
from src.logger.logUtil import get_logger, LoggerName
from src.services import folder_service, action_entity, tasks_service
from src.util import stringify
from src.util.commonutil import common_success, CommonResponse
from src.util.redis_keys_util import key_folder_tree

router = APIRouter(prefix="/folder", tags=["笔记本"])

logger = get_logger(LoggerName.client)


@router.get("/", summary="笔记本树--侧边栏")
async def open_tree_folder(
        parent_id: int = None,
        flush: str = None,
        user: auth_open.OpenUser = Depends(auth_open.get_open_user),
        async_db: deps.DBSessionDep = deps.DBSessionDep,
        redis_: deps.RedisSessionDep = deps.RedisSessionDep
):
    """笔记本树--侧边栏"""
    key = key_folder_tree.format(user_id=user.biz_id)
    data = await redis_.get(key)
    if data and stringify.is_empty(flush):
        return common_success(data=json.loads(data))
    folder_tree = await folder_service.get_recursion(async_db, user.biz_id, parent_id)
    await redis_.setex(key, 60 * 60 * 24, stringify.from_json(folder_tree))
    return common_success(data=folder_tree)


@router.post("/", summary="创建笔记本")
async def open_create_folder(
        param: folder_service.GenerateFolderSchema,
        user: auth_open.OpenUser = Depends(auth_open.get_open_user),
        async_db: deps.DBSessionDep = deps.DBSessionDep,
        redis_: deps.RedisSessionDep = deps.RedisSessionDep
):
    """创建笔记本"""
    key = key_folder_tree.format(user_id=user.biz_id)
    folder = await folder_service.create_folder(async_db, param, user.biz_id)
    if isinstance(folder, CommonResponse):
        return folder
    await redis_.delete(key)
    return common_success(data=folder)


@router.put("/{folder_id}", summary="重命名")
async def open_rename_folder(
        folder_id: int,
        param: folder_service.NameSchema,
        user: auth_open.OpenUser = Depends(auth_open.get_open_user),
        async_db: deps.DBSessionDep = deps.DBSessionDep,
        redis_: deps.RedisSessionDep = deps.RedisSessionDep
):
    """重命名笔记本"""
    key = key_folder_tree.format(user_id=user.biz_id)
    folder = await folder_service.rename_folder(async_db, folder_id, user.biz_id, param.name)
    if isinstance(folder, CommonResponse):
        return folder
    await redis_.delete(key)
    return common_success(data=folder)


@router.delete("/{folder_id}", summary="删除笔记本")
async def open_rename_folder(
        folder_id: int,
        user: auth_open.OpenUser = Depends(auth_open.get_open_user),
        async_db: deps.DBSessionDep = deps.DBSessionDep,
        redis_: deps.RedisSessionDep = deps.RedisSessionDep
):
    """删除笔记本"""
    key = key_folder_tree.format(user_id=user.biz_id)
    folder = await folder_service.remove_folder(async_db, folder_id, user.biz_id)
    if isinstance(folder, CommonResponse):
        return folder
    await redis_.delete(key)
    return common_success(data=folder)


@router.post("/content", summary="笔记本下的任务列表")
async def open_create_folder(
        param: tasks_service.SearchTaskFolderSchema,
        flush: bool = False,
        user: auth_open.OpenUser = Depends(auth_open.get_open_user),
        async_db: deps.DBSessionDep = deps.DBSessionDep,
        redis_: deps.RedisSessionDep = deps.RedisSessionDep
):
    """笔记本下的任务列表"""

    data = await tasks_service.search_task_content(
        redis_,
        async_db,
        param,
        user.biz_id,
        user.keys,
        flush=flush
    )
    if isinstance(data, CommonResponse):
        return data
    return common_success(data=data)


@router.get("/first_page", summary="笔记本首页")
async def open_create_folder(
        page_no: int = 1,
        page_size: int = 10,
        keyword: str = None,
        flush: bool = False,
        user: auth_open.OpenUser = Depends(auth_open.get_open_user),
        async_db: deps.DBSessionDep = deps.DBSessionDep,
        redis_: deps.RedisSessionDep = deps.RedisSessionDep
):
    """笔记本首页"""

    data = await folder_service.search_first_page_mobile(
        redis_,
        async_db,
        user.biz_id,
        user.keys,
        page_no, page_size, keyword, flush)
    if isinstance(data, CommonResponse):
        return data
    return common_success(data=data)
