"""
文章
"""

from fastapi import APIRouter, Depends, Path, Query

from src.core import deps, auth_open
from src.database import myredis
from src.logger.logUtil import get_logger
from src.services import article_service
from src.util import stringify, redis_keys_util
from src.util.commonutil import (
    common_error, common_success, common_enum_error, ErrorCodeEnum, CommonResponse
)

logger = get_logger(__name__)

router = APIRouter(prefix="/article", tags=["文章"])


@router.get("/{task_biz_id}", summary="文章内容列表")
async def section_read(
        task_biz_id: str,
        page_size: int = 5,
        page_no: int = 1,
        flush: bool = False,
        start_time_str=None,
        end_time_str=None,
        user: auth_open.OpenUser = Depends(auth_open.get_open_user),
        redis_: deps.RedisSessionDep = deps.RedisSessionDep,
        async_db: deps.DBSessionDep = deps.DBSessionDep
):
    # 文章内容列表
    try:
        task = await article_service.can_access_article_cache(async_db, redis_, user.biz_id, task_biz_id,
                                                              keys_ids=user.keys, all_flag=user.all_flag, flush=flush)
        if not task:
            return common_enum_error(ErrorCodeEnum.NO_PERMISSION)
        article = task.article
        if not article:
            return common_enum_error(ErrorCodeEnum.ARTICLE_NOT_EXISTS)

        data = await article_service.search_article_details(
            async_db, redis_, user.biz_id, task_biz_id, article, page_no, page_size, start_time_str, end_time_str,
            flush=flush
        )
        return common_success(data)
    except Exception as ex:
        logger.exception(f"获取文章详细失败，biz_id={task_biz_id}\n{stringify.from_exception(ex)}")
        return common_error("获取详细失败")


@router.get("/meta/{task_biz_id}", summary="文章meta内容-左侧")
async def section_read(
        task_biz_id: str,
        flush: bool = False,
        user: auth_open.OpenUser = Depends(auth_open.get_open_user),
        redis_: deps.RedisSessionDep = deps.RedisSessionDep,
        async_db: deps.DBSessionDep = deps.DBSessionDep
):
    # 文章meta内容
    try:
        data = await article_service.get_meta_article(async_db, redis_, user.biz_id, task_biz_id,
                                                      keys_ids=user.keys, all_flag=user.all_flag, flush=flush)
        if isinstance(data, CommonResponse):
            return data
        return common_success(data)
    except Exception as ex:
        logger.exception(f"获取文章meta内容失败，biz_id={task_biz_id}\n{stringify.from_exception(ex)}")
        return common_error("获取文章meta内容失败")


@router.post('/record', summary='创建划线记录')
async def create_record(
        req: article_service.UnderlineSchema,
        user: auth_open.OpenUser = Depends(auth_open.get_open_user),
        async_db: deps.DBSessionDep = deps.DBSessionDep,
        redis_: deps.RedisSessionDep = deps.RedisSessionDep
):
    try:
        record_id = await article_service.create_record_and_detail(async_db, req, user.biz_id)
        if isinstance(record_id, CommonResponse):
            return record_id
        article_data = await article_service.get_record_by_detail_id(async_db, req.biz_id)

        await myredis.clear_redis_cache(redis_, redis_keys_util.key_article_details_all.format(user_id=user.biz_id))
        return common_success(article_data)
    except Exception as ex:
        logger.exception(f"创建划线记录失败，detail_id={req.biz_id}\n{stringify.from_exception(ex)}")
        return common_error("创建划线记录失败")


@router.put('/record', summary='更新划线记录')
async def update_record(
        req: article_service.UpdateUnderlineSchema,
        user: auth_open.OpenUser = Depends(auth_open.get_open_user),
        async_db: deps.DBSessionDep = deps.DBSessionDep,
        redis_: deps.RedisSessionDep = deps.RedisSessionDep
):
    try:
        article_detail_id = await article_service.update_record_and_detail(async_db, req, user.biz_id)
        if isinstance(article_detail_id, CommonResponse):
            return article_detail_id
        article_data = await article_service.get_record_by_detail_id(async_db, article_detail_id)

        await myredis.clear_redis_cache(redis_, redis_keys_util.key_article_details_all.format(user_id=user.biz_id))
        return common_success(article_data)
    except Exception as ex:
        logger.exception(f"更新划线记录，record_id={req.record_id}\n{stringify.from_exception(ex)}")
        return common_error("更新划线记录失败")


@router.delete('/record/{record_id}/{context_id}', summary='删除划线记录')
async def delete_record(
        record_id: str = Path(..., description="记录Id"),
        context_id: str = Path(..., description="记录的每一条数据对应的 Id 后台生成的唯一值"),
        language: str = Query('zh', description="当前内容的语言类型, 影响返回结果对应的数据"),
        user: auth_open.OpenUser = Depends(auth_open.get_open_user),
        async_db: deps.DBSessionDep = deps.DBSessionDep,
        redis_: deps.RedisSessionDep = deps.RedisSessionDep
):
    try:
        article_detail_id = await article_service.delete_record_and_detail(async_db, record_id, context_id, user.biz_id)
        if isinstance(article_detail_id, CommonResponse):
            return article_detail_id
        article_data = await article_service.get_record_by_detail_id(async_db, article_detail_id)

        await myredis.clear_redis_cache(redis_, redis_keys_util.key_article_details_all.format(user_id=user.biz_id))
        return common_success(article_data)
    except Exception as ex:
        logger.exception(f"删除划线记录，record_id={record_id}context_id={context_id}\n{stringify.from_exception(ex)}")
        return common_error("删除划线记录失败")


@router.put('/hi-light', summary='高亮当前选择的文本内容')
async def delete_record(
        req: article_service.HighLightSchema,
        user: auth_open.OpenUser = Depends(auth_open.get_open_user),
        async_db: deps.DBSessionDep = deps.DBSessionDep,
        redis_: deps.RedisSessionDep = deps.RedisSessionDep
):
    try:
        article_detail_id = await article_service.update_hi_part_detail(async_db, req, user.biz_id, user.keys)
        if isinstance(article_detail_id, CommonResponse):
            return article_detail_id
        article_data = await article_service.get_record_by_detail_id(async_db, article_detail_id)

        await myredis.clear_redis_cache(redis_, redis_keys_util.key_article_details_all.format(user_id=user.biz_id))
        return common_success(article_data)
    except Exception as ex:
        logger.exception(f"高亮当前选择的文本内容，detail_id={req.biz_id}\n{stringify.from_exception(ex)}")
        return common_error("高亮当前选择的文本内容失败")


@router.put('/correct/part', summary='局部纠错')
async def delete_record(
        req: article_service.HighLightSchema,
        user: auth_open.OpenUser = Depends(auth_open.get_open_user),
        async_db: deps.DBSessionDep = deps.DBSessionDep,
        redis_: deps.RedisSessionDep = deps.RedisSessionDep
):
    try:
        article_detail_id = await article_service.update_hi_part_detail(async_db, req, user.biz_id, user.keys)
        if isinstance(article_detail_id, CommonResponse):
            return article_detail_id
        article_data = await article_service.get_record_by_detail_id(async_db, article_detail_id)

        await myredis.clear_redis_cache(redis_, redis_keys_util.key_article_details_all.format(user_id=user.biz_id))
        return common_success(article_data)
    except Exception as ex:
        logger.exception(f"局部纠错，detail_id={req.biz_id}\n{stringify.from_exception(ex)}")
        return common_error("局部纠错失败")


@router.post('/correct/all/count', summary='全文纠错出现次数')
async def delete_record(
        req: article_service.CorrectAllSchema,
        user: auth_open.OpenUser = Depends(auth_open.get_open_user),
        async_db: deps.DBSessionDep = deps.DBSessionDep,
        redis_: deps.RedisSessionDep = deps.RedisSessionDep
):
    try:
        response = await article_service.correct_all_count(async_db, redis_, req, user.biz_id, user.keys,
                                                           user.all_flag)
        if isinstance(response, CommonResponse):
            return response
        return common_success(response)
    except Exception as ex:
        logger.exception(f"全文纠错出现次数，detail_id={req.biz_id}\n{stringify.from_exception(ex)}")
        return common_error("全文纠错出现次数")


@router.put('/correct/all', summary='全文纠错')
async def delete_record(
        req: article_service.UpdateCorrectAllSchema,
        user: auth_open.OpenUser = Depends(auth_open.get_open_user),
        async_db: deps.DBSessionDep = deps.DBSessionDep,
        redis_: deps.RedisSessionDep = deps.RedisSessionDep
):
    try:
        response = await article_service.correct_all_count_update(async_db, redis_, req, user.biz_id, user.keys,
                                                                  user.all_flag)
        if isinstance(response, CommonResponse):
            return response
        redis_keys_util.key_task_id.format(user_id=user.biz_id, task_id=req.biz_id)
        await myredis.clear_redis_cache(redis_, redis_keys_util.key_article_details_all.format(user_id=user.biz_id))
        await redis_.delete(redis_keys_util.key_task_id.format(user_id=user.biz_id, task_id=req.biz_id))
        return common_success(response)
    except Exception as ex:
        logger.exception(f"全文纠错，detail_id={req.biz_id}\n{stringify.from_exception(ex)}")
        return common_error("全文纠错失败")
