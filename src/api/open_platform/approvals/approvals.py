"""
审批
"""
from typing import Literal

from fastapi import APIRouter, Security, Path
from sqlalchemy.orm import joinedload, load_only
from src.api.open_platform.approvals import schemas
from src.core import deps
from src.core.authorize import get_platform_user
from src.database import enums, models
from src.logger.logUtil import get_logger
from src.services import approval_service, lock_points_services, action_entity
from src.util.commonutil import CommonResponse, common_success, common_error, common_enum_error, ErrorCodeEnum
from src.util import stringify

logger = get_logger(__name__)

router = APIRouter(prefix="/approvals", tags=["审批"])


@router.post("/_search", response_model=CommonResponse, summary="我的审批列表")
async def submit_approvals_point(
        param: action_entity.SchemaSearchEntity,
        page_no: int = 1,
        page_size: int = 5,
        async_db: deps.DBSessionDep = deps.DBSessionDep,
        current_user=Security(get_platform_user, scopes=[enums.PlatformLevelEnum.TREASURER.mark])
):

    conditions, sorts = action_entity.set_filters_sorts(models.ApprovalProcessModel, param)
    conditions.append(models.ApprovalProcessModel.approver_id==current_user.biz_id)
    data = await models.ApprovalProcessModel.search_model_paginate(
        async_db,
        page_size=page_size,
        current_page=page_no,
        filters=conditions,
        joins=[
            joinedload(models.ApprovalProcessModel.request).options(
                joinedload(models.ApprovalRequestModel.user_requester).options(
                    load_only(*[
                        models.PlatformUserModel.name,
                        models.PlatformUserModel.mobile,
                        models.PlatformUserModel.email,
                        models.PlatformUserModel.biz_id
                    ])
                )
            )
        ],
        sorts=sorts,
        serialize=True
    )
    return common_success(data=data)


@router.put("/points/{status}", response_model=CommonResponse, summary="充值积分审批")
async def approve_process_point(
        param: schemas.ProcessSchema,
        status: Literal['approved', 'rejected'] = Path(description="approved 通过，rejected 拒绝"),
        async_db: deps.DBSessionDep = deps.DBSessionDep,
        current_user=Security(get_platform_user, scopes=[enums.PlatformLevelEnum.TREASURER.mark])
):
    try:
        if status == 'approved':
            request = await approval_service.approve_process(
                async_db, param.process_id, current_user.biz_id, param.comment
            )
            content = request.content or {}
            user_id = content.get("user_id")
            points = int(content.get("point", 0)) + int(content.get("gift_point", 0))
            await lock_points_services.open_point_increase(
                async_db, user_id, request.biz_id, points, content.get("remark")
            )
            request = await request.serialize_data(request)
            return common_success(request)
        elif status == 'rejected':
            request = await approval_service.reject_process(
                async_db, param.process_id, current_user.biz_id, param.comment
            )
            request = await request.serialize_data(request)
            return common_success(request)
        else:
            return common_enum_error(ErrorCodeEnum.METHOD_NOT_FOUND)
    except ValueError as ex:
        return common_error(str(ex))
    except Exception:
        logger.exception("审批失败")
        return common_error("审批失败")