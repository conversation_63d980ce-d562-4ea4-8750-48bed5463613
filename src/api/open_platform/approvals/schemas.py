from email.policy import default
from typing import Optional, Literal

from pydantic import BaseModel, Field


class PointApprovalSchema(BaseModel):
    point: int = 0
    gift_point: int = 0
    user_id: str
    remark: str = None
    extra_info: dict = {}


class SubmitPointApprovalSchema(BaseModel):
    """创建积分"""
    title: str = Field(..., max_length=50, description="审批标题")
    content: PointApprovalSchema


class ProcessSchema(BaseModel):
    """审核"""
    process_id: str = Field(..., description="审核id")
    comment: str = Field(default=None, description="审核备注")
