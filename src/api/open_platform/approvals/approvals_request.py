"""
审批请求
"""

from fastapi import APIRouter, Security
from sqlalchemy.orm import joinedload, load_only

from src.api.open_platform.approvals import schemas
from src.core import deps
from src.core.authorize import get_platform_user
from src.database import enums, models
from src.logger.logUtil import get_logger
from src.services import approval_service, action_entity
from src.util.commonutil import CommonResponse, common_success, common_error

logger = get_logger(__name__)

router = APIRouter(prefix="/approvals_request", tags=["审批请求"])


@router.post("/_search", response_model=CommonResponse, summary="我提交的审批列表")
async def submit_approvals_point(
        param: action_entity.SchemaSearchEntity,
        page_no: int = 1,
        page_size: int = 5,
        async_db: deps.DBSessionDep = deps.DBSessionDep,
        current_user=Security(get_platform_user, scopes=[enums.PlatformLevelEnum.EMPLOYEE.mark])
):
    conditions, sorts = action_entity.set_filters_sorts(models.ApprovalRequestModel, param)
    conditions.append(models.ApprovalRequestModel.requester_id == current_user.biz_id)
    data = await models.ApprovalRequestModel.search_model_paginate(
        async_db,
        page_size=page_size,
        current_page=page_no,
        filters=conditions,
        joins=[
            joinedload(models.ApprovalRequestModel.processes).options(
                joinedload(models.ApprovalProcessModel.user_approver).options(
                    load_only(*[
                        models.PlatformUserModel.name,
                        models.PlatformUserModel.mobile,
                        models.PlatformUserModel.email,
                        models.PlatformUserModel.biz_id
                    ])
                )
            )
        ],
        sorts=sorts,
        serialize=True
    )
    return common_success(data=data)


@router.post("/points", response_model=CommonResponse, summary="提交充值积分审批")
async def submit_approvals_point(
        param: schemas.SubmitPointApprovalSchema,
        async_db: deps.DBSessionDep = deps.DBSessionDep,
        current_user=Security(get_platform_user, scopes=[enums.PlatformLevelEnum.EMPLOYEE.mark])
):
    request_data = param.__dict__
    request_data["content"] = param.content.__dict__
    request_data["user_id"] = current_user.biz_id
    request = await approval_service.create_request(async_db, request_type="action_open_pay_point",
                                                    request_data=request_data)
    if not request:
        pass
    return common_success(data=request.to_dict())


@router.put("/points/{biz_id}", response_model=CommonResponse, summary="取消申请")
async def submit_approvals_point(
        biz_id: str,
        async_db: deps.DBSessionDep = deps.DBSessionDep,
        current_user=Security(get_platform_user, scopes=[enums.PlatformLevelEnum.EMPLOYEE.mark])
):
    try:
        request = await approval_service.cancel_request(async_db, biz_id, current_user.biz_id)
        return common_success(data=request)
    except ValueError as ex:
        return common_error(str(ex))
    except Exception:
        logger.exception("取消审批申请失败")
        return common_error("取消审批申请失败")
