from fastapi import APIRouter

from src.logger.logUtil import get_logger
from src.services.action_entity import get_filters_conditions
from src.util.commonutil import CommonResponse, common_success

logger = get_logger(__name__)

router = APIRouter(prefix="/constant", tags=["常数内容"])


@router.get("/filters", response_model=CommonResponse, summary="筛选条件")
async def platform_user_info():
    data = get_filters_conditions()
    return common_success(data=data)
