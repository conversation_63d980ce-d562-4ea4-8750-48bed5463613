"""
产品vip
"""

from fastapi import APIRouter, Depends

from src.logger.logUtil import get_logger
from src.model.user_manager import UserPO
from src.util.TokenUtil import get_current_user
from src.util.commonutil import common_success

router = APIRouter(prefix="/plan", tags=["产品vip"])
logger = get_logger(__name__)


@router.get("/", summary="列表/详情")
async def block_user_add(
        user: UserPO = Depends(get_current_user),
):
    """"""

    return common_success()

