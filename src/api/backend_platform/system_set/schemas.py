from typing import Literal

from pydantic import BaseModel, Field

from src.services import approval_service


class ApprovalConfigSchema(BaseModel):
    request_type: str
    name: str
    is_active: bool
    steps: list[approval_service.StepData]


class AppReleaseSchema(BaseModel):
    platform: Literal['android', 'ios'] = Field(..., description="'android' 或 'ios'")
    version_name: str = Field(..., max_length=50, description="版本号，如 '1.2.3'")
    version_code: int = Field(..., description="版本号，如 123")
    is_force_update: bool = Field(default=False, description="是否强制更新")
    content: str = Field(description="更新内容（必选）")
    download_url: str = Field(..., max_length=300, description="是否强制更新")
    is_active: bool = Field(default=True, description="是否启用")
