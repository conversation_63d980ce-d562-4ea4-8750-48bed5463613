"""
系统配置
"""

from fastapi import APIRouter, Security

from src.api.backend_platform.system_set import schemas
from src.core import authorize
from src.core import deps
from src.logger.logUtil import get_logger, LoggerName
from src.model.lecture_ai import AITYPE
from src.model.user_manager import UserPO
from src.services import user_service, approval_service
from src.util.commonutil import common_success, common_enum_error, ErrorCodeEnum
from src.util.redis_keys_util import key_ai_task_limit

router = APIRouter(prefix="/approval", tags=["系统配置"])

logger = get_logger(LoggerName.backend)


@router.get("/", summary="审批配置列表")
async def block_user_delete(
        page_on:int = 1,
        page_size:int = 5,
        user: UserPO = Security(authorize.get_backend_user, scopes=[]),
        async_db: deps.DBSessionDep = deps.DBSessionDep
):
    """从黑名单中删除"""
    data = await approval_service.query_approval_config(async_db, page_on, page_size)

    return common_success(data=data)


@router.post("/", summary="审批配置")
async def block_user_delete(
        param: schemas.ApprovalConfigSchema,
        user: UserPO = Security(authorize.get_backend_user, scopes=[]),
        async_db: deps.DBSessionDep = deps.DBSessionDep,
        redis_: deps.RedisSessionDep = deps.RedisSessionDep
):
    """从黑名单中删除"""
    return common_success(data={})
