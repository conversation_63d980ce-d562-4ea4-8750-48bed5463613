"""
app 配置
"""
import json
from typing import Literal

from fastapi import APIRouter, Security, Path
from sqlalchemy import update
from asyncpg.exceptions import UniqueViolationError
from src.api.backend_platform.system_set import schemas
from src.core import deps, authorize
from src.database import models
from src.logger.logUtil import get_logger, LoggerName
from src.model.user_manager import UserPO
from src.services import action_entity
from src.util import dateUtil, stringify
from src.util.commonutil import common_success, common_enum_error, ErrorCodeEnum, common_error
from src.util.redis_keys_util import key_app_lastest
from sqlalchemy.exc import IntegrityError
router = APIRouter(prefix="/release", tags=["系统配置"])

logger = get_logger(LoggerName.backend)


@router.post("/", summary="创建发布版本")
async def release_create(
        param: schemas.AppReleaseSchema,
        user: UserPO = Security(authorize.get_backend_user, scopes=[]),
        async_db: deps.DBSessionDep = deps.DBSessionDep,
        redis_: deps.RedisSessionDep = deps.RedisSessionDep
):
    """创建发布版本"""
    try:
        key = key_app_lastest.format(platform=param.platform)
        param_data = param.__dict__
        param_data["create_by"] = user.biz_id

        if param.is_active:
            await async_db.execute(
                update(models.AppVersionModel).filter(
                    models.AppVersionModel.platform == param.platform,
                    models.AppVersionModel.is_active.is_(True),
                    models.AppVersionModel.delete_flag == 0
                ).values(
                    is_active=False,
                    update_time=dateUtil.get_current_time_in_beijing(),
                    update_by=user.biz_id
                )
            )

        data = await models.AppVersionModel.generate_model(async_db, data=param_data)
        data = await data.serialize_data(data)
        await redis_.delete(key)
        return common_success(data=data)
    except UniqueViolationError:
        return common_error("数据已存在")
    except IntegrityError:
        return common_error("数据已存在")

@router.get("/lastest/{platform}", summary="最新版本")
async def app_release_list(
        platform: Literal['android', 'ios'] = Path(..., description="'android' 或 'ios'"),
        user: UserPO = Security(authorize.get_backend_user, scopes=[]),
        async_db: deps.DBSessionDep = deps.DBSessionDep,
        redis_: deps.RedisSessionDep = deps.RedisSessionDep
):
    """最新版本"""
    key = key_app_lastest.format(platform=platform)
    data = await redis_.get(key)
    if data:
        return common_success(data=json.loads(data))
    data = await models.AppVersionModel.query_model(
        async_db,
        fields=[
            models.AppVersionModel.platform,
            models.AppVersionModel.version_name,
            models.AppVersionModel.version_code,
            models.AppVersionModel.is_force_update,
            models.AppVersionModel.content,
            models.AppVersionModel.download_url,
            models.AppVersionModel.is_active,
        ],
        filters=[
            models.AppVersionModel.platform == platform,
            models.AppVersionModel.is_active.is_(True),
            models.AppVersionModel.delete_flag == 0
        ],
        serialize=True
    )
    if data:
        await redis_.setex(key, 60 * 60 * 24, stringify.from_json(data))
    return common_success(data=data)


@router.post("/_search", summary="版本列表")
async def app_release_list(
        param: action_entity.SchemaSearchEntity,
        page_no: int = 1,
        page_size: int = 5,
        user: UserPO = Security(authorize.get_backend_user, scopes=[]),
        async_db: deps.DBSessionDep = deps.DBSessionDep
):
    """版本列表"""
    conditions, sorts = action_entity.set_filters_sorts(models.AppVersionModel, param)
    data = await models.AppVersionModel.search_model_paginate(
        async_db,
        page_size=page_size,
        current_page=page_no,
        filters=conditions,
        sorts=sorts,
        serialize=True
    )
    return common_success(data=data)


@router.put("/{app_id}", summary="修改单条数据")
async def update_app_release_list(
        app_id: int,
        param: schemas.AppReleaseSchema,
        user: UserPO = Security(authorize.get_backend_user, scopes=[]),
        async_db: deps.DBSessionDep = deps.DBSessionDep,
        redis_: deps.RedisSessionDep = deps.RedisSessionDep
):
    """修改单条数据"""
    key = key_app_lastest.format(platform=param.platform)
    data = await models.AppVersionModel.query_model(
        async_db,
        filters=[
            models.AppVersionModel.id == app_id
        ]
    )
    if data is None:
        return common_enum_error(ErrorCodeEnum.NOT_FOUND)
    if param.is_active:
        await async_db.execute(
            update(models.AppVersionModel).filter(
                models.AppVersionModel.platform == param.platform,
                models.AppVersionModel.is_active.is_(True),
                models.AppVersionModel.delete_flag == 0
            ).values(
                is_active=False,
                update_time=dateUtil.get_current_time_in_beijing(),
                update_by=user.biz_id
            )
        )
    data.platform = param.platform
    data.version_name = param.version_name
    data.version_code = param.version_code
    data.is_force_update = param.is_force_update
    data.content = param.content
    data.download_url = param.download_url
    data.is_active = param.is_active
    await async_db.commit()
    await redis_.delete(key)
    data = await data.serialize_data(data)
    return common_success(data=data)


@router.delete("/{app_id}", summary="删除数据")
async def delete_app_release_list(
        app_id: int,
        user: UserPO = Security(authorize.get_backend_user, scopes=[]),
        async_db: deps.DBSessionDep = deps.DBSessionDep,
        redis_: deps.RedisSessionDep = deps.RedisSessionDep
):
    """删除数据"""
    data = await models.AppVersionModel.query_model(
        async_db,
        filters=[
            models.AppVersionModel.id == app_id,
            models.AppVersionModel.delete_flag == 0
        ]
    )
    if data is None:
        return common_enum_error(ErrorCodeEnum.NOT_FOUND)
    key = key_app_lastest.format(platform=data.platform)
    data.delete_flag = 1
    data.update_by = user.biz_id
    data.update_time = dateUtil.get_current_time_in_beijing()
    await async_db.commit()
    await redis_.delete(key)
    data = await data.serialize_data(data)
    return common_success(data=data)
