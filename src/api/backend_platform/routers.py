"""
后台管理 接口
"""
from src.api.backend_platform.block import block, ai_limit
from src.api.backend_platform.wechat_action import wechat_menu
from src.api.backend_platform.download_file import export_file
from src.api.backend_platform.system_set import approval_set, app_version
from src.api.backend_platform.action_task import stream_socket


def backend_register_blue(app):
    version = '/api/v1'
    app.include_router(block.router, prefix=f'{version}')
    app.include_router(ai_limit.router, prefix=f'{version}')
    app.include_router(wechat_menu.router, prefix=f'{version}')
    app.include_router(export_file.router, prefix=f'{version}')
    app.include_router(approval_set.router, prefix=f'{version}')
    app.include_router(app_version.router, prefix=f'{version}')
    app.include_router(stream_socket.router, prefix=f'{version}')
