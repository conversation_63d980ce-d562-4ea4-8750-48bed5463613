"""
黑名单
"""

from fastapi import APIRouter, Depends

from src.core import deps
from src.logger.logUtil import get_logger
from src.model.user_manager import UserPO
from src.services import user_service
from src.util.TokenUtil import get_current_user
from src.util.commonutil import common_success, common_enum_error, ErrorCodeEnum
from src.util.redis_keys_util import key_source_task_block

router = APIRouter(prefix="/block", tags=["黑名单"])

logger = get_logger(__name__)


@router.post("/", summary="加入黑名单")
async def block_user_add(
        param: list[str],
        redis_: deps.RedisSessionDep = deps.RedisSessionDep,
        user: UserPO = Depends(get_current_user),
):
    """加入黑名单"""
    all_flag = await user_service.system_user(user)
    if not all_flag:
        return common_enum_error(ErrorCodeEnum.NO_PERMISSION)
    messages = list()
    for user_id in param:
        try:
            await redis_.sadd(key_source_task_block, user_id)
            exists = await redis_.sismember(key_source_task_block, user_id)
            messages.append({
                "user_id": user_id,
                "exists": exists,
                "message": "success"
            })
        except Exception:
            messages.append({
                "user_id": user_id,
                "exists": False,
                "message": "error"
            })

    return common_success(data=messages)


@router.delete("/", summary="从黑名单中删除")
async def block_user_delete(
        param: list[str],
        user: UserPO = Depends(get_current_user),
        redis_: deps.RedisSessionDep = deps.RedisSessionDep
):
    """从黑名单中删除"""
    all_flag = await user_service.system_user(user)
    if not all_flag:
        return common_enum_error(ErrorCodeEnum.NO_PERMISSION)
    messages = list()
    for user_id in param:
        try:
            exists = await redis_.sismember(key_source_task_block, user_id)
            if exists:
                await redis_.srem(key_source_task_block, user_id)
            exists = await redis_.sismember(key_source_task_block, user_id)
            messages.append({
                "user_id": user_id,
                "exists": exists,
                "message": "success"
            })
        except Exception:
            messages.append({
                "user_id": user_id,
                "exists": 0,
                "message": "error"
            })

    return common_success(data=messages)


@router.get("/", summary="查看黑名单")
async def block_user_delete(
        user_id: str = None,
        user: UserPO = Depends(get_current_user),
        redis_: deps.RedisSessionDep = deps.RedisSessionDep
):
    """黑名单列表"""
    all_flag = await user_service.system_user(user)
    if not all_flag:
        return common_enum_error(ErrorCodeEnum.NO_PERMISSION)
    if user_id:
        exists = await redis_.sismember(key_source_task_block, user_id)
        return common_success(data=exists)
    else:
        members = await redis_.smembers(key_source_task_block)
        return common_success(data=members)
