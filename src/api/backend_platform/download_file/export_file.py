"""
特供接口 播客输出
"""

from fastapi import APIRouter, Depends

from src.core import deps
from src.logger.logUtil import get_logger
from src.model.user_manager import UserPO
from src.services import user_service
from src.util.TokenUtil import get_current_user
from src.util.commonutil import common_success, common_enum_error, ErrorCodeEnum
from src.util.redis_keys_util import key_source_task_block
from src.corn.downloads import article_async_download
router = APIRouter(prefix="/export", tags=["导出文件"])

logger = get_logger(__name__)


@router.post("/{export_id}", summary="使用Id导出")
async def block_user_add(
        export_id: str,
        redis_: deps.RedisSessionDep = deps.RedisSessionDep,
        user: UserPO = Depends(get_current_user),
):
    """使用Id导出"""
    await article_async_download(export_id)

    return common_success(data={})
