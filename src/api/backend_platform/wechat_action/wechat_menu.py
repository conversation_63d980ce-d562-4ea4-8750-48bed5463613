"""
微信公众号菜单接口
"""

from fastapi import APIRouter, Depends

from src.core import deps
from src.logger.logUtil import get_logger
from src.model.user_manager import UserPO
from src.util.TokenUtil import get_current_user
from src.util.commonutil import common_success
from src.util.wechat_oauth import wechat_base_client

router = APIRouter(prefix="/wechat", tags=["微信相关"])
logger = get_logger(__name__)


@router.post("/", summary="设置菜单")
async def block_user_add(
        param: dict,
        user: UserPO = Depends(get_current_user),
):
    """
    生成菜单
    {
        "button": [
            {
                "name": "商务合作",
                "sub_button": [
                    {
                        "type": "media_id",
                        "name": "商务合作",
                        "media_id": "2eZLCLh0Ku4Ym0339pfRv6KKQjo-Z5cvt2vbMvKE9MPvHGbyUQY3hVthx3TAXUoc",
                    }
                ],
            },
            {"type": "view", "name": "视频总结", "url": "https://aihaoji.com/zh"},
            {
                "type": "media_id",
                "name": "用户群",
                "media_id": "2eZLCLh0Ku4Ym0339pfRv3m-kNqCCBeXWjbg_DKTQm2OJKC1Sdh3VoVk-iELLq_S",
            },
        ]
    }
    """
    resp = wechat_base_client.menu.create(param)
    return common_success(data=resp)


@router.get("/images", summary="查看图片列表")
async def block_user_delete(
        page_no: int = 1,
        page_size: int = 20,
        user: UserPO = Depends(get_current_user)
):
    """查看图片列表"""
    images = wechat_base_client.material.batchget("image", offset=page_no, count=page_size)
    return common_success(data=images)
