import asyncio
import time
from typing import Annotated

from fastapi import APIRouter, Depends
from fastapi import (
    WebSocket,
    WebSocketDisconnect
)
from fastapi.responses import StreamingResponse
from redis.asyncio import Redis

from src.core import deps, authorize
from src.database import myredis
from src.util import redis_keys_util

router = APIRouter(prefix="/stream", tags=["流式AI任务"])


async def generate_text_stream(redis_: Redis, task_id: str):
    redis_key = redis_keys_util.key_audio_stream.format(task_id=task_id)
    count = 0
    first_count = 0
    while True:
        time.sleep(0.2)
        data = await redis_.rpop(redis_key)
        if data:
            first_count = -1
            count = 0
            yield data
        else:
            count += 1
        if first_count == -1:
            if count > 60:
                break
        else:
            if count > 150:
                break


@router.get("/stream-text")
async def stream_text(
        task_id: str,
        redis_: deps.RedisSessionDep = deps.RedisSessionDep
):
    return StreamingResponse(
        generate_text_stream(redis_, task_id=task_id),
        media_type="text/plain",
        headers={
            "Content-Type": "text/plain",
            "Cache-Control": "no-cache"
        }
    )


@router.websocket("/audio/{task_id}/ws")
async def websocket_endpoint(
        *,
        websocket: WebSocket,
        task_id: str,
        cookie_or_token: Annotated[str, Depends(authorize.get_cookie_or_token)],
):
    await websocket.accept()
    try:
        while True:
            try:
                data = await websocket.receive_text()
                async with myredis.get_async_redis() as redis_:
                    redis_key = redis_keys_util.key_audio_stream.format(task_id=task_id)
                    await myredis.set_push(redis_, redis_key, data)
                await asyncio.sleep(0.01)
            except WebSocketDisconnect:
                break
            except RuntimeError as e:
                break
    finally:
        try:
            await websocket.close()
        except:
            pass
