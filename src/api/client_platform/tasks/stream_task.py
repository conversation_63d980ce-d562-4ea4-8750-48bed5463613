from fastapi import APIRouter, Depends
from fastapi.responses import StreamingResponse

from src.core import deps
from src.services import stream_service
from src.util import redis_keys_util
from src.util.TokenUtil import get_current_user
from src.util.commonutil import common_enum_error, ErrorCodeEnum

router = APIRouter(prefix="/stream", tags=["流式任务"])


@router.get("/podcast/{task_id}", summary="播客流")
async def create_my_share_token(
        task_id: str,
        user=Depends(get_current_user),
        redis_: deps.RedisSessionDep = deps.RedisSessionDep
):
    """
    # 播客流
    """
    # lock_task_stream = redis_keys_util.key_audio_stream_lock.format(task_id=task_id)
    # data = await redis_.get(lock_task_stream)
    # if data:
    #     return common_enum_error(ErrorCodeEnum.STREAM_LOCK_ERROR)
    # await redis_.setex(lock_task_stream, 60 * 30, "data")
    redis_key = redis_keys_util.key_audio_stream.format(task_id=task_id)
    return StreamingResponse(
        stream_service.generate_redis_stream(redis_, redis_key, task_id),
        media_type="text/plain",
        headers={
            "Content-Type": "text/plain",
            "Cache-Control": "no-cache"
        }
    )
