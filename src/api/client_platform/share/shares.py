"""
分享
"""

from fastapi import APIRouter, Depends, Request
from fastapi_limiter.depends import RateLimiter

from src.core import deps
from src.database import constants, myredis
from src.logger.logUtil import get_logger, LoggerName
from src.services import share_service, article_service
from src.util import stringify, redis_keys_util
from src.util.TokenUtil import get_current_user, get_share_user
from src.util.commonutil import common_success, CommonResponse, common_enum_error, ErrorCodeEnum, common_error

router = APIRouter(prefix="/share", tags=["笔记分享"])

logger = get_logger(LoggerName.client)


@router.post("/_search", summary="我的分享列表")
async def search_my_share(
        param: share_service.SearchShareSchema,
        user=Depends(get_current_user),
        async_db: deps.DBSessionDep = deps.DBSessionDep,
        redis_: deps.RedisSessionDep = deps.RedisSessionDep
):
    """删除分享"""
    data = await share_service.search_share(redis_, async_db, param, user.biz_id)
    if isinstance(data, CommonResponse):
        return data
    return common_success(data=data)


@router.get("/{task_id}", summary="分享token")
async def create_my_share_token(
        task_id: str,
        user=Depends(get_current_user),
        async_db: deps.DBSessionDep = deps.DBSessionDep,
        redis_: deps.RedisSessionDep = deps.RedisSessionDep
):
    """
    # 分享token
    - share_id 存在则分享已创建 如为None 则需要掉创建接口，
    """
    data = await share_service.create_token(redis_, async_db, task_id, user.biz_id)
    share_id = None
    if data:
        share_token = data.share_token
        share_id = data.biz_id
    else:
        share_token = stringify.random_letters(constants.SHARE_TOKEN_LENGTH)
    return common_success(data={
        "share_token": share_token,
        "share_id": share_id
    })


@router.post("/", summary="新建分享")
async def create_my_share(
        param: share_service.CreateShareDefaultSchema,
        user=Depends(get_current_user),
        async_db: deps.DBSessionDep = deps.DBSessionDep,
        redis_: deps.RedisSessionDep = deps.RedisSessionDep
):
    """最新版本"""
    data = await share_service.create_share(redis_, async_db, param, user.biz_id)
    if isinstance(data, CommonResponse):
        return data
    return common_success(data=data)


@router.put("/{share_id}", summary="关闭/启用分享")
async def active_my_share(
        share_id: str,
        user=Depends(get_current_user),
        async_db: deps.DBSessionDep = deps.DBSessionDep,
        redis_: deps.RedisSessionDep = deps.RedisSessionDep
):
    """关闭/启用分享"""
    data = await share_service.remove_or_active_share(redis_, async_db, share_id, user.biz_id)
    if isinstance(data, CommonResponse):
        return data
    return common_success(data=data)


@router.delete("/{share_id}", summary="删除分享")
async def delete_my_share(
        share_id: str,
        user=Depends(get_current_user),
        async_db: deps.DBSessionDep = deps.DBSessionDep,
        redis_: deps.RedisSessionDep = deps.RedisSessionDep
):
    """删除分享"""
    data = await share_service.remove_or_active_share(redis_, async_db, share_id, user.biz_id, remove=True)
    if isinstance(data, CommonResponse):
        return data
    return common_success(data=data)


@router.get("/articles/meta", dependencies=[Depends(RateLimiter(times=3, seconds=30))], summary="分享文章meta内容-左侧")
async def section_read_share(
        request: Request,
        share_token: str,
        user=Depends(get_share_user),
        redis_: deps.RedisSessionDep = deps.RedisSessionDep,
        async_db: deps.DBSessionDep = deps.DBSessionDep
):
    try:
        headers = request.headers
        ip_address = headers.get("x-forwarded-for", "").split(",")[0] or \
                     headers.get("x-real-ip") or \
                     request.client.host
        user_agent = headers.get("user-agent")
        if len(share_token) != 18:
            return common_enum_error(ErrorCodeEnum.PARAMS_ERROR)

        share_data = await share_service.get_share_by_token(redis_, async_db, share_token)
        if not share_data:
            return common_enum_error(ErrorCodeEnum.SHARE_NOT_EXISTS)
        user_id = share_data.get("user_id")
        task_biz_id = share_data.get("task_id")
        share_id = share_data.get("biz_id")
        share_token = share_data.get("share_token")
        data = await article_service.get_meta_article(async_db, redis_, user_id, task_biz_id)
        if isinstance(data, CommonResponse):
            return data
        access_data = await share_service.access_record(async_db, share_id, ip_address, user_agent, user)
        if access_data:
            data["access_id"] = access_data.biz_id
        key = redis_keys_util.key_share_all.format(user_id=user_id)
        await myredis.clear_redis_cache(redis_, key)
        await redis_.delete(key)
        token_key = redis_keys_util.key_share_token.format(token=share_token)
        await redis_.delete(token_key)
        return common_success(data)
    except Exception as ex:
        logger.exception(f"获取文章详细失败，share_token={share_token}\n{stringify.from_exception(ex)}")
        return common_error("获取文章详细失败")


@router.get("/articles/details", summary="分享文章内容列表")
async def section_read_share(
        share_token: str,
        page_size: int = 5,
        page_no: int = 1,
        start_time_str=None,
        end_time_str=None,
        user=Depends(get_share_user),
        redis_: deps.RedisSessionDep = deps.RedisSessionDep,
        async_db: deps.DBSessionDep = deps.DBSessionDep
):
    try:
        if user is None:
            page_no = 1
            page_size = 2
            start_time_str = None
            end_time_str = None
        if len(share_token) != 18:
            return common_enum_error(ErrorCodeEnum.PARAMS_ERROR)
        share_data = await share_service.get_share_by_token(redis_, async_db, share_token)
        if not share_data:
            return common_enum_error(ErrorCodeEnum.SHARE_NOT_EXISTS)
        user_id = share_data.get("user_id")
        task_biz_id = share_data.get("task_id")

        task = await article_service.can_access_article_cache(async_db, redis_, user_id, task_biz_id)
        if not task:
            return common_enum_error(ErrorCodeEnum.NO_PERMISSION)
        article = task.article
        if not article:
            return common_enum_error(ErrorCodeEnum.ARTICLE_NOT_EXISTS)

        data = await article_service.search_article_details(
            async_db, redis_, user_id, task_biz_id, article, page_no, page_size, start_time_str, end_time_str
        )
        return common_success(data)
    except Exception as ex:
        logger.exception(f"获取文章详细失败，share_token={share_token}\n{stringify.from_exception(ex)}")
        return common_error("获取文章详细失败")
