"""
app 配置
"""
import json
from typing import Literal

from fastapi import APIRouter, Path

from src.core import deps
from src.database import models
from src.logger.logUtil import get_logger, LoggerName
from src.util.commonutil import common_success
from src.util.redis_keys_util import key_app_lastest
from src.util import stringify

router = APIRouter(prefix="/folder", tags=["笔记本"])

logger = get_logger(LoggerName.client)


@router.post("/", summary="创建笔记本")
async def app_release_list(
        platform: Literal['android', 'ios'] = Path(..., description="'android' 或 'ios'"),
        async_db: deps.DBSessionDep = deps.DBSessionDep,
        redis_: deps.RedisSessionDep = deps.RedisSessionDep
):
    """最新版本"""
    key = key_app_lastest.format(platform=platform)
    data = await redis_.get(key)
    if data:
        return common_success(data=json.loads(data))
    data = await models.AppVersionModel.query_model(
        async_db,
        fields=[
            models.AppVersionModel.platform,
            models.AppVersionModel.version_name,
            models.AppVersionModel.version_code,
            models.AppVersionModel.is_force_update,
            models.AppVersionModel.content,
            models.AppVersionModel.download_url,
            models.AppVersionModel.is_active,
        ],
        filters=[
            models.AppVersionModel.platform == platform,
            models.AppVersionModel.is_active.is_(True),
            models.AppVersionModel.delete_flag == 0
        ],
        serialize=True
    )
    if data:
        await redis_.setex(key, 60 * 60 * 24, stringify.from_json(data))
    return common_success(data=data)
