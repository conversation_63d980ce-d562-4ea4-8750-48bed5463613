"""
公共接口
"""
import json
from typing import Literal

from fastapi import APIRouter, Path, Depends, Request
from pydantic import BaseModel, HttpUrl

from src.core import deps
from src.database import models, enums
from src.logger.logUtil import get_logger, LoggerName
from src.services import wechat_service
from src.util import stringify
from src.util.TokenUtil import get_current_user
from src.util.commonutil import common_enum_error, ErrorCodeEnum, common_success
from src.util.redis_keys_util import key_app_lastest

router = APIRouter(prefix="/public", tags=["公共接口"])

logger = get_logger(LoggerName.client)


@router.get("/lastest/{platform}", summary="APP最新版本")
async def app_release_list(
        platform: Literal['android', 'ios'] = Path(..., description="'android' 或 'ios'"),
        async_db: deps.DBSessionDep = deps.DBSessionDep,
        redis_: deps.RedisSessionDep = deps.RedisSessionDep
):
    """最新版本"""
    key = key_app_lastest.format(platform=platform)
    data = await redis_.get(key)
    if data:
        return common_success(data=json.loads(data))
    data = await models.AppVersionModel.query_model(
        async_db,
        fields=[
            models.AppVersionModel.platform,
            models.AppVersionModel.version_name,
            models.AppVersionModel.version_code,
            models.AppVersionModel.is_force_update,
            models.AppVersionModel.content,
            models.AppVersionModel.download_url,
            models.AppVersionModel.is_active,
        ],
        filters=[
            models.AppVersionModel.platform == platform,
            models.AppVersionModel.is_active.is_(True),
            models.AppVersionModel.delete_flag == 0
        ],
        serialize=True
    )
    if data:
        await redis_.setex(key, 60 * 60 * 24, stringify.from_json(data))
    return common_success(data=data)


class JSSDKSchema(BaseModel):
    url: HttpUrl


@router.post("/js_sdk/signa", summary="wechat js sdk 签名")
async def create_my_share_token(
        request: Request,
        param: JSSDKSchema,
        user=Depends(get_current_user),
        redis_: deps.RedisSessionDep = deps.RedisSessionDep
):
    """
    # js sdk 签名
    """
    source = request.headers.get("source", enums.SourceEnum.PC.value)
    data = await wechat_service.js_sdk_signa(redis_, source, url=str(param.url))
    if data is None:
        return common_enum_error(ErrorCodeEnum.WECHAT_API_ERROR)
    return common_success(data)
