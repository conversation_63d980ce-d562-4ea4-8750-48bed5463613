from typing import Optional

from fastapi import HTTPException
from pydantic import BaseModel, HttpUrl, field_validator

from src.database.enums import TaskStatusEnum


class SpecialMarkDownSchema(BaseModel):
    content: Optional[str]
    callback_url: Optional[HttpUrl] = None
    ctrl_params: dict = {}
    PropagateID: str = None
    ReservedCode2: str = None
    Propagate: str = None
    audio_name: str = None
    category: str = None
    # ctrl_params= {
    #     "maleName": podcast_params.get("maleName", ""),
    #     "femaleName": podcast_params.get("femaleName", ""),
    #     "maleTimbre": podcast_params.get("maleTimbre", "zh_m_jiawen"),
    #     "femaleTimbre": podcast_params.get("femaleTimbre", "zh_f_qingqing"),
    #     "language": "zh"
    # }

class SpecialFinishSchema(BaseModel):
    task_id: str
    status: str
    task_info: dict

    @field_validator("status")
    def check_status(cls, value):
        if str(value).lower() not in [
            TaskStatusEnum.INVALID.value.lower(),
            TaskStatusEnum.FAILED.value.lower(),
            TaskStatusEnum.FINISH.value.lower()
        ]:
            raise HTTPException(status_code=400, detail="status: invalid, fail, finish 是允许的")
        return value
