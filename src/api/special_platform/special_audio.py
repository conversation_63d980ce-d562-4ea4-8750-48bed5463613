"""
特供接口 播客输出
"""
import json
from typing import Optional, Literal

from fastapi import APIRouter, Depends, Path
from fastapi_limiter.depends import RateLimiter
from fastapi.responses import StreamingResponse

from src.api.open.open_platform_task_api import get_open_key_with_auth
from src.api.special_platform import schemas
from src.core import deps
from src.database.enums import SpecialTaskCategoryEnum
from src.logger.logUtil import get_logger
from src.services.special_services import markdown_audio
from src.services.stream_service import generate_redis_stream
from src.util.TokenUtil import verify_key
from src.util.commonutil import ErrorCodeEnum, common_enum_error, common_error, common_success
from src.util import stringify, redis_keys_util

router = APIRouter(prefix="/audio", tags=["Ai播客任务"])

logger = get_logger(__name__)


@router.post("/{category}", dependencies=[Depends(RateLimiter(times=3, seconds=5))], summary="创建任务")
async def special_create_task(
        app_key: str, t: int, sign: str,
        param: schemas.SpecialMarkDownSchema,
        category: Literal['markdown'] = Path(title="AI播客任务类型， markdown markdown文档"),
        platform=Depends(get_open_key_with_auth),
        async_db: deps.DBSessionDep = deps.DBSessionDep
):
    """创建任务"""
    if category.lower() == SpecialTaskCategoryEnum.MARKDOWN.value.lower():
        if param.PropagateID is None or param.ReservedCode2 is None or param.Propagate is None:
            return common_error("PropagateID、ReservedCode2、Propagate 为必填字段")
        mark, message = await markdown_audio.crete_markdown_task(async_db, param.__dict__, platform)
        if not mark:
            logger.error(f'创建任务失败::{category}::{message}')
            return common_error(message)
        return common_success(data={"task_id": message})
    return common_enum_error(ErrorCodeEnum.CHECK_PATH_API)


# 查询转换状态
@router.get("/{category}/status", summary="查询任务状态")
async def get_task_status(
        app_key: str, t: int, task_id: str, sign: str,
        category: Literal['markdown'] = Path(title="AI播客任务类型， markdown markdown文档"),
        platform=Depends(get_open_key_with_auth),
        async_db: deps.DBSessionDep = deps.DBSessionDep,
        redis_: deps.RedisSessionDep = deps.RedisSessionDep
):
    # 任务状态
    key = redis_keys_util.key_special_task_status.format(task_id=task_id)
    cache_data = await redis_.get(key)
    if cache_data:
        return common_success(data=json.loads(cache_data))
    if category.lower() == SpecialTaskCategoryEnum.MARKDOWN.value.lower():
        mark, message = await markdown_audio.get_task_status(async_db, task_id, platform)
        if not mark:
            logger.error(f'查询任务状态失败::{category}::{message}')
            return common_error(message)
        if "caption_list" in message:
            del message["caption_list"]
        await redis_.setex(key, 60 * 15, stringify.from_json(message))
        return common_success(data=message)
    return common_enum_error(ErrorCodeEnum.CHECK_PATH_API)


@router.get("/{category}/caption", summary="口播稿")
async def get_task_status(
        app_key: str, t: int, task_id: str, sign: str,
        category: Literal['markdown'] = Path(title="AI播客任务类型， markdown markdown文档"),
        platform=Depends(get_open_key_with_auth),
        async_db: deps.DBSessionDep = deps.DBSessionDep,
        redis_: deps.RedisSessionDep = deps.RedisSessionDep
):
    # 任务状态
    key = redis_keys_util.key_special_task_caption.format(task_id=task_id)
    cache_data = await redis_.get(key)
    if cache_data:
        return common_success(data=json.loads(cache_data))
    if category.lower() == SpecialTaskCategoryEnum.MARKDOWN.value.lower():
        mark, message = await markdown_audio.get_task_status(async_db, task_id, platform)
        if not mark:
            logger.error(f'查询口播稿失败::{category}::{message}')
            return common_error(message)
        await redis_.setex(key, 60 * 30, stringify.from_json(message))
        return common_success(data=message)
    return common_enum_error(ErrorCodeEnum.CHECK_PATH_API)


@router.get("/{category}/stream", summary="播客流")
async def get_task_status(
        task_id: str,
        category: Literal['markdown'] = Path(title="AI播客任务类型， markdown markdown文档"),
        platform=Depends(get_open_key_with_auth),
        redis_: deps.RedisSessionDep = deps.RedisSessionDep
):
    # 任务状态
    # lock_task_stream = redis_keys_util.key_audio_stream_lock.format(task_id=task_id)
    # data = await redis_.get(lock_task_stream)
    # if data:
    #     return common_enum_error(ErrorCodeEnum.STREAM_LOCK_ERROR)
    #
    # await redis_.setex(lock_task_stream, 60 * 30, "data")
    if category.lower() == SpecialTaskCategoryEnum.MARKDOWN.value.lower():
        redis_key = redis_keys_util.key_audio_stream.format(task_id=task_id)
        return StreamingResponse(
            generate_redis_stream(redis_, redis_key, task_id),
            media_type="text/plain",
            headers={
                "Content-Type": "text/plain",
                "Cache-Control": "no-cache"
            }
        )
    return common_enum_error(ErrorCodeEnum.CHECK_PATH_API)



@router.get("/{category}/latest", summary="获取最后一条任务")
async def get_latest_task(
        task_id: Optional[str] = None,
        category: Literal['markdown'] = Path(title="AI播客任务类型， markdown markdown文档"),
        key: str = Depends(verify_key)
):
    # 获取最后一条任务
    try:
        if category.lower() == SpecialTaskCategoryEnum.MARKDOWN.value.lower():
            task = await markdown_audio.get_last_task(task_id)
            if task:
                return common_success(data=task)
            return common_success()
        return common_enum_error(ErrorCodeEnum.CHECK_PATH_API)
    except Exception as ex:
        logger.error(f"任务获取失败:{task_id}\n{stringify.from_exception(ex)}")
        return common_error("获取失败")


@router.post("/{category}/finish", summary="任务完成调用")
async def complete_task(
        param: schemas.SpecialFinishSchema,
        category: Literal['markdown'] = Path(title="AI播客任务类型， markdown markdown文档"),
        key: str = Depends(verify_key),
        async_db: deps.DBSessionDep = deps.DBSessionDep,
        redis_: deps.RedisSessionDep = deps.RedisSessionDep
):
    # 更新任务状态
    try:
        key = redis_keys_util.key_special_task_status.format(task_id=param.task_id)
        await redis_.delete(key)
        key_caption = redis_keys_util.key_special_task_caption.format(task_id=param.task_id)
        await redis_.delete(key_caption)
        if category.lower() == SpecialTaskCategoryEnum.MARKDOWN.value.lower():
            mark, message = await markdown_audio.task_finish(
                async_db, param.task_id, param.status, param=param.task_info)
            if mark:
                return common_success(data={"task_id": message, "status": param.status})
            logger.error(f'特供接口任务状态更新失败::{category}::{param.task_id}::{message}')
            return common_error(message)
        return common_enum_error(ErrorCodeEnum.CHECK_PATH_API)
    except Exception as ex:
        logger.error(f"特供接口任务状态更新失败::{category}::{param.__dict__}\n{stringify.from_exception(ex)}")
        return common_error("更新任务状态败")
