import uuid
from datetime import datetime, timezone
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException
from playhouse.postgres_ext import fn
from pydantic import BaseModel, HttpUrl, field_validator

from src.api.open.open_platform_task_api import get_open_key_with_auth
from src.corn.book2audio import async_book2audio_callback
from src.logger.logUtil import get_logger
from src.model.book2audio import BookTask, BookTaskStatus, get_latest_book2task
from src.util.commonutil import ErrorCodeEnum, common_enum_error, common_error, common_success
from src.util.TokenUtil import verify_key

router = APIRouter(prefix="/api/v1/book2audio")

logger = get_logger(__name__)

# 允许上传的文件类型
ALLOWED_AUDIO_EXTENSIONS = {".mp3", ".wav", ".flac"}
ALLOWED_SUBTITLE_EXTENSIONS = {".srt", ".vtt"}


class BookRequestVO(BaseModel):
    book_title: str
    author: str
    content: Optional[str] = ""
    corpus: Optional[str] = ""
    callback_url: Optional[HttpUrl] = None
    ctrl_params: dict = {}
    """
    ctrl_params i.e.
    {
        "language": "zh",
        "voice": "female",
        "format": "mp3",
    }
    """


# 上传书籍并请求转换
@router.post("/convert")
async def convert_book(app_key: str, t: int, sign: str, bookvo: BookRequestVO, _=Depends(get_open_key_with_auth)):
    conditions = [
        BookTask.book_title == bookvo.book_title,
        BookTask.author == bookvo.author,
        BookTask.corpus == bookvo.corpus,
        BookTask.delete_flag == 0,
    ]
    if bookvo.ctrl_params.get("language"):
        conditions.append(
            (fn.json_extract_path_text(BookTask.ctrl_params, "language") == bookvo.ctrl_params.get("language"))
        )
    if bookvo.ctrl_params.get("voice"):
        conditions.append((fn.json_extract_path_text(BookTask.ctrl_params, "voice") == bookvo.ctrl_params.get("voice")))
    if bookvo.ctrl_params.get("format"):
        conditions.append(
            (fn.json_extract_path_text(BookTask.ctrl_params, "format") == bookvo.ctrl_params.get("format"))
        )
    task = BookTask.select().where(*conditions).first()
    if not task:
        task = BookTask.create(
            biz_id=f"book2audio_{uuid.uuid4()}",
            **bookvo.model_dump(),
        )
        return common_success(data={"task_id": task.biz_id, "status": task.status})
    else:
        return common_enum_error(ErrorCodeEnum.TASK_REPEAT)


# 查询转换状态
@router.get("/status")
async def get_task_status(app_key: str, t: int, task_id: str, sign: str, _=Depends(get_open_key_with_auth)):
    task = BookTask.get_or_none(BookTask.biz_id == task_id, BookTask.delete_flag == 0)
    if not task:
        return common_enum_error(ErrorCodeEnum.TASK_NOT_EXISTS)
    return common_success(data=task.to_vo())


@router.post("/getLatest")
async def get_latest(task_id: Optional[str] = None, key: str = Depends(verify_key)):
    try:
        task = get_latest_book2task(task_id)
        if task:
            return common_success(data=task.__data__)
        else:
            return common_success()
    except Exception:
        logger.exception("任务获取失败")
        return common_error("获取失败")


class BookTaskVO(BaseModel):
    task_id: str
    status: str
    remark: Optional[str] = None
    duration: Optional[int] = None  # 花费的时间，单位秒
    size: Optional[int] = None  # 处理的字节大小
    book_title: Optional[str] = None  # book 的名字
    audio_uri: Optional[HttpUrl] = None
    caption_list: Optional[List[dict]] = []

    @field_validator("status")
    def check_status(cls, value):
        if value not in [BookTaskStatus.invalid, BookTaskStatus.failed, BookTaskStatus.completed]:
            raise HTTPException(status_code=400, detail="status: invalid, failed, completed 是允许的")
        return value


# 示例：完成任务后，回调掌阅
@router.post("/finish")
async def complete_task(task_vo: BookTaskVO, key: str = Depends(verify_key)):
    task = BookTask.get_or_none(BookTask.biz_id == task_vo.task_id, BookTask.delete_flag == 0)
    if not task:
        return common_error(f"task: {task_vo.task_id} not found")
    # 构建更新字段的字典
    update_data = {}
    if task_vo.duration:
        update_data["duration"] = task_vo.duration
    if task_vo.size:
        update_data["size"] = task_vo.size
    if task_vo.remark:
        update_data["remark"] = task_vo.remark
    if task_vo.book_title:
        update_data["book_title"] = task_vo.book_title
    if task_vo.audio_uri:
        update_data["audio_uri"] = task_vo.audio_uri
    if task_vo.caption_list:
        update_data["caption_list"] = task_vo.caption_list
    update_data["status"] = task_vo.status  # 状态必定更新
    update_data["updated_at"] = datetime.now(timezone.utc)  # 更新时间
    # 执行数据库更新
    if update_data:
        count = (
            BookTask.update(**update_data)
            .where(BookTask.biz_id == task_vo.task_id, BookTask.delete_flag == 0)
            .execute()
        )
        if count:
            async_book2audio_callback.delay(task_vo.task_id)
            return common_success(data={"task_id": task_vo.task_id, "status": task_vo.status})
        else:
            logger.error(f"task: {task_vo.task_id} update failed")
    return common_error("finish 失败")
