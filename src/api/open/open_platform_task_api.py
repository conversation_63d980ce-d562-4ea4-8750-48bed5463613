import json
import random
import string
import threading
from datetime import datetime, timezone
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Request
from fastapi_limiter.depends import RateLimiter
from peewee import DoesNotExist, Value
from playhouse.postgres_ext import fn
from playhouse.shortcuts import model_to_dict
from starlette.datastructures import QueryParams

from src.api.article.article_api import query_article_details
from src.logger.logUtil import get_logger
from src.model.User_action_record_manager import UserActionProcess, UserActionType, add_user_action_record
from src.model.article_manager import LectureArticle
from src.model.lecture_ai import AITYPE, check_ai_task_has_created, create_ai_task_with_task_type, \
    check_ai_task_has_list
from src.model.oplatform_manager import get_open_platform_by_appKey, sub_open_platform_point
from src.model.task_manager import TASK_SOURCE_CHOICES, LectureTask, OpenPlatformTaskVo
from src.util import MD5Util
from src.util.MD5Util import encode_map_to_string
from src.util.cacheUtil import open_api_cache
from src.util.commonutil import CommonResponse, ErrorCodeEnum, common_enum_error, common_error, common_success, \
    common_balance_error
from src.util.dateUtil import get_current_time_in_beijing
from src.util.stringify import get_uid
from src.util.url_handle_v2 import SourceHandler
from src.services import url_service

logger = get_logger(__name__)

router = APIRouter(prefix="/open/api/v1/task")


async def get_open_key_with_auth(request: Request, app_key: str, t: int, sign: str):
    # 获取时间年月日参数，以yyyyMMdd格式
    datestr = get_current_time_in_beijing().strftime("%Y%m%d")
    metadata = open_api_cache.get(app_key + "-" + datestr)
    if not metadata:
        platform = get_open_platform_by_appKey(app_key)
        if not platform:
            return common_error("app_key 不存在")
        open_api_cache.set(app_key + "-" + datestr, {"platform": model_to_dict(platform)})
        metadata = open_api_cache.get(app_key + "-" + datestr)

    current_timestamp = datetime.now(timezone.utc).timestamp()
    # 如果当前时间超过5分钟，签名失效
    if t + 300000 < current_timestamp:
        raise HTTPException(status_code=403, detail="签名时间超过5min")
    params: QueryParams = request.query_params
    data = {}
    data.update(params)
    data.pop("sign")
    if await request.body():
        try:
            data.update(await request.json())
        except json.JSONDecodeError:
            pass
    calSign = MD5Util.get_md5_hash(encode_map_to_string(data), metadata["platform"]["secret_key"])
    if calSign != sign:
        raise HTTPException(status_code=403, detail="签名错误")
    return metadata["platform"]


locks = {}


def get_lock(task_id):
    if task_id not in locks:
        locks[task_id] = threading.Lock()
    return locks[task_id]

def create_task_old(rep, detail, platform, task_biz_id):
    task = LectureTask.create(**{
        "url": rep.url,
        "task_source": rep.task_source,
        "open_platform_id": platform["biz_id"],
        "delete_flag": 0,
        "biz_id": task_biz_id,
        "token": "".join(random.sample(string.ascii_letters + string.digits, 6)),
        "name": detail.title  or "未命名",
        "duration_minutes": detail.duration_minutes,
        "status": "confirm",
        "process_percent": 0,
        "start_time": datetime.now(tz=timezone.utc),
        "end_time": None,
        "create_time": datetime.now(tz=timezone.utc),
        "update_time": datetime.now(tz=timezone.utc),
        "order": 3,
        "author": detail.author_name or "",
        "url_biz_id": detail.biz_id,
        "img_url": detail.pic_path or "",
        "ctrl_params": rep.ctrl_params,
    })

    # 新任务插入一条新的文章
    article = LectureArticle.create(
        delete_flag=0,
        biz_id=get_uid(),
        name=detail.title,
        article_source=rep.task_source,
        create_time=datetime.now(tz=timezone.utc),
        update_time=datetime.now(tz=timezone.utc),
        video_url=rep.url,
        img_url=detail.pic_path,
        author=detail.author_name,
    )
    task.article_biz_id = article.biz_id
    task.save()
    return task, article


@router.post("/create", response_model=CommonResponse, dependencies=[Depends(RateLimiter(times=2, seconds=5))])
async def create_task(
        rep: OpenPlatformTaskVo,
        app_key: str,
        t: int,
        sign: str,
        platform=Depends(get_open_key_with_auth),
):
    try:
        if rep.task_source == TASK_SOURCE_CHOICES["upload"]:
            return common_error("上传视频不支持")

        if rep.task_source == app_key:
            detail = await url_service.create_url_link(str(rep.url), rep.task_source)
        else:
            detail = SourceHandler.handle_source(str(rep.url), rep.task_source)
        if not detail:
            return common_error("非法链接", code=400)

        if detail.duration_minutes is None:
            detail.duration_minutes = 0

        if detail.duration_minutes <=  0:
            return common_error("视频时长不合法")
        # 积分
        platform_point = platform["points"] or 0
        if platform_point < detail.duration_minutes:
            return common_balance_error("您剩余使用额度不足以转录当前内容，请充值。")

        # 如果没有设置过字段，给默认值
        rep.ctrl_params.setdefault("input_language", "auto")
        rep.ctrl_params.setdefault("output_language", "zh")
        rep.ctrl_params.setdefault("image_mode", "no")
        rep.ctrl_params.setdefault("enable_speaker_recognition", False)
        rep.ctrl_params.setdefault("enable_ai_polish", False)
        rep.ctrl_params.setdefault("enable_total_summary", False)
        rep.ctrl_params.setdefault("total_summary_template", "default")
        rep.ctrl_params.setdefault("enable_outline", False)
        rep.ctrl_params.setdefault("enable_podcast_summary", False)
        rep.ctrl_params.setdefault("custom_words", [])
        rep.url = detail.origin_url
        filters = [
            LectureTask.url == rep.url,
            LectureTask.task_source == rep.task_source,
            LectureTask.open_platform_id == platform["biz_id"],
            LectureTask.delete_flag == 0,
            fn.json_extract_path_text(
                LectureTask.ctrl_params, "image_mode") == rep.ctrl_params.get("image_mode"),
            fn.json_extract_path_text(
                LectureTask.ctrl_params, "input_language") == rep.ctrl_params.get("input_language"),
            fn.json_extract_path_text(
                LectureTask.ctrl_params, "output_language") == rep.ctrl_params.get("output_language"),
            fn.json_extract_path_text(
                LectureTask.ctrl_params, "enable_speaker_recognition").cast('boolean') == rep.ctrl_params.get(
                "enable_speaker_recognition"),
            fn.json_extract_path_text(
                LectureTask.ctrl_params, "enable_ai_polish").cast('boolean') == rep.ctrl_params.get("enable_ai_polish"),
            fn.json_extract_path_text(
                LectureTask.ctrl_params, "enable_total_summary").cast('boolean') == rep.ctrl_params.get(
                "enable_total_summary"),
            fn.json_extract_path_text(
                LectureTask.ctrl_params, "total_summary_template") == rep.ctrl_params.get("total_summary_template"),
            fn.json_extract_path_text(
                LectureTask.ctrl_params, "enable_outline").cast('boolean') == rep.ctrl_params.get("enable_outline"),
            fn.json_extract_path_text(
                LectureTask.ctrl_params, "enable_podcast_summary").cast('boolean') == rep.ctrl_params.get(
                "enable_podcast_summary")
        ]

        try:
            task = LectureTask.select().where(*filters).get()
            return common_enum_error(ErrorCodeEnum.TASK_REPEAT, task.biz_id)
        except DoesNotExist:
            created = True

        task_biz_id = str(UUID(int=random.getrandbits(128)))

        with get_lock(rep.task_source):
            if detail.duration_minutes > 0:
                points_sub_flag = sub_open_platform_point(platform["biz_id"], detail.duration_minutes)
                if not points_sub_flag:
                    return common_error("您剩余使用额度不足以转录当前内容，请充值。")
            task, article = create_task_old(rep, detail, platform, task_biz_id=task_biz_id)
            add_user_action_record(
                platform["biz_id"],
                UserActionType.UNLOCK_VIDEO,
                UserActionProcess.SUB,
                f"平台{rep.task_source} 转换视频",
                detail.duration_minutes,
                task_biz_id,
            )
            # ai 播客
            enable_podcast_summary = rep.ctrl_params.get("enable_podcast_summary")
            if enable_podcast_summary:
                podcast_params = rep.ctrl_params.get("podcast_param", {})
                param = {
                    "user_id": platform["biz_id"],
                    "article_biz_id": article.biz_id,
                    "task_type": 7,
                    "ctrl_params": {
                        "maleName": podcast_params.get("maleName", ""),
                        "femaleName": podcast_params.get("femaleName", ""),
                        "maleTimbre": podcast_params.get("maleTimbre", "zh_m_jiawen"),
                        "femaleTimbre": podcast_params.get("femaleTimbre", "zh_f_qingqing"),
                        "language": "zh"
                    }
                }
                create_ai_task_with_task_type(**param)
        return common_success(data={"biz_id": task.biz_id, "duration": task.duration_minutes})
    except Exception:
        logger.exception("open platform create task error")
        return common_error("创建失败")


@router.get("/status")
async def task_status(app_key: str, t: int, sign: str, task_id: str, platform=Depends(get_open_key_with_auth)):
    # 如果keyword 存在，就查询关键字。 如果不存在，就查询所有
    task = (
        LectureTask.select()
        .where(
            LectureTask.open_platform_id == platform["biz_id"],
            LectureTask.biz_id == task_id,
            LectureTask.delete_flag == 0,
        )
        .first()
    )
    if not task:
        return common_error("任务不存在")

    ret_data = {"biz_id": task.biz_id, "status": "processing", "points": task.duration_minutes, "article": {}}

    if task.status == "fail":
        ret_data.update({"status": task.status, "remark": task.remark})
    ai_tasks = check_ai_task_has_list(task.article_biz_id, [AITYPE.AI_POLISHING.value, AITYPE.AUDIO.value])
    finished_list = list()
    audio = False
    for item_task in ai_tasks:
        if item_task.status in ["finish", "finished"]:
            finished_list.append(True)
        else:
            finished_list.append(False)
        if item_task.task_type == AITYPE.AUDIO.value:
            audio = True
    if finished_list and all(finished_list):
        article = (
            LectureArticle.select()
            .where(LectureArticle.biz_id == task.article_biz_id, LectureArticle.delete_flag == 0)
            .first()
        )

        ret_data.update({"status": task.status, "article": article.to_vo_audio() if audio else article.to_vo()})
    return common_success(ret_data)


@router.get("/sections")
async def task_sections(
        task_id: str,
        app_key: str,
        t: int,
        sign: str,
        page_no: int = 1,
        page_size: int = 10,
        platform=Depends(get_open_key_with_auth),
):
    # 如果keyword 存在，就查询关键字。 如果不存在，就查询所有
    task = (
        LectureTask.select()
        .where(
            LectureTask.open_platform_id == platform["biz_id"],
            LectureTask.biz_id == task_id,
            LectureTask.delete_flag == 0,
        )
        .first()
    )
    if not task:
        return common_error("任务不存在")
    ret_data = {
        "biz_id": task.biz_id,
        "status": "processing",
        "points": task.duration_minutes,
        "sections": [],
        "total": 0,
    }
    if task.status == "fail":
        ret_data.update({"status": task.status, "remark": task.remark})
    out_language = (task.ctrl_params or {}).get("output_language") or "zh"
    ai_task = check_ai_task_has_created(task.article_biz_id, AITYPE.AI_POLISHING.value)
    if task.status == "finish" and (ai_task and ai_task.status in ["finish", "finished"]):
        details, total = await query_article_details(task.article_biz_id, None, page_no, page_size, None)
        ret_data.update(
            {
                "status": task.status,
                "sections": [detail.to_open_vo(out_language) for detail in details],
                "total": total
            }
        )
    return common_success(data=ret_data)
