import json
import random
from typing import Dict
from uuid import UUID

import requests
from fastapi import APIRouter, Request
from playhouse.shortcuts import model_to_dict
from svix import Webhook, WebhookVerificationError

from config import settings
from src.api.login.user_api import update_user
from src.logger.logUtil import get_logger
from src.model.order_manager import ProductID, update_membership
from src.model.user_manager import UserPO, delete_user, get_user_by_id
from src.util.beta_test_user import beta_test_user_email
from src.util.cacheUtil import DotDict, ttl_cache
from src.util.commonutil import common_error, common_success
from src.util.TokenUtil import create_jwt

logger = get_logger(__name__)

router = APIRouter(prefix="/api/v1")


async def auth_check(header, body) -> Dict:
    try:
        wh = Webhook(settings.CLERK_OAUTH_KEY)
        msg = wh.verify(body, header)
        return msg
    except WebhookVerificationError:
        logger.exception("auth error: ")
        return {}


@router.post("/oauth/github")
async def githubOauth(token):
    """
    github oauth
    :param req:
    :return:
    """
    url = "https://api.github.com/user?access_token=" + token
    headers = {"Authorization": "token " + token}
    resp = requests.get(url, headers=headers, timeout=10)

    if resp.status_code == 200:
        data = resp.json()
        user = get_user_by_id(data.get("id"))
        if user:
            token = create_jwt(user.__data__)
            ttl_cache.set(user.biz_id, DotDict(user.__data__))
            return common_success({"jwtToken": token})
        else:
            UserPO.create(
                biz_id="user_" + str(UUID(int=random.getrandbits(128))),
                email=data.get("email"),
                name=data.get("name"),
                head_img=data.get("avatar_url"),
            )
        return common_success()
    else:
        logger.error(f"Github oauth error: {resp.text}")
        return common_error("Github oauth error")


@router.post("/oauth")
async def oauth(request: Request):
    """
    webhook
    :param request:
    :return: 用户信息
    """
    data = await auth_check(request.headers, await request.body())
    switch = {
        "session.ended": session_end,
        "session.created": session_create,
        "user.created": user_created,
        "user.deleted": user_deleted,
        "user.update": user_updated,
    }
    if data:
        func = switch.get(data.get("type"))
        if func:
            await func(data.get("data"))
        return common_success()
    return common_error("auth failed")


@router.get("/test/login")
async def test_login(request: Request, user_id):
    if not settings.APP_DEBUG:
        return common_error("not allowed")

    user = get_user_by_id(user_id)
    if user:
        ttl_cache.set(user.biz_id, DotDict(model_to_dict(user)))
        return common_success(create_jwt(user.__data__))


@router.get("/test/cache")
async def test_cache(request: Request, session_id, user_id):
    # http://127.0.0.1:8000/api/v1/test/cache?session_id=sess_xxxxxx&user_id=user_xxxxxx
    if not settings.APP_DEBUG:
        return common_error("not allowed")

    await session_create({"id": session_id, "user_id": user_id})
    return common_success({"msg": "success"})


async def session_end(data):
    user_id = data.get("user_id")
    if user_id in ttl_cache:
        ttl_cache.delete(user_id)
    else:
        logger.info(f"User ID {user_id} not found in cache")
    pass


async def session_create(data):
    """
    创建session
    :param data:
    :return:
    """
    user_id = data.get("user_id")
    user = get_user_by_id(user_id)
    if user:
        record = model_to_dict(user)
        if "extra_info" in record:
            try:
                record["extra_info_json"] = json.loads(record["extra_info"])
            except json.JSONDecodeError:
                record["extra_info_json"] = {}
        else:
            record["extra_info_json"] = {}
        ttl_cache.set(data.get("id"), DotDict(record))
    else:
        logger.exception("user not found")
    pass


async def user_created(data):
    """
    创建用户
    :param data:
    :return:
    """
    email_address = data.get("email_addresses")[0]
    # 如果username为空则 使用姓+名
    UserPO.create(
        biz_id=data.get("id"),
        email=email_address.get("email_address"),
        name=data.get("username") or data.get("last_name") + data.get("first_name"),
        google_open_id=data.get("primary_email_address_id"),
    )
    # 如果是测试用户，赠送一个会员
    if email_address in beta_test_user_email:
        update_membership(data.get("id"), ProductID.SUBSCRIBE_STANDARD, "100积分")


async def user_updated(data):
    """
    更新用户
    :param data:
    :return:
    """
    email_address = data.get("email_addresses")[0]
    update_user(
        UserPO(
            biz_id=data.get("id"),
            email=email_address.get("email_address"),
            name=data.get("username") or data.get("last_name") + data.get("first_name"),
            google_open_id=data.get("primary_email_address_id"),
            mobile=len(data.get("phone_numbers")) > 0 and data.get("phone_numbers")[0],
        )
    )

    pass


async def user_deleted(data):
    # 如果username为空则 使用姓+名
    await delete_user(UserPO(biz_id=data.get("id")))
    pass
