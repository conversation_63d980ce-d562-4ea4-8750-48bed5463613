import os
from typing import Optional

import httpx
from fastapi import APIRouter, Depends, HTTPException

from src.constant.wanpan import ALI_WANPAN_DOWNLOAD_URL_HOST, ALI_WANPAN_REDIS_PREFIX
from src.logger.logUtil import get_logger
from src.util.cacheUtil import open_api_cache
from src.util.commonutil import common_error, common_success, common_third_error
from src.util.TokenUtil import get_current_user
from src.util.third_party import box_ali

router = APIRouter(prefix="/api/v1/user/ali_wanpan", tags=["Login"])

logger = get_logger(__name__)

# 百度开放平台相关配置
ALI_CLIENT_ID = os.environ.get("ALI_CLIENT_ID", "********************************")
ALI_CLIENT_SECRET = os.environ.get("ALI_CLIENT_SECRET", "3d134e49a18c41fcb661184d356da307")


@router.get("/auth/access_token", summary="阿里授权")
async def auth_callback(code: str, _=Depends(get_current_user)):
    # 1. 通过 code 获取 access token
    url = "https://openapi.alipan.com/oauth/access_token"
    data = {
        "client_id": ALI_CLIENT_ID,
        "client_secret": ALI_CLIENT_SECRET,
        "grant_type": "authorization_code",
        "code": code,
    }
    async with httpx.AsyncClient() as client:
        token_resp = await client.post(url, json=data)
    if token_resp.status_code != 200:
        raise HTTPException(status_code=token_resp.status_code, detail="获取 access token 失败")
    token_data = token_resp.json()
    access_token = token_data.get("access_token")
    if not access_token:
        raise HTTPException(status_code=400, detail=token_data.get("message"))
    return common_success(data=token_data)


@router.get("/home", summary="阿里授权首页")
async def uinfo_home(access_token: str, _=Depends(get_current_user)):
    success, drive_info = await box_ali.get_user_drive(access_token)
    if not success:
        if str(drive_info) == "401":
            return common_third_error("获取 drive info 失败")
        return common_error(message=drive_info)

    backup_drive_id = drive_info.get("backup_drive_id")
    resource_drive_id = drive_info.get("resource_drive_id")
    pan_list = list()
    if backup_drive_id:
        pan_list.append({
            "name": "备份盘",
            "drive_id": backup_drive_id,
            "parent_file_id": "root",
            "mark": "backup"
        })
    if resource_drive_id:
        pan_list.append({
            "name": "资源盘",
            "drive_id": resource_drive_id,
            "parent_file_id": "root",
            "mark": "resource"
        })
    drive_info["root"] = pan_list
    return common_success(data=drive_info)


@router.get("/directory", summary="阿里文件目录")
async def uinfo_directory(
        access_token: str,
        drive_id: str,
        parent_file_id: Optional[str] = "root",
        marker: Optional[str] = "",
        _=Depends(get_current_user)):
    success, drive_info = await box_ali.get_user_drive(access_token)
    if not success:
        return common_error(message=drive_info)
    backup_drive_id = drive_info.get("backup_drive_id")
    resource_drive_id = drive_info.get("resource_drive_id")
    if drive_id is None:
        return common_error("请选择授权盘")
    if drive_id!=resource_drive_id and drive_id != backup_drive_id:
        return common_error("没有授权盘")
    success, list_info = await box_ali.get_directory_file_list(access_token, drive_id, parent_file_id, marker)
    if not success:
        return common_error(message=drive_info)

    success, vip = await box_ali.get_user_vip(access_token)
    if not success:
        return common_error(message=vip)
    result = {"uinfo": drive_info, "listall": list_info, "vip": vip}
    return common_success(data=result)


@router.get("/list", include_in_schema=False)
async def uinfo_and_list(
        access_token: str, dir: Optional[str] = "root", marker: Optional[str] = "", _=Depends(get_current_user)
):
    drive_url = "https://openapi.alipan.com/adrive/v1.0/user/getDriveInfo"
    headers = {"Authorization": f"Bearer {access_token}", "Content-Type": "application/json"}
    async with httpx.AsyncClient() as client:
        drive_resp = await client.post(drive_url, headers=headers)
    if drive_resp.status_code != 200:
        # return HTTPException(status_code=drive_resp.status_code, detail="获取 drive info 失败")
        return common_third_error("获取 drive info 失败")
        # return {"code":10001,"message":"获取 drive info 失败"}
    drive_info = drive_resp.json()
    if drive_info.get("code"):
        return common_error(message=drive_info.get("message"))
    list_url = "https://openapi.alipan.com/adrive/v1.0/openFile/list"
    data = {
        "drive_id": drive_info.get("resource_drive_id"),
        "parent_file_id": dir,
        "category": "video,audio",
    }
    if marker:
        data["marker"] = marker
    async with httpx.AsyncClient() as client:
        list_resp = await client.post(list_url, headers=headers, json=data)
    if list_resp.status_code != 200:
        raise HTTPException(status_code=list_resp.status_code, detail="获取 list file 失败")
    list_info = list_resp.json()
    if list_info.get("code"):
        return common_error(message=list_info.get("message"))
    vi, vip = await box_ali.get_user_vip(access_token)
    result = {"uinfo": drive_info, "listall": list_info, "vip": vip}
    return common_success(data=result)


@router.get("/file_download_url")
async def file_download_url(access_token: str, drive_id: str, file_id: str, _=Depends(get_current_user)):
    baseinfo_url = "https://openapi.alipan.com/adrive/v1.0/openFile/get"
    headers = {"Authorization": f"Bearer {access_token}", "Content-Type": "application/json"}
    data = {
        "drive_id": drive_id,
        "file_id": file_id,
    }
    async with httpx.AsyncClient() as client:
        base = await client.post(baseinfo_url, headers=headers, json=data)
    if base.status_code != 200:
        raise HTTPException(status_code=base.status_code, detail="获取 file_download_url 失败")
    base_info = base.json()
    if base_info.get("code"):
        return common_error(message=base_info.get("message"))
    url = "https://openapi.alipan.com/adrive/v1.0/openFile/getDownloadUrl"
    async with httpx.AsyncClient() as client:
        resp = await client.post(url, headers=headers, json=data)
    if resp.status_code != 200:
        raise HTTPException(status_code=resp.status_code, detail="获取 file_download_url 失败")
    file_data = resp.json()
    if file_data.get("code"):
        return common_error(message=file_data.get("message"))
    base_info.update(file_data)
    download_url = file_data.get("url")
    if download_url:
        base_info["download_url"] = download_url
        open_api_cache.set(f"{ALI_WANPAN_REDIS_PREFIX}{drive_id}_{file_id}", base_info)
        return common_success(
            data={
                "url": f"{ALI_WANPAN_DOWNLOAD_URL_HOST}?drive_id={drive_id}&file_id={file_id}",
                "download_url": download_url,
                "method": base_info.get("method"),
                "expiration": base_info.get("expiration"),
            }
        )
    else:
        return common_error("生成 download_url 失败")
