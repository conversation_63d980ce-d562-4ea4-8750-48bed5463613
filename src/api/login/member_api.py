import random
import string

from fastapi import APIRouter, Depends, Request
from playhouse.shortcuts import model_to_dict
from pydantic import BaseModel

from config import settings
from src.model.member_manager import MembershipType, get_lecture_member_by_user_id, verify_user_account
from src.model.order_manager import parse_option_points, update_membership, update_points
from src.model.User_action_record_manager import (
    UserActionProcess,
    UserActionType,
    add_user_action_record,
    get_user_action_record,
)
from src.model.oplatform_manager import OpenPlatform

from src.model.user_manager import check_is_admin
from src.util.commonutil import common_error, common_success
from src.util.TokenUtil import get_current_user
from src.services.user_service import member_forever

router = APIRouter(prefix="/api/v1/member")


@router.get("/profile", summary="会员信息")
async def profile(user=Depends(get_current_user)):
    member = get_lecture_member_by_user_id(user.biz_id)
    if member_forever(member):
        member.membership_type = -1
    return common_success(data=model_to_dict(member))


@router.get("/records")
async def records(page_size: int = 10, page_no: int = 1, user=Depends(get_current_user)):
    count, members = await get_user_action_record(user.biz_id, page_size, page_no)
    return common_success(data={"records": [member.__data__ for member in members], "total": count})


@router.get("/verify/upload")
async def upload_count(user=Depends(get_current_user)):
    """
    上传次数校验
    :param request:
    :return: 用户信息
    """
    verify_flag = verify_user_account(user.biz_id)

    if not verify_flag:
        return common_error("VIP用户才能使用上传解析哦")

    return common_success()


@router.post("/charge/free/{user_id}")
async def free_charge(user_id: str, product_id: int = 1, user=Depends(get_current_user)):
    """
    免费赠送会员
    :param user_id:
    :param request:
    :return: 用户信息
    """
    customers = settings.CUSTOMER_SERVICE_SEND.split(",")
    if user.biz_id not in customers:
        return common_error("非法操作")

    if MembershipType.VIP == product_id:
        free_points = "2800积分"
    else:
        free_points = "1400积分"

    update_membership(user_id, product_id, free_points)

    add_user_action_record(
        user_id,
        UserActionType.FREE_CHARGE,
        UserActionProcess.ADD,
        "赠送会员",
        parse_option_points(free_points),
    )

    return common_success()


@router.get("/charge/free/{user_id}/{points}")
async def free_points(request: Request, user_id: str, points: int):
    if not settings.APP_DEBUG:
        return common_error("not allowed")

    update_points(user_id, f"{points}积分")

    add_user_action_record(
        user_id,
        UserActionType.FREE_CHARGE,
        UserActionProcess.ADD,
        "内测赠送",
        points,
    )

    return common_success()


@router.get("/charge/vip/{user_id}/{days}")
async def free_vip(request: Request, user_id: str, days: int):
    if not settings.APP_DEBUG:
        return common_error("not allowed")

    update_membership(user_id, 2, f"{days}天")

    # add_user_action_record(user_id,
    #                        UserActionType.FREE_CHARGE,
    #                        UserActionProcess.ADD,
    #                        "内测会员",
    #                        0,
    #                        )

    return common_success()


class PointActionRequest(BaseModel):
    points: int
    action: str = "ADD"  # 暂时默认为添加


@router.post("/{user_id}/points")
async def charge_points(user_id: str, point_action_request: PointActionRequest, user=Depends(get_current_user)):
    """
    给用户添加 points
    """
    if not check_is_admin(user.biz_id):
        return common_error("非授权用户，无法操作")
    member = get_lecture_member_by_user_id(user_id)
    if not member:
        return common_error(f"用户 {user_id} 不存在")

    point_text = f"{point_action_request.points}积分"
    update_points(user_id, point_text)

    add_user_action_record(
        user_id,
        UserActionType.ADD_POINTS,
        UserActionProcess.ADD,
        "添加积分",
        parse_option_points(point_text),
    )
    return common_success(f"用户 {user_id} 添加 {point_text} 成功")


class OpenPlatForm(BaseModel):
    name :str
    daily_limit:int
    points:int

def generate_random_string(length: int, unique_chars: bool = True):
    characters = string.ascii_letters + string.digits  # 包含大小写字母和数字
    if unique_chars:
        if length > len(characters):
            raise ValueError("Length exceeds the number of unique characters.")
        return ''.join(random.sample(characters, length))  # 不重复字符
    else:
        return ''.join(random.choices(characters, k=length))  # 允许重复字符

# 添加开放平台 app_key
@router.post("/add_key")
async def add_open_plform_key(openPlatForm:OpenPlatForm):

    # 生成 app_key 和 secret_key
    app_key = generate_random_string(10, unique_chars=True)
    secret_key = generate_random_string(16, unique_chars=True)

    open_plat_form = OpenPlatform.select().where(OpenPlatform.app_key == app_key, OpenPlatform.delete_flag == 0)
    if open_plat_form:
        return common_error("生成的 app_key 已存在，请重试")
    OpenPlatform.create(
        name=openPlatForm.name,
        app_key=app_key,
        secret_key=secret_key,
        daily_limit=openPlatForm.daily_limit,
        points=openPlatForm.points
    )
    return common_success({"app_key":app_key,"secret_key":secret_key})