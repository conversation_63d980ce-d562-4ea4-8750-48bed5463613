import os
from typing import Optional
from urllib.parse import quote

import httpx
from fastapi import APIRouter, Depends, HTTPException

from src.constant.wanpan import BAIDU_WANPAN_DOWNLOAD_URL_HOST, BAIDU_WANPAN_REDIS_PREFIX
from src.util.TokenUtil import get_current_user
from src.util.cacheUtil import open_api_cache
from src.util.commonutil import common_error, common_success, common_third_error

router = APIRouter(prefix="/api/v1/user/baidu_wanpan", tags=["Login"])


# 百度开放平台相关配置
BAIDU_CLIENT_ID = os.environ.get("BAIDU_CLIENT_ID", "")
BAIDU_CLIENT_SECRET = os.environ.get("BAIDU_CLIENT_SECRET", "your_client_secret")
REDIRECT_URI = os.environ.get(
    "REDIRECT_URI",
    "https://readlecture-web-server-production-7e01.up.railway.app/api/v1/user/baidu_wanpan/auth/callback",
)
AppKey = "hD4pWBFwseKbKUzpfKCMQvUVL4r7X0p4"
AppID = "118088828"
Secretkey = "6sjuRnH6dBCt8LP1lIyacFZBod8U5wd7"


@router.get("/auth/access_token")
async def auth_callback(code: str, _=Depends(get_current_user)):
    # 1. 通过 code 获取 access token
    url = f"https://openapi.baidu.com/oauth/2.0/token?grant_type=authorization_code&code={code}&client_id={AppKey}&client_secret={Secretkey}&redirect_uri=https%3A%2F%2Faihaoji.com%3Fsource%3Dbaidu"
    headers = {"User-Agent": "pan.baidu.com"}
    async with httpx.AsyncClient() as client:
        token_resp = await client.get(url, headers=headers)
    if token_resp.status_code != 200:
        raise HTTPException(status_code=token_resp.status_code, detail="获取 access token 失败")
    token_data = token_resp.json()
    access_token = token_data.get("access_token")
    if not access_token:
        raise HTTPException(status_code=400, detail="未返回 access token")
    return common_success(data=token_data)


def filter_audio_video(listall: Optional[dict] = None) -> dict:
    if listall is None:
        listall = {}
    need_items = []
    for item in listall.get("list") or []:
        if item.get("isdir"):
            need_items.append(item)
        if item.get("category") in [1, 2]:
            need_items.append(item)
    listall["list"] = need_items
    return listall


@router.get("/list")
async def uinfo_and_list(access_token: str, dir: str = "/", _=Depends(get_current_user)):
    uinfo_url = f"https://pan.baidu.com/rest/2.0/xpan/nas?access_token={access_token}&method=uinfo&vip_version=v2"
    async with httpx.AsyncClient() as client:
        resp = await client.get(uinfo_url)
    if resp.status_code != 200:
        # raise HTTPException(status_code=resp.status_code, detail="获取 user_info 失败")
        return common_third_error("获取 user_info 失败")
    uinfo = resp.json()
    if uinfo.get("errno") != 0:
        return common_error(message=uinfo.get("errmsg"))
    quote_dir = quote(dir, safe="-_.!~*'()")
    list_url = (
        f"https://pan.baidu.com/rest/2.0/xpan/file?method=list&dir={quote_dir}&access_token={access_token}&start=0"
    )
    async with httpx.AsyncClient() as client:
        resp2 = await client.get(list_url)
    if resp2.status_code != 200:
        raise HTTPException(status_code=resp2.status_code, detail="获取 list file 失败")
    listall = resp2.json()
    if listall.get("errno") != 0:
        return common_error(message=listall.get("errmsg"))
    result = {
        "uinfo": uinfo,
        "listall": filter_audio_video(listall),
    }
    return common_success(data=result)


@router.get("/file_download_url")
async def file_download_url(access_token: str, fs_id: int, _=Depends(get_current_user)):
    url = f"https://pan.baidu.com/rest/2.0/xpan/multimedia?method=filemetas&access_token={access_token}&fsids=%5B{fs_id}%5D&thumb=1&dlink=1&extra=1&needmedia=1&detail=1"
    async with httpx.AsyncClient() as client:
        resp = await client.get(url)
    if resp.status_code != 200:
        raise HTTPException(status_code=resp.status_code, detail="获取 file_download_url 失败")
    data = resp.json()
    if data.get("errno") != 0:
        return common_error(message=data.get("errmsg"))
    file_info = data.get("list")[0]
    dlink = file_info.get("dlink")
    if dlink:
        file_info["download_url"] = dlink
        file_info["access_token"] = access_token
        open_api_cache.set(f"{BAIDU_WANPAN_REDIS_PREFIX}{fs_id}", file_info)
        return common_success(
            data={
                "url": f"{BAIDU_WANPAN_DOWNLOAD_URL_HOST}?fs_id={fs_id}",
                "download_url": dlink,
            }
        )
    else:
        return common_error("生成 download_url 失败")
