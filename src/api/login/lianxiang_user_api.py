from typing import Optional

from fastapi import APIRouter, Request
from pydantic import BaseModel, Field

from src.api.login.sms_login import INIT_EMAIL, INIT_PASSWORD, get_verification_code
from src.database import enums, constants
from src.model.user_manager import UserPO, hash_password, verify_password
from src.services.user_service import re_enable_account, generate_user_biz_id
from src.util.TokenUtil import create_jwt
from src.util.cacheUtil import DotDict, ttl_cache
from src.util.commonutil import CommonResponse, common_error, common_success, common_enum_error, ErrorCodeEnum
from src.util.stringify import is_email, is_phone

router = APIRouter(prefix="/api/v1/lianxiang_user")


class LianxiangLoginUserVO(BaseModel):
    email: Optional[str] = Field(None, min_length=3, max_length=50)
    username: Optional[str] = Field(None, min_length=3, max_length=50)
    password: Optional[str] = Field(None, min_length=8)
    login_type: int = Field(..., description="登录类型：1=邮箱+密码, 2=手机+验证码")
    mobile: Optional[str] = Field(None, min_length=11, max_length=11)
    sms_code: Optional[str] = Field(None, title="手机短信验证码", description="用户的手机短信验证码")


@router.post("/login", response_model=CommonResponse, summary="联想客户端登录")
async def login(request: Request, user: LianxiangLoginUserVO):
    if user.login_type == 1:
        username = user.username or user.email
        if username is None or user.password is None:
            return common_error("please provide email and password")

        if not is_email(username):
            return common_enum_error(ErrorCodeEnum.EMAIL_ERROR)

        if username.lower() == "undefined":
            return common_enum_error(ErrorCodeEnum.EMAIL_ERROR)
        conditions = [
            UserPO.email == username,
            ((UserPO.delete_flag == 0) | (UserPO.delete_flag == 2))
        ]
        user_po = UserPO.select().where(*conditions).first()
        if not user_po:
            return common_error("user not exist")

        if not user_po.mail_access:
            return common_error("please verify email first")

        if not verify_password(user.password, user_po.password):
            return common_error("password error")

        # 验证通过再取消注销
        await re_enable_account(user_po, username)

        token = create_jwt(user_po.__data__)
        ttl_cache.set(user_po.biz_id, DotDict(user_po.__data__))
        return common_success(
            {"jwtToken": token, "head_img": user_po.head_img, "user_id": user_po.biz_id, "name": user_po.name}
        )
    elif user.login_type == 2:
        # 短信验证码登录
        if user.mobile is None or user.sms_code is None:
            return common_error("please provide email and password")

        if not is_phone(user.mobile):
            return common_enum_error(ErrorCodeEnum.MOBILE_ERROR)
        # 获取缓存中的验证码
        cached_code = get_verification_code(user.mobile)
        if not cached_code:
            return common_error("验证码已过期或未发送")
        if cached_code != user.sms_code:
            return common_error("验证码错误")

        user_po = UserPO.select().where(
            (UserPO.mobile == user.mobile) &
            ((UserPO.delete_flag == 0) | (UserPO.delete_flag == 2))
        ).first()
        if not user_po:
            source = request.headers.get(constants.HEADER_SOURCE, enums.SourceEnum.UNKNOWN.value)
            user_po = UserPO.create(
                biz_id=generate_user_biz_id(),
                name=user.mobile,
                mobile=user.mobile,
                password=hash_password(INIT_PASSWORD),
                email=INIT_EMAIL,
                source=source
            )
        else:
            await re_enable_account(user_po, user.mobile)
        token = create_jwt(user_po.__data__)
        ttl_cache.set(user_po.biz_id, DotDict(user_po.__data__))
        return common_success(
            {"jwtToken": token, "user_id": user_po.biz_id, "head_img": user_po.head_img, "name": user_po.name}
        )
    else:
        return common_error(f"login_type: {user.login_type} 不支持")
