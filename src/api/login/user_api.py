import random
import time
from uuid import UUID

from fastapi import APIRouter, Request, Response, Depends
from fastapi.responses import RedirectResponse
from pydantic import BaseModel, Field

from config import settings
from src.database import enums, constants
from src.logger.logUtil import get_logger
from src.mail.send_mail import send_verify_code_mail, send_verify_link_mail
from src.model.user_manager import (
    LoginUserVO,
    UserPO,
    UserVO,
    decode_token,
    generate_token,
    get_user_by_email,
    get_user_by_id,
    hash_password,
    update_user,
    verify_password,
    get_user_by_mobile
)
from src.services.user_service import re_enable_account
from src.services.wechat_service import (
    direct_access_token_by_code
)
from src.util.TokenUtil import create_jwt, get_6_random_mail_code
from src.util.TokenUtil import get_current_user
from src.util.cacheUtil import DotDict, open_api_cache, ttl_cache
from src.util.commonutil import CommonResponse, ErrorCodeEnum, common_enum_error, common_error, common_success
from src.util.dateUtil import get_beijing_time
from src.util.extra_info_util import record_user_extra_info
from src.util.stringify import is_email, is_phone

logger = get_logger(__name__)

router = APIRouter(prefix="/api/v1/user", tags=["Login"])


class UserEditSchema(BaseModel):
    name: str = Field(default=None, min_length=3, max_length=50, title="姓名", description="用户的姓名")
    head_img: str = Field(default=None)


# 更新用户头像和昵称
@router.post("/edit", response_model=CommonResponse, summary="更新用户头像和昵称")
def edit_user(user1: UserEditSchema, user=Depends(get_current_user)):
    if user1.name:
        query = UserPO.update(name=user1.name).where(UserPO.biz_id == user.biz_id)
        query.execute()
    if user1.head_img:
        query = UserPO.update(head_img=user1.head_img).where(UserPO.biz_id == user.biz_id)
        query.execute()
    return common_success("更新成功..")


@router.post("/register/mail/code", response_model=CommonResponse, summary="邮箱验证码注册用户")
async def create_user_mail_code(request: Request, user: UserVO):
    """
    创建用户
    :param request
    :param user:
    :return:
    """
    # return common_error("暂不支持邮箱注册")
    if not user.email_code:
        return common_error(code=ErrorCodeEnum.PARAMS_ERROR.error_code, message="验证码不能为空")

    if not user.email or not user.password:
        return common_enum_error(ErrorCodeEnum.NOT_FOUND)
    check_user = get_user_by_email(user.email)
    if check_user and check_user.mail_access:
        return common_enum_error(ErrorCodeEnum.USER_EXIST)

    if not check_user:
        user.biz_id = "user_" + str(UUID(int=random.getrandbits(128)))
        user.password = hash_password(user.password)
        extra_info = user.__dict__.pop("extra_info")
        source = request.headers.get(constants.HEADER_SOURCE, enums.SourceEnum.UNKNOWN.value)
        user_dict = user.__dict__
        user_dict["source"] = source
        check_user = UserPO.create(**user_dict)
        # 记录注册时 utm_* 字段信息
        extra_info_json = None
        if extra_info:
            extra_info_json = extra_info.model_dump()
        record_user_extra_info(check_user, utm_source=user.utm_source, extra_info=extra_info_json)
    else:
        if user.password:
            check_user.password = hash_password(user.password)
        if user.name:
            check_user.name = user.name

    # 创建用户
    try:
        # 异步执行，发送验证邮件
        token = get_6_random_mail_code(user.email)
        if token and token == user.email_code:
            check_user.mail_access = True
            check_user.save()
            token = create_jwt(check_user.__data__)
            ttl_cache.set(check_user.biz_id, DotDict(check_user.__data__))
            return common_success(data={"jwtToken": token})
        else:
            return common_error("验证码错误")
    except Exception:
        logger.exception("create user occur error")
        # 创建session
        return common_error(message="create user error")


@router.post("/register/mail", response_model=CommonResponse, summary="通过邮箱-创建用户")
async def create_user_mail(request: Request, user: UserVO):
    """
    创建用户
    :param request
    :param user:
    :return:
    """
    if not user.email or not user.password:
        return common_enum_error(ErrorCodeEnum.NOT_FOUND)
    check_user = get_user_by_email(user.email)
    if check_user and check_user.mail_access:
        return common_enum_error(ErrorCodeEnum.USER_EXIST)

    if not check_user:
        user.biz_id = "user_" + str(UUID(int=random.getrandbits(128)))
        user.password = hash_password(user.password)
        source = request.headers.get(constants.HEADER_SOURCE, enums.SourceEnum.UNKNOWN.value)
        user_dict = user.__dict__
        user_dict["source"] = source
        check_user = UserPO.create(**user_dict)
    else:
        if user.password:
            check_user.password = hash_password(user.password)
        if user.name:
            check_user.name = user.name

    # 创建用户
    try:
        # 异步执行，发送验证邮件
        token = generate_token(check_user)
        verify_email_url = settings.DOMAIN + "api/v1/user/mail/bind?token=" + token
        send_verify_link_mail(user.email, check_user.name, verify_email_url)
        return common_success(message="请点击邮件内容以完成邮箱验证")
    except Exception:
        logger.exception("create user occur error")
        # 创建session
        return common_error(message="create user error")


@router.post("/reset/mail", response_model=CommonResponse)
async def reset_mail(request: Request, user: UserVO):
    """
    创建用户
    :param request
    :param user:
    :return:
    """
    if not user.email or not user.password:
        return common_enum_error(ErrorCodeEnum.NOT_FOUND)
    check_user = get_user_by_email(user.email)

    if not check_user:
        user.biz_id = "user_" + str(UUID(int=random.getrandbits(128)))
        user.password = hash_password(user.password)
        source = request.headers.get(constants.HEADER_SOURCE, enums.SourceEnum.UNKNOWN.value)
        user_dict = user.__dict__
        user_dict["source"] = source
        check_user = UserPO.create(**user_dict)
    else:
        if user.password:
            check_user.password = hash_password(user.password)
        if user.name:
            check_user.name = user.name

    # 创建用户
    try:
        # 异步执行，发送验证邮件
        token = generate_token(check_user)
        verify_email_url = settings.DOMAIN + "api/v1/user/mail/bind?token=" + token
        send_verify_link_mail(user.email, check_user.name, verify_email_url)
        return common_success(message="请点击邮件内容以完成邮箱验证")
    except Exception:
        logger.exception("create user occur error")
        # 创建session
        return common_error(message="create user error")


@router.get("/mail/bind")
async def bind_mail(token: str, response: Response):
    """
    验证邮箱
    :param response: 邮箱
    :param token: token 10分钟有效
    :return:
    """

    payload = decode_token(token)
    if not payload:
        return common_enum_error(ErrorCodeEnum.ERROR_TOKEN_CHECK)

    po = get_user_by_email(payload["sub"])

    if not po:
        return common_enum_error(ErrorCodeEnum.USER_EXIST)

    # 更新是否有验证邮箱
    po.mail_access = True
    po.password = payload.get("password")
    if update_user(po):
        # 验证成功，跳转到首页
        response.set_cookie("jwtToken", create_jwt(po.__data__))
        return RedirectResponse(url="/", status_code=303)
    else:
        return common_error("error verify email")


@router.get("/mail/send", summary="发送邮件验证码")
async def verify_email(email: str):
    """
    发送邮件验证码
    :param email: 邮箱
    :return:
    """
    if not email:
        return common_enum_error(ErrorCodeEnum.FAILED)

    token = get_6_random_mail_code(email)

    send_verify_code_mail(email, email, token)

    return common_success()


@router.get("/login/wxToken")
async def verify_wx_token(wx_token: str, response: Response, request: Request):
    """
    验证微信token,并
    :param request:
    :param response:
    :param wx_token: wx
    :return:
    """
    biz_id = open_api_cache.get("readlecture:wx_token:" + wx_token)
    if not biz_id:
        return common_enum_error(ErrorCodeEnum.ERROR_TOKEN_CHECK)
    if biz_id == "NEED_BIND_WECHAT_PUBLIC_ACCOUNT":
        return common_enum_error(ErrorCodeEnum.NEED_BIND_WECHAT_PUBLIC_ACCOUNT)
    user = get_user_by_id(biz_id)
    if not user:
        return common_error("user not exist")
    token = create_jwt(user.__data__)
    try:
        open_api_cache.delete("readlecture:wx_token:" + wx_token)
    except Exception as e:
        logger.error("pop cache error", e)
    response.set_cookie("jwtToken", token)
    # 筛选出 utm_* 开头的字段并记录
    all_params = dict(request.query_params)
    utm_params = {k: v for k, v in all_params.items() if k.startswith("utm_")}
    record_user_extra_info(user, extra_info=utm_params)
    return common_success({"jwtToken": token})


@router.post("/login", response_model=CommonResponse, summary="用户密码登录")
async def login(user: LoginUserVO):
    # 兼容原参数email，待前端发版后可以移除
    username = user.username or user.email
    if username is None or user.password is None:
        return common_error("please provide email and password")

    conditions = [
        ((UserPO.delete_flag == 0) | (UserPO.delete_flag == 2))
    ]
    username_is_email = False
    if is_email(username):
        conditions.append(UserPO.email == username)
        username_is_email = True
    elif is_phone(username):
        conditions.append(UserPO.mobile == username)
    else:
        return common_error("支持邮箱和手机号登录")

    user_po = UserPO.select().where(*conditions).first()
    if not user_po:
        return common_error("user not exist")

    if username_is_email and not user_po.mail_access:
        return common_error("please verify email first")

    if not verify_password(user.password, user_po.password):
        return common_error("password error")
    # 验证通过再取消注销
    await re_enable_account(user_po, username)

    token = create_jwt(user_po.__data__)
    ttl_cache.set(user_po.biz_id, DotDict(user_po.__data__))
    return common_success({"jwtToken": token})


# 验证手机验证码
def get_verification_code(phone: str):
    data = ttl_cache.get(phone) or {}
    if not data:
        return None
    code, expiry_time = data.get("code"), data.get("expiry_time") or 0
    if time.time() > expiry_time:  # 验证码过期
        ttl_cache.delete(phone)
        return None
    return code


# # 清理user 及user下关联的数据
# def cleanup_user_data(biz_id: str):
#     # 删除用户及其相关联的数据
#     user = get_user_by_id(biz_id)
#     if user:
#         # 删除用户任务
#         LectureTask.update(delete_flag=1).where(LectureTask.user_id == biz_id).execute()
#         # 删除用户文件夹
#         Folder.update(delete_flag=1).where(Folder.user_id == biz_id).execute()
#         # 删除用户文章
#         # LectureArticle.update(delete_flag=1).where(LectureArticle.user_id == biz_id).execute()
#         # 删除用户文章详情
#         LectureArticleDetail.update(delete_flag=1).where(LectureArticleDetail.user_id == biz_id).execute()
#         # 删除用户
#         UserPO.update(delete_flag=1).where(UserPO.biz_id == biz_id).execute()
#         ttl_cache.delete(biz_id)


@router.get("/off_wx_user/wxToken")
async def verify_off_wx_token(wx_token: str, request: Request):
    """
    验证微信token,关闭微信用户轮训链接
    :param request:
    :param wx_token: wx
    :return:
    """
    biz_id = open_api_cache.get("readlecture:wx_token:" + wx_token)
    if not biz_id:
        return common_enum_error(ErrorCodeEnum.ERROR_TOKEN_CHECK)
    if biz_id == "NEED_BIND_WECHAT_PUBLIC_ACCOUNT":
        return common_enum_error(ErrorCodeEnum.NEED_BIND_WECHAT_PUBLIC_ACCOUNT)
    user = get_user_by_id(biz_id)
    if not user:
        return common_error("user not exist")
    # 删除缓存
    try:
        open_api_cache.delete("readlecture:wx_token:" + wx_token)
    except Exception as e:
        logger.error("pop cache error", e)
    return common_success("用户认证成功")


"""
    1.手机号注销需要先调用/api/v1/user/mobile/send_sms接口发送短信验证码验证，验证通过执行注销流程
    2.注销邮箱账户暂时不需要邮箱验证  如不需要邮箱校验注销掉第一个判断，并且不需要触发邮箱验证码接口
"""


@router.post("/off_user", response_model=CommonResponse, summary="注销账号")
def off_user(user: UserVO, request: Request, current_user=Depends(get_current_user)):
    # 手机号账号注销
    if user.user_type == 1:
        msg = '手机号'
        phone_number = user.mobile
        user_code = user.sms_code
        # 获取缓存中的验证码
        cached_code = get_verification_code(phone_number)
        if not cached_code:
            return common_error("验证码已过期或未发送")
        if cached_code != user_code:
            return common_error("验证码错误")
        target_user = get_user_by_mobile(phone_number)

    elif user.user_type == 2:
        msg = '微信账号'
        source = request.headers.get("source", "")
        token = direct_access_token_by_code(source, user.code)
        if not token:
            return common_error("error token check.please try again later")

        wx_unionid = token.get('unionid')
        target_user = UserPO.get_or_none(wx_unionid=wx_unionid, delete_flag=0)

    elif user.user_type == 3:
        msg = '邮箱账户'
        target_user = get_user_by_email(user.email)

    else:
        return common_enum_error(ErrorCodeEnum.PARAMS_ERROR)

    if not target_user:
        return common_enum_error(ErrorCodeEnum.UNKNOWN_USER)
    elif target_user.biz_id != current_user.biz_id:
        return common_error("与当前用户不匹配")
    else:
        target_user.delete_flag = 2
        target_user.deletion_request_time = get_beijing_time()
        target_user.save()
        return common_success(f"{msg}注销流程已申请")


"""
    设置密码 or 更改密码
    1.修改密码必须优先发送短信验证码接口进行真人验证
    2.验证通过，拿到user_id判断用户是首次设置密码还是修改密码。首次设置密码只需传new_password即可。如果基于旧密码更改新的密码，需将旧密码传过来验证，然后改成新的密码
"""


@router.post("/setOrUpdatePassWord", response_model=CommonResponse, summary="更新密码")
def set_or_update_password(user: UserVO, user1=Depends(get_current_user)):
    # 1。短信验证码真人验证
    if user.mobile:
        phone_number = user.mobile
        user_code = user.sms_code
        # 获取缓存中的验证码
        cached_code = get_verification_code(phone_number)
        if not cached_code:
            return common_error("验证码已过期或未发送")
        if cached_code != user_code:
            return common_error("验证码错误")
        if user1.mobile != user.mobile:
            return common_error("手机号与当前用户手机号不匹配")
    elif user.email:
        email = user.email
        email_code = user.email_code
        if not email_code:
            return common_error(code=ErrorCodeEnum.PARAMS_ERROR.error_code, message="验证码不能为空")
        token = get_6_random_mail_code(email)
        if token and token != user.email_code:
            return common_error("邮箱验证码错误")
        if user1.email != user.email:
            return common_error("验证邮箱与当前用户邮箱不匹配")
    else:
        return common_error("支持邮箱或手机号修改")
    user_info = get_user_by_id(user1.biz_id)
    if not user_info:
        return common_error("用户不存在")

    # 新密码
    new_password = hash_password(user.password)
    user_info.password = new_password
    user_info.save()
    return common_success("设置密码成功")


# 解绑手机号
@router.post("/mobile/unbind", response_model=CommonResponse, summary="解绑手机号")
def unbind_mobile(user: UserVO, user1=Depends(get_current_user)):
    phone_number = user.mobile
    user_code = user.sms_code
    # 获取缓存中的验证码
    cached_code = get_verification_code(phone_number)
    if not cached_code:
        return common_error("验证码已过期或未发送")
    if cached_code != user_code:
        return common_error("验证码错误")
    user_info = get_user_by_id(user1.biz_id)
    # 已有密码的先验证旧密码，然后旧密码匹配，更新为新的密码
    if not user_info.mobile:
        return common_error("当前用户没有手机号，无须操作")
    counts = (
        UserPO.update(
            mobile=None,
        )
        .where(UserPO.biz_id == user1.biz_id)
        .execute()
    )
    if counts:
        return common_success("解绑手机号成功")
    else:
        return common_error("解绑手机号失败")


# 解绑邮箱
@router.post("/email/unbind", response_model=CommonResponse, summary="解绑邮箱")
def unbind_email(user: UserVO, user1=Depends(get_current_user)):
    token = get_6_random_mail_code(user.email)
    if user.email_code != token:
        return common_error("邮件验证码错误")
    user_info = get_user_by_id(user1.biz_id)
    # 已有密码的先验证旧密码，然后旧密码匹配，更新为新的密码
    if not user_info.email:
        return common_error("当前用户没有邮箱，无须操作")
    counts = (
        UserPO.update(
            email=None,
        )
        .where(UserPO.biz_id == user1.biz_id)
        .execute()
    )
    if counts:
        return common_success("解绑微信成功")
    else:
        return common_error("解绑邮箱失败")
