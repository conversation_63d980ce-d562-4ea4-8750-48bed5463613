import random
import string
from urllib.parse import urljoin

from fastapi import APIRout<PERSON>, Depends, Query
from fastapi.responses import JSONResponse
from playhouse.shortcuts import model_to_dict
from pydantic import HttpUrl
from starlette.requests import Request
from starlette.responses import RedirectResponse
from wechatpy import WeChatOAuth, create_reply, parse_message
from wechatpy.events import ScanEvent, SubscribeEvent, SubscribeScanEvent, UnsubscribeEvent
from wechatpy.exceptions import InvalidSignatureException
from wechatpy.utils import check_signature

from config import settings
from src.database import enums
from src.logger.logUtil import get_logger
from src.model.order_manager import handle_wechat_notify
from src.model.user_manager import UserPO, get_user_by_id, query_user
from src.model.wechat_manager import EventVO
from src.services.user_service import generate_user_biz_id
from src.services.wechat_service import (
    get_wechat_access_token,
    get_unionid,
    direct_access_token_by_code,
    delete_access_token_cache,
    CategoryEnum
)
from src.services import track_service
from src.util.TokenUtil import get_current_user
from src.util.cacheUtil import Dot<PERSON>ict, open_api_cache, ttl_cache
from src.util.celery_util import sem_to_baidu
from src.util.commonutil import CommonResponse, ErrorCodeEnum, common_enum_error, common_error, common_success
from src.util.dateUtil import get_beijing_time
from src.util.redis_keys_util import (
    key_wechat_bind_error, key_pc_wechat_auth, key_pc_wechat_auth_notice, key_share_register
)
from src.util.wechat_oauth import WechatBaseClient, wechat_base_client

logger = get_logger(__name__)

router = APIRouter(prefix="/api/v1/wechat", tags=["wechat"])

reply_info = """等你很久了！开心！和一个有趣的灵魂相遇了！😄恭喜你找到 AI 时代办公新工具！点击AihaoJi.com！帮你加快工作效率10X倍，赶快成功升职加薪！"""
login_success = """登录成功！"""
offuser_success = """"微信账号注销流程已申请，若7日内重新登录，注销流程将自动取消!"""
reset_success = """微信账号注销流程已取消，欢迎回来！"""

"""
微信登录:
    step1: frontend GET /url for getting qrcode
    step2: wx GET /check for checking health
    step3: wx POST /check for subscribing event(scan, subscrib, unsubscrib)

微信绑定:
    step1: frontend GET /bindwx for getting url(需要转换成二维码给移动用户扫码)
    step2: wx GET /auth for giving (code and state), 通过 code 可以获取微信用户的信息, 在 /auth 里会完成用户绑定
    step3: frontend GET /profile for finding whether binded

微信授权:
    step1: frontend GET /wx-authorize for getting url(可在微信里直接打开)
    step2: wx GET /wx-authorize-notify for giving (code and state), 通过 code 可以获取微信用户的信息, 设置用户的openid
    step3: frontend GET /profile for finding whether authorized
"""


@router.get("/wx-authorize", response_model=CommonResponse)
async def wx_authorize(wx_token: str, user_id: str = None, redirect_uri: HttpUrl = Query(...)):
    """
    获取微信授权链接
    :param wx_token: 微信token
    :param user_id: 微信token
    :param redirect_uri: 微信token
    :return:
    """
    # 缓存 wx_token 的 biz_id
    open_api_cache.set("readlecture:wx_token:" + wx_token, user_id)
    open_api_cache.set("readlecture:wx_token_redirect_uri:" + wx_token, redirect_uri)

    datas = WeChatOAuth(
        app_id=settings.WECHAT_APP_ID,
        secret=settings.WECHAT_APP_SECRET,
        # 这里由于只有生产环境的微信公众号，hardcode here
        redirect_uri="https://aihaoji.com/api/v1/wechat/wx-authorize-notify",
        scope="snsapi_base",
        state=wx_token,
    )
    return common_success(data=datas.authorize_url)


@router.get("/wx-authorize-notify")
async def wx_authorize_notify(code: str, state: str):
    token = WechatBaseClient().get_wechat_oauth(state).fetch_access_token(code)
    if not token:
        return common_error("error token check.please try again later")
    logined_biz_id = open_api_cache.get("readlecture:wx_token:" + state)
    counts = (
        UserPO.update(
            wx_open_id=token["openid"],
            wx_public_access=True,
        )
        .where(UserPO.biz_id == logined_biz_id)
        .execute()
    )
    if counts:
        logger.info(f"微信授权成功，更新的数量为 {counts}, biz_id: {logined_biz_id}")
    else:
        logger.error(f"微信授权失败，更新的数量为 {counts}, biz_id: {logined_biz_id}")
    # 如果 redirect_uri 没有缓存，回到 叮当好记 公众号
    redirect_uri = open_api_cache.get("readlecture:wx_token_redirect_uri:" + state)
    open_api_cache.set("readlecture:wx_token:" + state, logined_biz_id)
    if not redirect_uri:
        redirect_uri = "https://mp.weixin.qq.com/mp/profile_ext?action=home&__biz=MzkwMjgyOTc5Mg=="
    return RedirectResponse(
        url=f"{redirect_uri}#wechat_redirect",
        status_code=303,
    )


async def check_wx_unionid(openid):
    try:
        wx_unionid, access_token = None, None
        for _ in range(3):
            access_token = get_wechat_access_token("pc")
            success, wx_unionid = get_unionid(access_token, openid)
            if success:
                break
            elif wx_unionid is not None:
                delete_access_token_cache("pc")
                continue
            else:
                break
        return wx_unionid, access_token
    except Exception:
        return None, None


@router.post("/check", summary="微信公众号事件回调")
async def check(request: Request):
    """此接口为绑定公众号服务器地址接口，处理微信公众号接收的消息，包括用户发送的消息"""
    body = await request.body()
    content = body.decode("utf-8")
    msg = parse_message(content)

    # 用户关注公众号事件
    if isinstance(msg, SubscribeEvent):
        wx_unionid, access_token = await check_wx_unionid(msg.source)
        if wx_unionid is None:
            wechat_reply = create_reply("", msg)
        else:
            user = await query_user(wx_unionid, msg.source)
            if not user:
                user = UserPO.create(
                    biz_id=generate_user_biz_id(),
                    name=f"wx_user_{''.join(random.sample(string.ascii_letters, 6))}",
                    wx_open_id=msg.source,
                    wx_unionid=wx_unionid,
                    wx_public_access=True,
                    head_img="",
                    source=enums.SourceEnum.PC.value
                )
            else:
                user.wx_unionid = wx_unionid
                user.wx_public_access = True
                user.save()
            ttl_cache.set(user.biz_id, DotDict(model_to_dict(user)))
            state = open_api_cache.get("readlecture:wx_token:biz:" + user.biz_id)
            if state:
                open_api_cache.set("readlecture:wx_token:" + state, user.biz_id)
            wechat_reply = create_reply(reply_info, msg)
    # 取消关注公众号的时候触发的逻辑
    elif isinstance(msg, UnsubscribeEvent):
        wx_unionid, access_token = await check_wx_unionid(msg.source)
        if wx_unionid is None:
            wechat_reply = create_reply("", msg)
        else:
            user = await query_user(wx_unionid, msg.source)
            if user:
                user.wx_unionid = wx_unionid
                user.wx_public_access = False
                user.save()
            wechat_reply = create_reply("", msg)
    # 用户扫码关注公众号”或“已关注用户扫码进入公众号”的事件
    elif isinstance(msg, SubscribeScanEvent) or isinstance(msg, ScanEvent):
        wx_unionid, access_token = await check_wx_unionid(msg.source)
        if wx_unionid is None:
            wechat_reply = create_reply("", msg)
        else:
            user = await query_user(wx_unionid, msg.source)
            scene_id = msg.scene_id
            logger.info(f"扫码微信登录二维码scene_id：{scene_id}")
            if not user:
                user = UserPO.create(
                    biz_id=generate_user_biz_id(),
                    name=f"wx_user_{''.join(random.sample(string.ascii_letters, 6))}",
                    wx_open_id=msg.source,
                    wx_unionid=wx_unionid,
                    wx_public_access=True,
                    head_img="",
                    source=enums.SourceEnum.PC.value
                )
                utm_params = open_api_cache.get(key_share_register.format(token_id=scene_id))
                if utm_params and isinstance(utm_params, dict):
                    await track_service.TrackService.event_register(user, utm_params)
            else:
                user.delete_flag = 0
                user.deletion_request_time = None
                user.wx_unionid = wx_unionid
                user.wx_public_access = True
                user.save()
            ttl_cache.set(user.biz_id, DotDict(model_to_dict(user)))
            open_api_cache.set("readlecture:wx_token:" + msg.scene_id, user.biz_id)
            wechat_reply = create_reply(login_success, msg)
    else:
        wechat_reply = create_reply("", msg)
    return wechat_reply.render()


@router.get("/check")
async def wx_check_health(signature: str, timestamp: str, nonce: str, echostr: str = None):
    try:
        check_signature(settings.WECHAT_TOKEN, signature, timestamp, nonce)
        # return echostr
        return JSONResponse(content=echostr)
    except InvalidSignatureException:  # 处理异常情况或忽略
        logger.error(
            "微信授权链接验证失败,signature:%s,timestamp:%s,nonce:%s,echostr:%s", signature, timestamp, nonce, echostr
        )
        return JSONResponse(content=echostr)


async def get_user_by_wx(biz_id, wx_unionid, open_id):
    target_user = UserPO.select().where(
        UserPO.biz_id != biz_id,
        UserPO.wx_unionid == wx_unionid,
        UserPO.delete_flag.in_([0, 2])
    ).first()
    if target_user is None:
        target_user = UserPO.select().where(
            UserPO.biz_id != biz_id,
            UserPO.wx_open_id == open_id,
            UserPO.delete_flag.in_([0, 2])
        ).first()
    return target_user


@router.get("/auth")
async def auth(code: str, state: str):
    """初步推测是微信绑定 单用，微信扫码回调认证（1）"""
    token = direct_access_token_by_code("pc", code)
    if not token:
        return common_error("error token check.please try again later")
    pc_open_id = token["openid"]
    wx_unionid = token.get('unionid')
    wechat_user = WechatBaseClient().get_wechat_oauth(state).get_user_info(pc_open_id, token["access_token"])

    if not wechat_user:
        return common_error("error token check, please try again later")
    logined_biz_id = open_api_cache.get("readlecture:wx_token:" + state)
    target_user = await get_user_by_wx(logined_biz_id, wx_unionid, pc_open_id)

    # 有用微信登录过
    if target_user:
        # 关注公众号 or 没有关注公众号,先更新当前微信用户的信息，再合并
        bind_key = key_wechat_bind_error.format(user_id=logined_biz_id)
        open_api_cache.set(bind_key, "该微信已有账号，请先注销/解绑其历史账号")

    # 没有用微信登录过
    else:
        logined_user = get_user_by_id(logined_biz_id)
        logined_user.name = wechat_user["nickname"]
        logined_user.wx_open_id = wechat_user["openid"]
        logined_user.wx_unionid = wx_unionid
        logined_user.head_img = wechat_user["headimgurl"]
        logined_user.save()
        ttl_cache.set(logined_biz_id, DotDict(model_to_dict(logined_user)))

    return RedirectResponse(
        url=f"https://mp.weixin.qq.com/mp/profile_ext?action=home&__biz={settings.WECHAT_REDIRECT_BIZ}==#wechat_redirect",
        status_code=303,
    )


@router.get("/bindwx", response_model=CommonResponse, summary='PC端微信绑定')
async def bindwx(wx_token: str, user=Depends(get_current_user)):
    """
    获取微信授权链接
    :param wx_token: 微信token
    :param user
    :return:
    """
    # 缓存 wx_token 的 biz_id
    open_api_cache.set("readlecture:wx_token:" + wx_token, user.biz_id)
    bind_key = key_wechat_bind_error.format(user_id=user.biz_id)
    open_api_cache.delete(bind_key)
    datas = WeChatOAuth(
        app_id=settings.WECHAT_APP_ID,
        secret=settings.WECHAT_APP_SECRET,
        redirect_uri=urljoin(settings.DOMAIN, "/api/v1/wechat/auth"),
        # 这里由于只有生产环境的微信公众号，hardcode here
        # redirect_uri=settings.WECHAT_REDIRECT_URI,
        scope="snsapi_userinfo",
        state=wx_token,
    )
    return common_success(data=datas.authorize_url)


@router.get("/bindmobilewx", response_model=CommonResponse, summary='APP绑定微信')
async def bind_mobile_login(wx_token: str, state: str, request: Request, user=Depends(get_current_user)):
    tag_name = 'bindmobilewx'
    open_api_cache.set("readlecture:wx_token:" + wx_token, user.biz_id)
    source = request.headers.get("source", "")

    current_user = UserPO.select().where(UserPO.biz_id == user.biz_id).first()

    if current_user.wx_unionid:
        return common_error("该账号已绑定过微信")

    token = direct_access_token_by_code(source, wx_token)

    if not token:
        return common_error("error token check.please try again later")
    pc_open_id = token["openid"]
    wx_unionid = token.get('unionid')

    wechat_user = WechatBaseClient().get_wechat_oauth(state).get_user_info(token["openid"], token["access_token"])
    if not wechat_user:
        return common_error("error token check, please try again later")

    target_user = await get_user_by_wx(user.biz_id, wx_unionid, pc_open_id)
    if target_user:
        return common_enum_error(ErrorCodeEnum.WECHAT_NEED_LOGOFF)
    UserPO.update(
        wx_unionid=wx_unionid,
        name=wechat_user["nickname"],
        head_img=wechat_user["headimgurl"]
    ).where(UserPO.biz_id == user.biz_id).execute()
    return common_success("移动端绑定微信成功")


@router.post("/unbindwx", response_model=CommonResponse, summary='PC端微信解绑')
async def unbindwx(user=Depends(get_current_user)):
    current_user = get_user_by_id(user.biz_id)
    if not current_user.wx_open_id and not current_user.wx_unionid:
        return common_error("当前用户没有绑定微信，无须操作")
    counts = (
        UserPO.update(
            wx_open_id=None,
            wx_unionid=None
        )
        .where(UserPO.biz_id == user.biz_id)
        .execute()
    )
    if counts:
        return common_success("解绑微信成功")
    else:
        logger.error(f"用户: {user.biz_id} 解绑微信失败")
        return common_error("解绑微信失败")


@router.get("/url", response_model=CommonResponse, summary="生成微信登录二维码")
async def url(wx_token: str, request: Request, is_off: int = None):
    """
    获取微信授权链接
    :param wx_token: 微信token
    :param request
    :param is_off
    :return:
    """
    all_params = dict(request.query_params)
    utm_params = {k: v for k, v in all_params.items() if k.startswith("utm_")}
    if utm_params:
        logger.info(f"生成微信登录二维码token：{wx_token}")
        open_api_cache.set(key_share_register.format(token_id=wx_token), utm_params)
    if is_off:
        open_api_cache.set(f"readlecture:wx_token_is_offer:{wx_token}", "1")
    datas = (
        WechatBaseClient()
        .get_we_chat_client()
        .qrcode.create(
            {
                "expire_seconds": 1800,
                "action_name": "QR_STR_SCENE",
                "action_info": {
                    "scene": {"scene_str": wx_token},
                },
            }
        )
    )
    return common_success(data=datas.get("url"))


@router.post("/xml/wechat_notify")
async def new_wechat_notify(request: Request):
    try:
        jsons = WechatBaseClient().get_wechat_pay().parse_payment_result(await request.body())

        if jsons["return_code"] != "SUCCESS" or jsons["result_code"] != "SUCCESS":
            logger.error("wechat pay error, request: %s", jsons)
            return WechatBaseClient.wechat_xml_call_back()

        await handle_wechat_notify(jsons)
        sem_to_baidu(order_id=jsons.get("out_trade_no"))

    except Exception:
        logger.error(
            "wechat pay error, request: ",
        )
        return WechatBaseClient.wechat_xml_call_back()

    return WechatBaseClient.wechat_xml_call_back()


@router.post("/wechat_notify")
async def wechat_notify(notification: EventVO):
    if notification.event_type != "TRANSACTION.SUCCESS":
        return common_success()

    try:
        jsons = WechatBaseClient().aes_256_decrypt(
            notification.resource.nonce, notification.resource.associated_data, notification.resource.ciphertext
        )

        await handle_wechat_notify(jsons)
        sem_to_baidu(order_id=jsons.get("out_trade_no"))

    except Exception:
        logger.error("wechat pay error, request: %s", notification)
        return common_success()

    return WechatBaseClient().get_wechat_pay().callback_success()


# pc 端微信注销
@router.get("/qrcode/{category}", response_model=CommonResponse, summary='PC端微信认证二维码')
async def wx_qrcode(category: CategoryEnum, wx_token: str, user=Depends(get_current_user)):
    """PC 认证二维码"""
    state = wx_token
    redis_key = key_pc_wechat_auth.format(category=category.value, state=state)
    if category == CategoryEnum.LOGOFF:
        datas = WeChatOAuth(
            app_id=settings.WECHAT_APP_ID,
            secret=settings.WECHAT_APP_SECRET,
            redirect_uri=urljoin(settings.DOMAIN, f"/api/v1/wechat/callback/{category.value}"),
            scope="snsapi_userinfo",
            state=state,
        )
        open_api_cache.set(redis_key, user.biz_id)
        notice_key = key_pc_wechat_auth_notice.format(category=category.value, state=state)
        open_api_cache.delete(notice_key)
        return common_success(data=datas.authorize_url)
    return common_enum_error(ErrorCodeEnum.METHOD_NOT_FOUND)


@router.get("/callback/{category}", response_model=CommonResponse, summary='PC端微信认证回调')
async def wx_qrcode_callback(category: CategoryEnum, code: str, state: str):
    """微信认证回调"""
    msg_log = '微信认证回调'
    try:
        redis_key = key_pc_wechat_auth.format(category=category.value, state=state)
        if category == CategoryEnum.LOGOFF:
            # PC 端注销
            notice_key = key_pc_wechat_auth_notice.format(category=category.value, state=state)
            user_biz_id = open_api_cache.get(redis_key)
            if user_biz_id is None:
                open_api_cache.set(notice_key, "二维码失效，刷新重试")
                raise Exception(f"{msg_log}，user_biz_id cache not found")

            token = direct_access_token_by_code("pc", code)
            if not token:
                open_api_cache.set(notice_key, "微信认证失败")
                raise Exception(f"{msg_log}，wx token is None")
            pc_open_id = token["openid"]
            wx_unionid = token.get('unionid')
            target_user = UserPO.select().where(
                UserPO.biz_id == user_biz_id,
                UserPO.wx_unionid == wx_unionid,
                UserPO.delete_flag == 0
            ).first()
            if target_user is None:
                target_user = UserPO.select().where(
                    UserPO.biz_id == user_biz_id,
                    UserPO.wx_open_id == pc_open_id,
                    UserPO.delete_flag == 0
                ).first()
            if not target_user:
                open_api_cache.set(notice_key, "扫码微信与当前用户不匹配")
                raise Exception(
                    f"{msg_log}，user_biz_id:{user_biz_id}--wx_unionid:{wx_unionid} 扫码微信与当前用户不匹配")
            elif target_user.delete_flag == 2:
                open_api_cache.set(notice_key, "用户已注销")
                raise Exception(f"{msg_log}，user_biz_id:{user_biz_id}--wx_unionid:{wx_unionid} 用户已注销")
            elif target_user.delete_flag == 0:
                target_user.delete_flag = 2
                target_user.deletion_request_time = get_beijing_time()
                target_user.save()
                open_api_cache.set(notice_key, "成功")
    except Exception:
        logger.exception(f"{msg_log}，错误")
    return RedirectResponse(
        url=f"https://mp.weixin.qq.com/mp/profile_ext?action=home&__biz={settings.WECHAT_REDIRECT_BIZ}==#wechat_redirect",
        status_code=303,
    )


@router.get("/waiting/{category}", response_model=CommonResponse, summary='PC端微注销轮询状态')
async def wx_qrcode_callback(category: CategoryEnum, wx_token: str):
    """PC端微注销轮询状态"""
    msg_log = 'PC端微注销轮询状态'
    state = wx_token
    try:
        redis_key = key_pc_wechat_auth.format(category=category.value, state=state)
        if category == CategoryEnum.LOGOFF:
            # PC 端注销
            biz_id = open_api_cache.get(redis_key)
            if not biz_id:
                return common_enum_error(ErrorCodeEnum.UNKNOWN_USER)
            notice_key = key_pc_wechat_auth_notice.format(category=category.value, state=state)

            msg = open_api_cache.get(notice_key)
            if not msg:
                return common_enum_error(ErrorCodeEnum.ERROR_TOKEN_CHECK)
            if msg == "成功":
                return common_success(msg)
            else:
                return common_error(msg)
        return common_enum_error(ErrorCodeEnum.METHOD_NOT_FOUND)
    except Exception:
        logger.exception(f"{msg_log}，错误")
        return common_enum_error(ErrorCodeEnum.ERROR_TOKEN_CHECK)


if __name__ == "__main__":
    try:
        # print(wechat_base_client.material.batchget("image"))
        wechat_base_client.menu.create(
            {
                "button": [
                    {
                        "name": "商务合作",
                        "sub_button": [
                            {
                                "type": "media_id",
                                "name": "商务合作",
                                "media_id": "2eZLCLh0Ku4Ym0339pfRvxwNg2l2BT6lxXw2hD318wc5OVZ7UDZHuX7DxH3sErPK",
                            }
                        ],
                    },
                    {"type": "view", "name": "视频总结", "url": "https://aihaoji.com/zh"},
                    {
                        "type": "media_id",
                        "name": "用户群",
                        "media_id": "2eZLCLh0Ku4Ym0339pfRvwQ6Y4cvlM0WQTebqg_aFNoPj4r7ntcrTgd0cdqwvtVb",
                    },
                ]
            }
        )
    except Exception as e:
        print(e)
