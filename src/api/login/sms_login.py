import random
import time

from fastapi import APIRouter, Depends, Request

from src.database import constants, enums
from src.logger.logUtil import get_logger
from src.model.user_manager import UserPO, UserVO, get_user_by_id, hash_password
from src.services.user_service import re_enable_account, generate_user_biz_id
from src.util.TokenUtil import create_jwt, get_current_user
from src.util.cacheUtil import DotDict, ttl_cache
from src.util.commonutil import CommonResponse, common_error, common_success, common_third_error, common_third_success, \
    common_enum_error, ErrorCodeEnum
from src.util.extra_info_util import record_user_extra_info
from src.util.sms_utils import CaptchaVerifyer, send_sms_by_aliyun
from src.util.stringify import is_phone
from src.services import track_service

logger = get_logger(__name__)

router = APIRouter(prefix="/api/v1/user/mobile", tags=["Login"])

INIT_PASSWORD = "********"
INIT_EMAIL = "undefined"
DEFAULT_EXPIRY_TIME = 60 * 2  # seconds


@router.post("/register_or_login", response_model=CommonResponse, summary="短信验证码登录/注册")
async def register_by_mobile(request: Request, user: UserVO):
    """
    创建用户
    :param request
    :param user:
    :return:
    """
    phone_number = user.mobile
    user_code = user.sms_code

    if phone_number is None or not is_phone(phone_number):
        return common_enum_error(ErrorCodeEnum.MOBILE_ERROR)

    # 获取缓存中的验证码
    cached_code = get_verification_code(phone_number)
    if not cached_code:
        return common_error("验证码已过期或未发送")

    if cached_code != user_code:
        return common_error("验证码错误")
    check_user = UserPO.select().where(
        (UserPO.mobile == phone_number) &
        ((UserPO.delete_flag == 0) | (UserPO.delete_flag == 2))
    ).first()

    if not check_user:
        source = request.headers.get(constants.HEADER_SOURCE, enums.SourceEnum.UNKNOWN.value)
        check_user = UserPO.create(
            biz_id=generate_user_biz_id(),
            mobile=phone_number,
            name=phone_number,
            password=hash_password(INIT_PASSWORD),
            email=INIT_EMAIL,
            source=source
        )
        # 注册时记录下用户来源 utm_* 字段
        extra_info_json = None
        if user.extra_info:
            extra_info_json = user.extra_info.model_dump()
        record_user_extra_info(check_user, utm_source=user.utm_source, extra_info=extra_info_json)

        if user.extra_info:
            await track_service.TrackService.event_register(check_user,user.extra_info)
    else:
        await re_enable_account(check_user, user.mobile)
    token = create_jwt(check_user.__data__)
    ttl_cache.set(check_user.biz_id, DotDict(check_user.__data__))
    return common_success({"jwtToken": token})


def set_verification_code(phone: str, code: str, ttl: int = DEFAULT_EXPIRY_TIME):
    expiry_time = time.time() + ttl
    ttl_cache.set(phone, {"code": code, "expiry_time": expiry_time})


def get_verification_code(phone: str):
    data = ttl_cache.get(phone) or {}
    if not data:
        return None
    code, expiry_time = data.get("code"), data.get("expiry_time") or 0
    if time.time() > expiry_time:  # 验证码过期
        ttl_cache.delete(phone)
        return None
    return code


@router.post("/send_sms", response_model=CommonResponse)
async def send_sms(user: UserVO, request: Request):
    """
    发送手机短信验证码
    """
    source = request.headers.get("source", "pc")
    if source == "pc":
        if not await CaptchaVerifyer.verify(user.captcha_verify_param or {}):
            return common_enum_error(ErrorCodeEnum.IMAGE_CAPTCHA_ERROR)
    phone = user.mobile
    if get_verification_code(phone):
        return common_third_error(f"短信发送失败: 您在 {DEFAULT_EXPIRY_TIME} 秒内已经发送过验证码，稍后重试",
                                  verify=False)
    code = str(random.randint(100000, 999999))
    try:
        response = send_sms_by_aliyun(phone, code)
        if response and b'"Message":"OK"' in response and b'"Code":"OK"' in response:
            logger.info(f"Sueecessed send {phone} a sms_code: {code}")
        else:
            logger.error(f"Faild send {phone} a sms_code: {code}, reason: {response.decode()}")
            raise Exception(response.decode())
    except Exception as e:
        return common_error(f"短信发送失败: {e}")
    # 缓存验证码
    set_verification_code(phone, code)
    return common_third_success("验证码已发送，请注意查收。", verify=True)


@router.post("/unbind", response_model=CommonResponse)
async def unbindwx(user=Depends(get_current_user)):
    current_user = get_user_by_id(user.biz_id)
    if not current_user.mobile:
        return common_error("当前用户没有绑定手机，无须操作")
    counts = (
        UserPO.update(
            mobile=None,
        )
        .where(UserPO.biz_id == user.biz_id)
        .execute()
    )
    if counts:
        return common_success("解绑手机号成功")
    else:
        return common_error("解绑手机号失败")
