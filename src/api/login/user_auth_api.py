from fastapi import APIRouter, Depends
from playhouse.shortcuts import model_to_dict
from starlette.requests import Request

from src.api.login.sms_login import get_verification_code
from src.logger.logUtil import get_logger
from src.model.member_manager import merge_member
from src.model.task_manager import merge_folder, merge_task
from src.model.user_manager import (
    UserMergeVO,
    UserPO,
    UserVO,
    delete_user,
    get_user_by_email,
    get_user_by_id,
    get_user_by_mobile,
    get_user_by_wx_id,
    hash_password,
)
from src.util import TokenUtil
from src.util.cacheUtil import DotDict, ttl_cache, open_api_cache
from src.util.commonutil import CommonResponse, common_error, common_success
from src.util.TokenUtil import create_jwt, get_6_random_mail_code, get_current_user, get_token_from_request
from src.util.wechat_oauth import WechatBaseClient
from src.util.redis_keys_util import key_wechat_bind_error
from src.util.stringify import is_empty
logger = get_logger(__name__)

router = APIRouter(prefix="/api/v1/auth/user", tags=["user"])



def check_wx_bind(wx_unionid, wx_open_id):
    bind_value = wx_unionid or wx_open_id
    if is_empty(bind_value):
        return None
    return 'wx-bind'



@router.get("/profile", summary="获取用户基本信息")
async def profile(user: UserPO = Depends(get_current_user)):
    """
    获取用户基本信息
    :return: 用户信息
    """

    current_user = get_user_by_id(user.biz_id)
    if not current_user:
        return common_error("unknown user")
    return common_success(
        {
            "name": current_user.name,
            "email": current_user.email,
            "mail_access": current_user.mail_access,
            "wx_open_id": check_wx_bind(current_user.wx_unionid, current_user.wx_open_id),
            "is_bind": current_user.wx_unionid is not None,
            "wx_public_access": current_user.wx_public_access,
            "biz_id": current_user.biz_id,
            "mobile": current_user.mobile,
            "head_img": current_user.head_img,
            "is_password":True if current_user.password else False
        }
    )



@router.get("/check_profile", summary="检查微信绑定返回结果")
async def check_profile(
        user: UserPO = Depends(get_current_user)
):
    """
    检查微信绑定返回结果
    :return: 用户信息
    """
    redis_key = key_wechat_bind_error.format(user_id=user.biz_id)
    error = open_api_cache.get(redis_key)
    if error:
        return common_error(error)
    current_user = get_user_by_id(user.biz_id)
    if not current_user:
        return common_error("unknown user")
    return common_success(
        {
            "name": current_user.name,
            "email": current_user.email,
            "mail_access": current_user.mail_access,
            "wx_open_id": check_wx_bind(current_user.wx_unionid, current_user.wx_open_id),
            "is_bind": current_user.wx_unionid is not None,
            "wx_public_access": current_user.wx_public_access,
            "biz_id": current_user.biz_id,
            "mobile": current_user.mobile,
            "head_img": current_user.head_img,
            "is_password":True if current_user.password else False
        }
    )




@router.post("/userMerge", summary="用户合并")
async def user_merge(merge: UserMergeVO, user_po: UserPO = Depends(get_current_user)):
    """
    TODO 绑定用户，合并逻辑取消，绑定时加校验  2025-4-26
    用户合并,只往邮箱账号合并， 目前不考虑往微信账号合并的事情
    :param user:
    :return:
    """
    current_user = get_user_by_id(user_po.biz_id)

    if not current_user:
        return common_error("unknown user")
    if merge.bind_type == 1:
        # 已经绑定了邮箱地址， 需要根据微信的accessToken获取到微信的授权数据
        token = WechatBaseClient().get_wechat_oauth(merge.bind_value).fetch_access_token(merge.bind_value)
        if not token:
            return common_error("error token check.please try again later")
        # 根据用户获取微信id
        if current_user.wx_open_id and current_user.wx_open_id == merge.bind_value:
            return common_success(message="该微信账号已经绑定")
        target_user = get_user_by_wx_id(token["openid"])
        if not target_user:
            current_user.wx_open_id = token["openid"]
            current_user.wx_public_access = True
            # merge_user(current_user, target_user)
            current_user.save()
            return common_success()
        else:
            return common_error("该微信账号已注册，不允许绑定")
    elif merge.bind_type == 2:
        if not merge.email:
            return common_error("未知邮箱账号")
        # 已经绑定了微信，需要根据邮箱的code绑定邮箱
        if current_user.email and current_user.email == merge.email:
            return common_success(message="该邮箱已经绑定至该账号")
        token = get_6_random_mail_code(merge.email)
        if merge.bind_value != token:
            return common_error("邮件验证码错误")
        target_user = get_user_by_email(merge.email)
        if target_user:
            return common_error("该邮箱账号已注册，不允许绑定")
        if merge.password:
            current_user.password = hash_password(merge.password)
        if not target_user:
            current_user.mail_access = True
            current_user.email = merge.email
            current_user.save()
            ttl_cache.set(current_user.biz_id, DotDict(model_to_dict(current_user)))
            return common_success(create_jwt(current_user.__data__))
        if target_user.wx_public_access:
            return common_error("该邮箱已经绑定在已有账号上,不可重复绑定")
        # 让邮箱账号为主账号， 微信账号为次账号
        # merge_user(target_user, current_user)
        ttl_cache.set(target_user.biz_id, DotDict(model_to_dict(target_user)))
        return common_success(create_jwt(target_user.__data__))
    if merge.bind_type == 3:
        # 手机号合并
        if not merge.mobile:
            return common_error("未知手机账号")
        if current_user.mobile and current_user.mobile == merge.mobile:
            return common_success(message="该手机已经绑定至该账号")
        token = get_verification_code(merge.mobile)
        if merge.bind_value != token:
            return common_error("手机验证码错误")
        target_user = get_user_by_mobile(merge.mobile)
        if merge.password:
            current_user.password = hash_password(merge.password)
        if not target_user:
            current_user.mobile = merge.mobile
            current_user.save()
            ttl_cache.set(current_user.biz_id, DotDict(model_to_dict(current_user)))
            return common_success(create_jwt(current_user.__data__))
        else:
            # current_user.mobile = merge.mobile
            # current_user.save()
            # merge_user(current_user, target_user)
            # ttl_cache.set(current_user.biz_id, DotDict(model_to_dict(current_user)))
            # return common_success(create_jwt(current_user.__data__))
            return common_error("手机号是已注册用户，不允许绑定")
    else:
        return common_error("未知合并逻辑")


def merge_user(current_user, target_user):
    """
    用户合并 1. 积分 2. 任务
    """
    try:
        current_user.wx_open_id = target_user.wx_open_id
        current_user.wx_unionid = target_user.wx_unionid
        current_user.wx_public_access = target_user.wx_public_access
        current_user.head_img = target_user.head_img
        if target_user.mail_access:
            current_user.password = target_user.password
        if target_user.mail_access:
            current_user.mail_access = target_user.mail_access
        merge_member(current_user.biz_id, target_user.biz_id)
        merge_task(current_user.biz_id, target_user.biz_id)
        merge_folder(current_user.biz_id, target_user.biz_id)
        current_user.save()
        delete_user(target_user)
    except Exception:
        logger.exception("merge user error")


@router.post("/reset", response_model=CommonResponse, summary="重置密码")
async def reset_user(user: UserVO, userPO: UserPO = Depends(get_current_user)):
    """
    重置密码 只需要提供 password 参数
    :param user:
    :return:
    """
    po = get_user_by_id(user.biz_id)
    if not po:
        return common_error("unknown user")

    if not update_password(user, po):
        return common_error(message="create user error")


@router.get("/refresh", response_model=CommonResponse, summary="刷新token")
async def refresh_user(req: Request):
    """
    刷新token
    :return:
    """
    token = get_token_from_request(req)
    dict = TokenUtil.verify_token(token)
    if not dict:
        return common_error("token invalid")
    return common_success(dict["token"])


def update_password(user: UserVO, po: UserPO):
    try:
        hashed_password = hash_password(user.password)
        UserPO.update(password=hashed_password).where(UserPO.biz_id == po.biz_id)
    except Exception:
        logger.exception("Error updating password: ")
        return False
    return True
