from fastapi import APIRouter, Query, Request
from playhouse.shortcuts import model_to_dict
from wechatpy import WeChatOAuth

from config import settings
from src.database import constants, enums
from src.logger.logUtil import get_logger
from src.model.user_manager import UserPO, query_user_by_unionid
from src.services.user_service import generate_user_biz_id
from src.services import track_service
from src.services.wechat_service import (
    direct_access_token_by_code
)
from src.util.TokenUtil import create_jwt
from src.util.cacheUtil import DotDict, open_api_cache, ttl_cache
from src.util.commonutil import CommonResponse, common_error, common_success
from src.util.extra_info_util import record_user_extra_info
from src.util.wechat_oauth import WechatBaseClient

logger = get_logger(__name__)

"""
    公众号页面用户信息授权:
    step0: check page url 是否带有code=CODE&state=STATE
    step1.1: 如果有：frontend /wx-authorize-login，获取用户信息
    step1.2: 如果没有：frontend GET /wx-authorize-url for redirect url
    step2: frontend /wx-authorize-login 登录
"""

router = APIRouter(prefix="/api/v2/login", tags=["wechatLogin"])


@router.get("/wx-authorize-url", response_model=CommonResponse)
async def wx_authorize_url(wx_token: str, scope: str = "snsapi_userinfo", user_id: str = None,
                           redirect_uri: str = Query(...)):
    """
    获取微信授权链接
    :param wx_token: 微信token
    :param scope: 微信token
    :param user_id: 微信token
    :param redirect_uri
    :return:
    """
    open_api_cache.set("readlecture:wx_token:" + wx_token, user_id)

    datas = WeChatOAuth(
        app_id=settings.WECHAT_APP_ID,
        secret=settings.WECHAT_APP_SECRET,
        redirect_uri=redirect_uri,
        scope=scope,
        state=wx_token,
    )
    return common_success(data={"url": datas.authorize_url, "user_id": user_id})


@router.get("/wx-authorize-login", response_model=CommonResponse)
async def wx_authorize_login(code: str, state: str, request: Request):
    """wx-authorize-login"""
    source = request.headers.get(constants.HEADER_SOURCE, enums.SourceEnum.UNKNOWN.value)
    token = direct_access_token_by_code(source, code)

    if not token:
        return common_error("error token check.please try again later")

    wx_unionid = token.get('unionid')

    wechat_user = WechatBaseClient().get_wechat_oauth(state).get_user_info(token["openid"], token["access_token"])
    if not wechat_user:
        return common_error("error token check, please try again later")
    user = await query_user_by_unionid(wx_unionid)
    if user:
        # 取消注销
        if user.delete_flag == 2:
            user.delete_flag = 0
            user.deletion_request_time = None
        user.head_img = wechat_user["headimgurl"]
        user.name = wechat_user["nickname"]
        user.wx_public_access = True
        ttl_cache.set(user.biz_id, DotDict(model_to_dict(user)))
        user.save()
    # 没有用微信登录过
    else:
        user = UserPO.create(
            biz_id=generate_user_biz_id(),
            name=wechat_user["nickname"],
            wx_public_access=True,
            wx_unionid=wx_unionid,
            head_img=wechat_user["headimgurl"],
            source=source
        )
        all_params = dict(request.query_params)
        utm_params = {k: v for k, v in all_params.items() if k.startswith("utm_")}
        record_user_extra_info(user, extra_info=utm_params)
        if utm_params:
            await track_service.TrackService.event_register(user, utm_params)
    token = create_jwt(user.__data__)
    ttl_cache.set(user.biz_id, DotDict(user.__data__))
    return common_success({"jwtToken": token})
