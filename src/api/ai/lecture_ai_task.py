from typing import Optional

from fastapi import APIRouter, Depends
from pydantic import BaseModel, HttpUrl

from src.logger.logUtil import get_logger
from src.model.article_detail_manager import LectureArticleDetail
from src.model.article_manager import LectureArticle, get_article_by_id
from src.model.lecture_ai import AITYP<PERSON>, LectureAITask, create_ai_task_with_task_type, find_ai_task
from src.orm.pgrepo import db
from src.util.commonutil import CommonResponse, ErrorCodeEnum, common_enum_error, common_error, common_success
from src.util.dateUtil import get_current_time_in_beijing
from src.util.TokenUtil import get_current_user, verify_key
from src.util.redis_keys_util import key_ai_task_limit
from src.core import deps
from src.database import enums

MAX_PAGE_SIZE = 20
logger = get_logger(__name__)

router = APIRouter(prefix="/api/v2/ai-task", tags=["ai-task"])


class AITaskVO(BaseModel):
    user_id: str = None
    article_biz_id: str
    task_type: int
    name: Optional[str] = None
    task_source: Optional[str] = None
    ctrl_params: Optional[dict] = None


async def remove_lock_limit_user(redis_):
    limit_key = key_ai_task_limit.format(ai_type=AITYPE.AUDIO.value)
    limit_user = await redis_.smembers(limit_key)
    LectureAITask.select(LectureAITask.user_id).where(
        (LectureAITask.delete_flag == 0),
        (LectureAITask.user_id.in_(list(limit_user))),
        (LectureAITask.task_type == AITYPE.AUDIO.value),
        (LectureAITask.status == enums.TaskStatusEnum.PROCESSING.value),
    )


@router.post("/create", response_model=CommonResponse)
async def create_task(task: AITaskVO, user=Depends(get_current_user)):
    """先保留，这个本是为 AI 算法侧创建 AI 润色任务 或者 音频转换 任务
    现在是后端自己创建
    """
    if task.task_type not in [AITYPE.AI_POLISHING.value, AITYPE.AUDIO.value]:
        return common_error(f"当前不支持创建 task_type: {task.task_type} 的任务")
    task.user_id = user.biz_id
    exists, ai_task = create_ai_task_with_task_type(**task.__dict__)
    if exists:
        return common_success({"task_id": ai_task.biz_id, "status": "created"})
    return common_success({"task_id": ai_task.biz_id, "status": "created"})


def _get_section_fields(task_type, sections, out_language):
    if not sections:
        return []
    needs = []
    for section in sections:
        origin_text = ""
        out_language_modified_text = ""
        if task_type == AITYPE.AUDIO.value:
            # 只提供 out_language 的 modified_text
            out_language_modified_text = (section.translated_modified_text_json or {}).get(out_language) or ""
        if task_type == AITYPE.AI_POLISHING.value:
            origin_text = section.origin_text
        needs.append(
            {
                "index": section.index,
                "start_time": section.start_time,
                "end_time": section.end_time,
                "origin_text": origin_text,
                "out_language_modified_text": out_language_modified_text,
                "speaker": section.speaker,
                "biz_id": section.biz_id,
                "pic_keywords": section.pic_keywords,
                "oss_pic_path": section.oss_pic_path,
                "local_pic_path": section.local_pic_path,
            }
        )
    return needs


@router.get("/sections", summary="文章解析结果内容")
async def sections_response(
        task_id: str, page_no: int = 1, page_size: int = 10, index: Optional[int] = None, key: str = Depends(verify_key)
):
    ai_task = find_ai_task(task_id)
    if not ai_task:
        return common_error(f"task: {task_id} not found")
    if ai_task.task_type not in [AITYPE.AUDIO.value, AITYPE.AI_POLISHING.value]:
        return common_error(f"当前不支持 task_type: {ai_task.task_type}")
    article_biz_id = ai_task.article_biz_id
    article = get_article_by_id(article_biz_id)
    if not article:
        return common_error(f"该任务没有相关联的文章 article_biz_id: {article_biz_id}")
    if page_size > MAX_PAGE_SIZE:
        return common_error(f"page_size 最大支持 {MAX_PAGE_SIZE}")
    total = None
    # index 是从1开始的
    if isinstance(index, int) and index:
        sections = LectureArticleDetail.select().where(
            (LectureArticleDetail.delete_flag == 0),
            (LectureArticleDetail.article_id == article_biz_id),
            (LectureArticleDetail.index == index),
        )
    else:
        query = LectureArticleDetail.select().where(
            (LectureArticleDetail.delete_flag == 0),
            (LectureArticleDetail.article_id == article_biz_id),
        )
        total = query.count()
        sections = query.order_by(LectureArticleDetail.index or LectureArticleDetail.start_time).paginate(
            page_no, page_size
        )
    section_details = _get_section_fields(ai_task.task_type, sections, out_language=article.out_language)
    return common_success({"sections": section_details, "total": total})


@router.get("/latest", summary="获取AI任务")
async def get_latest(
        task_type: int,
        key: str = Depends(verify_key),
        redis_: deps.RedisSessionDep = deps.RedisSessionDep
):
    # 这里添加事务，在查询加行级锁
    if task_type not in [AITYPE.AUDIO.value, AITYPE.AI_POLISHING.value]:
        return common_error(f"当前不支持 task_type: {task_type}")
    limit_key = key_ai_task_limit.format(ai_type=task_type)
    limit_user = await redis_.smembers(limit_key)
    with db.atomic() as txn:
        try:
            filters = [
                (LectureAITask.status == "confirm"),
                (LectureAITask.delete_flag == 0),
                (LectureAITask.task_type == task_type)
            ]
            if task_type == AITYPE.AUDIO.value and limit_user:
                filters.append(LectureAITask.user_id.not_in(list(limit_user)))
            task = (
                LectureAITask.select()
                .where(*filters)
                .for_update()
                .order_by(-LectureAITask.order, LectureAITask.id.asc())
                .first()
            )
            if not task:
                return common_success()

            article_biz_id = task.article_biz_id

            # 针对 AI 润色返回 corpus, sections 字段
            article = get_article_by_id(article_biz_id)
            if not article:
                return common_error(f"该任务没有相关联的文章 article_biz_id: {article_biz_id}")
            data = {
                "task_id": task.biz_id,
                "name": task.name,
                "task_type": task.task_type,
                "corpus": article.corpus,
                "ctrl_params": task.ctrl_params,
                "title": article.name,
                "user_id": task.user_id,
                "video_url": article.video_url,
                "video_lan": article.language,
                "markdown_lan": article.out_language,
                "input_language": article.language,
                "output_language": article.out_language,
            }
            task.status = "processing"
            task.save()
            if task_type == AITYPE.AUDIO.value:
                await redis_.sadd(limit_key, task.user_id)
            return common_success(data)
        except (LectureAITask.DoesNotExist, Exception) as e:
            txn.rollback()
            return common_error(str(e))


class AITaskResult(BaseModel):
    task_id: str
    audio_url: Optional[HttpUrl] = None
    oss_pdf_url: Optional[HttpUrl] = None
    oss_word_url: Optional[HttpUrl] = None
    outline: Optional[str] = None
    out_language_outline: Optional[str] = None
    out_language_total_summary_json: Optional[dict] = None
    total_summary_json: Optional[dict] = None
    podcast_transcript_json: Optional[list] = None
    token: Optional[int] = 0
    podcast_duration: Optional[float] = 0


@router.get("/retry")
async def retry(
        task_id: str, key: str = Depends(verify_key),
        redis_: deps.RedisSessionDep = deps.RedisSessionDep
):
    try:
        filters = [
            ((LectureAITask.status == "processing") | (LectureAITask.status == "fail"))
            & (LectureAITask.delete_flag == 0)
            & (LectureAITask.biz_id == task_id)
        ]
        task = (
            LectureAITask.select()
            .where(*filters)
            .for_update()
            .order_by(-LectureAITask.order, LectureAITask.id.asc())
            .first()
        )
        if task:
            limit_key = key_ai_task_limit.format(ai_type=AITYPE.AUDIO.value)
            exists = await redis_.sismember(limit_key, task.user_id)
            if exists:
                await redis_.srem(limit_key, task.user_id)
            # LectureAITask.update(status="confirm").where(*filters).execute()
            task.status = "confirm"
            task.remark = None
            task.save()
            return common_success(data={"status": task.status})
        return common_error("没有修改的任务")
    except (LectureAITask.DoesNotExist, Exception) as e:
        logger.exception(e)
        return common_error(str(e))


@router.post("/finish", response_model=CommonResponse, summary="接收AI任务反馈结果")
async def finish(
        result: AITaskResult,
        key: str = Depends(verify_key),
        redis_: deps.RedisSessionDep = deps.RedisSessionDep
):
    """
    AI算法侧 完成任务
    """
    # update task
    ai_task = find_ai_task(result.task_id)

    if not ai_task:
        logger.error(f"task: {result.task_id} not found")
        return common_error(f"task: {result.task_id} not found")

    if ai_task.task_type == AITYPE.AUDIO.value:
        limit_key = key_ai_task_limit.format(ai_type=ai_task.task_type)
        exists = await redis_.sismember(limit_key, ai_task.user_id)
        if exists:
            await redis_.srem(limit_key, ai_task.user_id)

    if ai_task.status == "finished":
        logger.error(f"task: {result.task_id} has been finished")
        return common_enum_error(ErrorCodeEnum.TASK_REPEAT)

    if ai_task.task_type not in [AITYPE.AI_POLISHING.value, AITYPE.AUDIO.value]:
        logger.error(f"task: {result.task_id} task_type: {ai_task.task_type} not supported")
        return common_error("当前 task_id: {result.task_id} 的 task_type: {ai_task.task_type} 不支持")

    article = get_article_by_id(ai_task.article_biz_id)
    if not article:
        logger.error(f"task: {result.task_id} 与 article: {article} 未关联")
        return common_error(f"task: {result.task_biz_id} 与 article: {article} 未关联")
    logger.info(
        f"finish task: {result.task_id} task_type: {ai_task.task_type} audio_url: {bool(result.audio_url)} "
        f"pdf_url: {result.oss_pdf_url} word_url: {result.oss_word_url} "
        f"outline: {bool(result.outline)} total_summary_json: {bool(result.total_summary_json)} "
    )
    if result.audio_url:
        article.audio_url = result.audio_url
        article.podcast_duration = result.podcast_duration
        LectureArticle.update(audio_url=result.audio_url, podcast_duration=result.podcast_duration).where(
            LectureArticle.biz_id == article.biz_id).execute()
    if ai_task.task_type == AITYPE.AI_POLISHING.value:
        if result.oss_pdf_url:
            article.oss_pdf_url = result.oss_pdf_url
        if result.oss_word_url:
            article.oss_word_url = result.oss_word_url
        # AI 算法侧会提供润色的 outline, total_summary_json, 能 markdown_lan 对应的 out_language_outline, ...
        translated_outline_json = article.translated_outline_json or {}
        if result.out_language_outline:
            translated_outline_json[article.out_language] = result.out_language_outline
        if result.outline and article.out_language != article.language:
            translated_outline_json[article.language] = result.outline
        article.translated_outline_json = translated_outline_json

        translated_total_summary_json = article.translated_total_summary_json or {}
        if result.out_language_total_summary_json:
            translated_total_summary_json[article.out_language] = result.out_language_total_summary_json
        if result.total_summary_json and article.out_language != article.language:
            translated_total_summary_json[article.language] = result.total_summary_json
        article.translated_total_summary_json = translated_total_summary_json
        # 创建 音频转换 任务
        # create_ai_task_with_task_type(
        #     user_id=ai_task.user_id,
        #     article_biz_id=ai_task.article_biz_id,
        #     task_type=AITYPE.AUDIO.value,
        #     ctrl_params=ai_task.ctrl_params,
        # )
        LectureArticle.update(
            oss_pdf_url=result.oss_pdf_url,
            oss_word_url=result.oss_word_url,
            translated_outline_json=translated_outline_json,
            translated_total_summary_json=translated_total_summary_json,
        ).where(LectureArticle.biz_id == article.biz_id).execute()
    if ai_task.task_type == AITYPE.AUDIO.value:
        if result.podcast_transcript_json:
            LectureArticle.update(
                podcast_transcript_json=result.podcast_transcript_json,
            ).where(LectureArticle.biz_id == article.biz_id).execute()
    ai_task.end_time = get_current_time_in_beijing()
    ai_task.status = "finished"
    ai_task.save()
    return common_success("更新任务成功")
