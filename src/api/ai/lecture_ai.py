import asyncio
import json
import uuid
from typing import List, Optional

import json_repair
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel, Field, field_validator

from src.constant.prompt import AI_LEARNING_PROMPT
from src.logger.logUtil import get_logger
from src.model.article_detail_manager import LectureArticleDetail, get_all_details_by_article_biz_id
from src.model.article_manager import LectureArticle, get_article_by_id
from src.model.lecture_ai import (
    AITYPE,
    FetchSource,
    RoleValue,
    check_ai_task_has_created,
    create_ai_detail,
    find_ai_learning_history,
    get_or_create_ai_group,
)
from src.util.article_utils import get_article_lan_modified_text, get_article_outline, get_article_summary_json
from src.util.commonutil import common_error, common_success
from src.util.llm_interface import async_llm_chat
from src.util.TokenUtil import get_current_user
from src.util.translate_util import llm_translate, llm_translate_json

router = APIRouter(prefix="/api/v1/lecture-ai")

logger = get_logger(__name__)


class ArticleFieldVO(BaseModel):
    outline: str
    total_summary_json: dict

    @field_validator("outline")
    def outline_must_not_be_empty(cls, value):
        if not value.strip():  # 检查是否是非空字符串
            raise HTTPException(status_code=400, detail="请提供非空 str: outline")
        return value

    @field_validator("total_summary_json")
    def total_summary_json_must_not_be_empty(cls, value):
        if not value:  # 检查是否为空字典
            raise HTTPException(status_code=400, detail="请提供非空 JSON object: total_summary_json")
        return value


class CtrlParamsVO(BaseModel):
    in_language: Optional[str] = None
    out_language: str

    @field_validator("in_language")
    def in_language_default_empty_str(cls, value):
        if not (value and value.strip()):
            return ""
        return value

    @field_validator("out_language")
    def total_summary_json_must_not_be_empty(cls, value):
        if not (value and value.strip()):
            raise HTTPException(status_code=400, detail="请提供有效的 out_language")
        return value


class AIAbility(BaseModel):
    ai_type: AITYPE = Field(
        description="AI 类型, 1. 大纲 2. 全文总结 3. 划线翻译 4. 划线总结 5. AI润色 6. AI学习 7 音频输出 8 翻译文章字段",
    )
    article_biz_id: str = Field(..., description="文章业务ID")
    content: Optional[str] = Field(None, description="Text格式内容")
    json_obj: Optional[ArticleFieldVO] = Field(
        None, description="JSON obj, 在翻译文章字段时，如 outline, total_summary_json"
    )
    ctrl_params: Optional[CtrlParamsVO] = Field(None, description="控制字段")

    @field_validator("ai_type")
    def supported_ai_type(cls, value):
        if value not in [
            AITYPE.OUTLINE.value,
            AITYPE.SUMMARY.value,
            AITYPE.TRANSLATION.value,
            # AITYPE.SUMMARY_LINE.value,
            AITYPE.AI_LEARNING.value,
            AITYPE.TRANSLATION_FIELD.value,
        ]:
            raise HTTPException(status_code=400, detail=f"现在不支持 ai_type: {value}")
        return value


async def call_open_llm_api(ai_ability: AIAbility, user_id):
    # 1. outline
    if ai_ability.ai_type == AITYPE.OUTLINE.value:
        article = get_article_by_id(ai_ability.article_biz_id)
        if not article:
            raise ValueError(f"article: {ai_ability.article_biz_id} 不存在")
        if ai_ability.ctrl_params:
            out_language = ai_ability.ctrl_params.out_language
        else:
            out_language = article.out_language
        translated_outline_json = article.translated_outline_json or {}
        if translated_outline_json.get(out_language):
            return {"outline": translated_outline_json[article.out_language], "total_summary_json": None}, 0
        else:
            article_text = get_article_lan_modified_text(article.out_language, ai_ability.article_biz_id)
            # TODO, 后面添加 token 值
            outline = await get_article_outline(article_text=article_text, language=out_language)
            translated_outline_json[out_language] = outline
            article.translated_outline_json = translated_outline_json
            (
                LectureArticle.update(translated_outline_json=translated_outline_json)
                .where(LectureArticle.biz_id == article.biz_id)
                .execute()
            )
            return {"outline": outline, "total_summary_json": None}, 0
    # 2. total_summary
    if ai_ability.ai_type == AITYPE.SUMMARY.value:
        article = get_article_by_id(ai_ability.article_biz_id)
        if not article:
            raise ValueError(f"article: {ai_ability.article_biz_id} 不存在")
        if ai_ability.ctrl_params:
            out_language = ai_ability.ctrl_params.out_language
        else:
            out_language = article.out_language
        translated_total_summary_json = article.translated_total_summary_json or {}
        if translated_total_summary_json.get(out_language):
            return {
                "outline": "",
                "total_summary_json": translated_total_summary_json[out_language],
            }, 0
        else:
            article_text = get_article_lan_modified_text(article.out_language, ai_ability.article_biz_id)
            # TODO, 后面添加 token 值
            total_summary_json = await get_article_summary_json(article_text=article_text, language=out_language)
            translated_total_summary_json[out_language] = total_summary_json
            article.translated_total_summary_json = translated_total_summary_json
            (
                LectureArticle.update(translated_total_summary_json=translated_total_summary_json)
                .where(LectureArticle.biz_id == article.biz_id)
                .execute()
            )
            return {"outline": "", "total_summary_json": total_summary_json}, 0
    # 5. AI 学习
    if ai_ability.ai_type == AITYPE.AI_LEARNING.value:
        system_prompt = AI_LEARNING_PROMPT.get(ai_ability.content)
        if not system_prompt:
            return f"AI 学习 {ai_ability.content} 暂不支持", 0
        if ans := find_ai_learning_history(user_id, ai_ability.article_biz_id, ai_ability.ai_type, ai_ability.content):
            return ans, 0
        article = get_article_by_id(ai_ability.article_biz_id)
        if not article:
            raise ValueError(f"article: {ai_ability.article_biz_id} 不存在")
        # 会议总结需要把 speaker, start_time 也放到里面
        if ai_ability.content == "会议总结":
            article_text = get_article_lan_modified_text(
                article.out_language, ai_ability.article_biz_id, with_speaker=True
            )
        else:
            article_text = get_article_lan_modified_text(article.out_language, ai_ability.article_biz_id)
        prompt = {
            "system": system_prompt,
            "user": article_text,
        }
        ans, token, _ = await async_llm_chat(prompt)
        return ans, token
    # 4. 划线总结
    # if ai_ability.ai_type == AITYPE.SUMMARY_LINE.value: ...
    # 3. 划线翻译
    if ai_ability.ai_type == AITYPE.TRANSLATION.value:
        if not ai_ability.ctrl_params:
            raise ValueError("ctrl_params 字段不能为空")
        # 前面两个不需要语言支持，而后面的需要语言字段
        in_language = ai_ability.ctrl_params.in_language
        out_language = ai_ability.ctrl_params.out_language
        text = ai_ability.content
        if not text:
            raise ValueError("content 字段不能为空")
        ans, token, _ = await llm_translate(text=text, in_language=in_language, out_language=out_language)
        return ans, token
    # 8. 翻译文章字段(意味着要更新到数据库)
    elif ai_ability.ai_type == AITYPE.TRANSLATION_FIELD.value:
        if not ai_ability.ctrl_params:
            raise ValueError("ctrl_params 字段不能为空")
        if not ai_ability.json_obj:
            raise ValueError("json_obj 字段不能为空")
        article = get_article_by_id(ai_ability.article_biz_id)
        if not article:
            raise ValueError(f"article: {ai_ability.article_biz_id} 不存在")
        in_language = ai_ability.ctrl_params.in_language
        out_language = ai_ability.ctrl_params.out_language
        # 判断是否已经有翻译过的, 如果有直接返回
        tra_outline = (article.translated_outline_json or {}).get(out_language)
        tra_total_summary_json = (article.translated_total_summary_json or {}).get(out_language)
        if tra_outline and tra_total_summary_json:
            return {"outline": tra_outline, "total_summary_json": tra_total_summary_json}, 0

        outline = ai_ability.json_obj.outline
        total_summary_json = ai_ability.json_obj.total_summary_json
        # 这里也把 lable_key_mappings 进行翻译
        total_summary_json["lable_key_mappings"] = {
            key: " ".join(word.title() for word in key.split("_")) for key in total_summary_json.keys()
        }
        trans_outline, trans_total_summary = await asyncio.gather(
            *[
                llm_translate(outline, in_language=in_language, out_language=out_language),
                llm_translate_json(
                    json.dumps(total_summary_json, indent=4), in_language=in_language, out_language=out_language
                ),
            ],
            return_exceptions=True,
        )
        if isinstance(trans_outline, Exception):
            logger.error(f"article_biz_id: {ai_ability.article_biz_id} 在翻译 outline 时失败")
            outline_token = 0
            tra_outline = ""
        else:
            tra_outline, outline_token, _ = trans_outline
            translated_outline_json = article.translated_outline_json
            translated_outline_json[out_language] = tra_outline
            article.translated_outline_json = translated_outline_json
            LectureArticle.update(translated_outline_json=translated_outline_json).where(
                LectureArticle.biz_id == article.biz_id
            ).execute()
        if isinstance(trans_total_summary, Exception):
            logger.error(f"article_biz_id: {ai_ability.article_biz_id} 在翻译 total_summary 时失败")
            total_summary_json_token = 0
            tra_total_summary_json = None
        else:
            tra_total_summary_str, total_summary_json_token, _ = trans_total_summary
            tra_total_summary_str = json_repair.repair_json(tra_total_summary_str)
            tra_total_summary_json = json.loads(tra_total_summary_str)

            translated_total_summary_json = article.translated_total_summary_json
            translated_total_summary_json[out_language] = tra_total_summary_json
            article.translated_total_summary_json = translated_total_summary_json
            LectureArticle.update(translated_total_summary_json=translated_total_summary_json).where(
                LectureArticle.biz_id == article.biz_id
            ).execute()
        return {
            "outline": tra_outline,
            "total_summary_json": tra_total_summary_json,
        }, outline_token + total_summary_json_token
    return "", 0


@router.post("/create-ability")
async def create_ability(ai_ability: AIAbility, user=Depends(get_current_user)):
    """ """
    ai_group = get_or_create_ai_group(user_id=user.biz_id, article_biz_id=ai_ability.article_biz_id)
    # 用户在这篇文章首次创建
    if not ai_group.check_can_use():
        return common_error("用户使用次数已经用完")
    # 5. AI 学习; 4. AI 划线总结; 3. 划线翻译; 2. total_summary; 1. outline; 8. 文章字段翻译
    try:
        ans, token = await call_open_llm_api(ai_ability, user.biz_id)
    except (ValueError, TypeError) as e:
        return common_error(str(e))
    except Exception:
        return common_error(f"AI 功能: {ai_ability.ai_type} 异常")
    # 如果有使用 token, 则创建 2 条记录
    if token:
        ask_detail_biz_id = uuid.uuid4().hex
        create_ai_detail(
            user_id=user.biz_id,
            group_biz_id=ai_group.biz_id,
            biz_id=ask_detail_biz_id,
            ai_type=ai_ability.ai_type,
            fetch_source=FetchSource.USER.value,
            content=ai_ability.content,
            role=RoleValue.USER.value,
        )
        ans_detail_biz_id = uuid.uuid4().hex
        # 不存储 OUTLINE SUMMARY TRANSLATION_FIELD 的结果 (1, 2, 8)
        if ai_ability.ai_type in [AITYPE.OUTLINE.value, AITYPE.SUMMARY.value, AITYPE.TRANSLATION_FIELD.value]:
            stored = None
        # 存储 AI_LEARNING SUMMARY_LINE TRANSLATION 的结果 (5, 4, 3)
        else:
            stored = ans
        create_ai_detail(
            user_id=user.biz_id,
            group_biz_id=ai_group.biz_id,
            biz_id=ans_detail_biz_id,
            ai_type=ai_ability.ai_type,
            fetch_source=FetchSource.USER.value,
            content=stored,
            role=RoleValue.ASSISTANT.value,
            token=token,
        )
        if ai_ability.ai_type not in [AITYPE.OUTLINE.value, AITYPE.SUMMARY.value]:
            ai_group.used_count += 2
            ai_group.save()
    if isinstance(ans, dict):
        return common_success({"content": "", "json_obj": ans})
    else:
        return common_success({"content": ans, "json_obj": None})


class TranslateVO(BaseModel):
    article_biz_id: str
    section_biz_ids: Optional[List[str]]
    out_language: str
    in_language: Optional[str]


semaphore = asyncio.Semaphore(10)


# 翻译并更新数据库
async def process_section(section: LectureArticleDetail, in_language: str = "", out_language: str = ""):
    translate_modified_text_json = section.translated_modified_text_json or {}
    modified_text = translate_modified_text_json.get(in_language) or section.origin_text
    # AI 算法侧提供了空的 origin_text
    if not section.origin_text:
        return ""
    if not modified_text:
        return
    if translate_modified_text_json.get(out_language):
        return translate_modified_text_json[out_language]
    async with semaphore:
        translated_text, _, _ = await llm_translate(
            text=modified_text, in_language=in_language, out_language=out_language
        )
        translate_modified_text_json[out_language] = translated_text
        section.translate_modified_text_json = translate_modified_text_json
        section.save()
        return translated_text


@router.post("/translate-sections")
async def translate_sectons(trans: TranslateVO, user=Depends(get_current_user)):
    article = get_article_by_id(trans.article_biz_id)
    if not article:
        return common_error(f"article: {trans.article_biz_id} 不存在")
    sections = get_all_details_by_article_biz_id(article_biz_id=trans.article_biz_id)
    if trans.section_biz_ids:
        sections = [section for section in sections if section.biz_id in trans.section_biz_ids]
    else:
        sections = list(sections)
    if not sections:
        return common_error(f"article: {trans.article_biz_id} 没有获取到 sections")
    results = await asyncio.gather(
        *[
            process_section(section, in_language=article.language, out_language=trans.out_language)
            for section in sections
        ],
        return_exceptions=True,
    )
    successed_sections, failed_sections = [], []
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            failed_sections.append(sections[i].biz_id)
            logger.error(f"Error: {result}")
        else:
            if result is None:
                failed_sections.append(sections[i].biz_id)
            else:
                successed_sections.append({"biz_id": sections[i].biz_id, "content": result})
    return common_success(
        data={
            "total": len(sections),
            "success": len(successed_sections),
            "fail": len(failed_sections),
            "successed_sections": successed_sections,
            "failed_sections": failed_sections,
        }
    )


@router.get("/ability-detail/{article_biz_id}")
async def ai_ability_details(article_biz_id: str, ai_type: int, user=Depends(get_current_user)):
    """ """
    if ai_type not in [AITYPE.AUDIO.value, AITYPE.AI_POLISHING.value]:
        return common_error("不支持查询 ai_type: {ai_type} 的任务")
    article = get_article_by_id(article_biz_id)
    if not article:
        return common_error(f"当前 article: {article_biz_id} 不存在")
    ai_task = check_ai_task_has_created(article_biz_id=article_biz_id, task_type=ai_type)
    audio_url = ""
    if not ai_task:
        status = "no_created"
    else:
        status = ai_task.status
    if AITYPE.AUDIO.value == ai_type:
        audio_url = article.audio_url
        podcast_duration = article.podcast_duration
        # 播客输出内容
        podcast_transcript_json = article.podcast_transcript_json
        return common_success({"audio_url": audio_url, "podcast_duration": podcast_duration,
                               "podcast_transcript_json": podcast_transcript_json, "status": status})
    else:
        return common_success({"status": status})
