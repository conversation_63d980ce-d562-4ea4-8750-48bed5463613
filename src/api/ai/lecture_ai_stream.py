import json
import uuid

from fastapi import APIRouter, Depends
from openai import OpenAI
from pydantic import BaseModel, Field
from sse_starlette.sse import EventSourceResponse

from config import settings
from src.constant.stream_prompt import AI_LEARNING_PROMPT, REASONING_CONTENT_PREFIX, REASONING_CONTENT_TAG
from src.logger.logUtil import get_logger
from src.model.article_manager import get_article_by_id
from src.model.lecture_ai import (
    AITYPE,
    FetchSource,
    RoleValue,
    create_ai_detail,
    find_ai_learning_history,
    find_all_ai_learning_history,
    get_or_create_ai_group,
)
from src.util.article_utils import get_article_lan_modified_text
from src.util.commonutil import common_error, common_success
from src.util.pandoc_util import clean_markdown_tags, convert_latex_in_markdown
from src.util.TokenUtil import get_current_user

doubao_client = OpenAI(api_key=settings.DOUBAO_API_KEY, base_url=settings.DOUBAO_BASE_URL)
zhipu_client = OpenAI(api_key=settings.ZHIPU_API_KEY, base_url=settings.ZHIPU_BASE_URL)

router = APIRouter(prefix="/api/v2/lecture-ai")

logger = get_logger(__name__)


def fetch_answer_from_deepseek_stream(params: dict, r1):
    try:
        params["model"] = settings.DOUBAO_MODEL
        if r1:
            params["model"] = settings.DOUBAO_R1_MODEL
        response = doubao_client.chat.completions.create(**params)
    except Exception:
        logger.error("doubao model 不可用, 将尝试使用 zhipu model", exc_info=True)
        try:
            params["model"] = settings.ZHIPU_MODEL
            if r1:
                params["model"] = settings.ZHIPU_R1_MODEL
            response = zhipu_client.chat.completions.create(**params)
        except Exception:
            logger.error("doubao and qianwen model 都不可用", exc_info=True)
            yield "AI 学习功能现在不可用，请您稍后重试"
            return
    for chunk in response:
        if hasattr(chunk.choices[0].delta, "reasoning_content") and chunk.choices[0].delta.reasoning_content:
            yield f"{REASONING_CONTENT_PREFIX}{chunk.choices[0].delta.reasoning_content}"
        yield chunk.choices[0].delta.content


async def event_generator(content, user_id, ai_group, params: dict, r1: bool):
    reason_ans = ""
    ans = ""
    for message in fetch_answer_from_deepseek_stream(params, r1):
        if message.startswith(REASONING_CONTENT_PREFIX):
            reason_content = message.replace(REASONING_CONTENT_PREFIX, "")
            if reason_content:
                yield {"data": json.dumps({"content": reason_content, "type": "thinking"}, ensure_ascii=False)}
                reason_ans += reason_content
        else:
            if message:
                yield {"data": json.dumps({"content": message, "type": "text"}, ensure_ascii=False)}
                ans += message
    ask_detail_biz_id = uuid.uuid4().hex
    create_ai_detail(
        user_id=user_id,
        group_biz_id=ai_group.biz_id,
        biz_id=ask_detail_biz_id,
        ai_type=AITYPE.AI_LEARNING.value,
        fetch_source=FetchSource.USER.value,
        content=content,
        role=RoleValue.USER.value,
    )
    ans_detail_biz_id = uuid.uuid4().hex
    combine_ans = REASONING_CONTENT_TAG.join([reason_ans, ans])
    create_ai_detail(
        user_id=user_id,
        group_biz_id=ai_group.biz_id,
        biz_id=ans_detail_biz_id,
        ai_type=AITYPE.AI_LEARNING.value,
        fetch_source=FetchSource.USER.value,
        content=combine_ans,
        role=RoleValue.ASSISTANT.value,
        token=len(combine_ans),
    )
    ai_group.used_count += 2
    ai_group.save()
    logger.info("Store AI study ans successfully")


async def history_generator(ans: str):
    # 这里由于旧 prompt 没有限制不要以 ```Markdown 开头
    ans = clean_markdown_tags(ans)
    # 这里由于旧 prompt 没有对公式替换为 $$ ... $$ 的形式，这里处理后返回给前端
    ans = convert_latex_in_markdown(ans)
    thinking_content = ""
    content = ans
    if ans and len(ans.split(REASONING_CONTENT_TAG)) >= 2:
        thinking_content, content = ans.split(REASONING_CONTENT_TAG)
    if thinking_content:
        yield {"data": json.dumps({"content": thinking_content, "type": "thinking"}, ensure_ascii=False)}
    yield {"data": json.dumps({"content": content, "type": "text"}, ensure_ascii=False)}
    logger.info("Return AI study ans from history")


class AIStudy(BaseModel):
    article_biz_id: str = Field(..., description="文章业务ID")
    content: str = Field(..., description="Text格式内容")
    r1: bool = False


@router.post("/ai-study-stream")
async def create_ai_study(ai_study: AIStudy, user=Depends(get_current_user)):
    """针对 AI study 单独的一个接口"""
    if ans := find_ai_learning_history(
        user.biz_id, ai_study.article_biz_id, AITYPE.AI_LEARNING.value, ai_study.content
    ):
        return EventSourceResponse(history_generator(ans))
    ai_group = get_or_create_ai_group(user_id=user.biz_id, article_biz_id=ai_study.article_biz_id)
    # 用户在这篇文章首次创建
    if not ai_group.check_can_use():
        return common_error("用户使用次数已经用完")
    article = get_article_by_id(ai_study.article_biz_id)
    if not article:
        return common_error(f"article: {ai_study.article_biz_id} 不存在")
    article_text = get_article_lan_modified_text(article.out_language, ai_study.article_biz_id)
    system_prompt = AI_LEARNING_PROMPT["SYSTEM"].format(article=article_text)
    user_prompt = AI_LEARNING_PROMPT.get(ai_study.content)
    if user_prompt:
        user_prompt = user_prompt.format(language=article.out_language)
    else:
        user_prompt = AI_LEARNING_PROMPT["Default"].format(asked=ai_study.content, language=article.out_language)
    messages = [
        {"role": "system", "content": system_prompt},
    ]
    history, _ = find_all_ai_learning_history(
        user.biz_id, ai_study.article_biz_id, AITYPE.AI_LEARNING.value, page_no=1, page_size=5
    )
    for item in history[::-1]:
        messages.append({"role": "assistant" if item["sender"] == "ai" else "user", "content": item["content"]})
    messages.append({"role": "user", "content": user_prompt})

    logger.info(f"message\n{messages}")
    params = {
        "messages": messages,
        "temperature": 0.1,
        "max_tokens": 4096,
        "timeout": 5 * 60,
        "stream": True,
    }
    return EventSourceResponse(event_generator(ai_study.content, user.biz_id, ai_group, params, ai_study.r1))


@router.get("/ask/history")
async def ai_study_message(article_biz_id: str, page_no: int = 1, page_size: int = 3, user=Depends(get_current_user)):
    # page_size 是以组的形式，3组为6个
    data, total = find_all_ai_learning_history(
        user_id=user.biz_id,
        article_biz_id=article_biz_id,
        ai_type=AITYPE.AI_LEARNING.value,
        page_no=page_no,
        page_size=page_size,
    )
    return common_success(data={"history": data, "total": total})
