from fastapi import APIRouter, Depends
from src.core import deps
from src.logger.logUtil import get_logger
from src.model.affiliates_manager import get_affiliates_by_user_id
from src.model.affiliates_order import get_count_and_page_by_affiliates_id
from src.util.commonutil import common_success
from src.util.TokenUtil import get_current_user
from src.services import affiliates_service

logger = get_logger(__name__)

router = APIRouter(prefix="/api/v1/affiliates/order", tags=["affiliates"])


@router.get("/list_fans")
async def order_list(page_size: int = 10, page_no: int = 1, user=Depends(get_current_user)):
    """
    获取分销商订单列表
    :param page_no: 页码
    :param page_size: 每页数量
    :param user: 请求用户
    :return: 分销商订单列表
    """
    user_id = user.biz_id
    affiliates = await get_affiliates_by_user_id(user_id)
    if not affiliates:
        return common_success()

    total, orders = get_count_and_page_by_affiliates_id(str(affiliates.biz_id), page_size, page_no)

    return common_success({"total": total, "list": [order.__data__ for order in orders]})


@router.get("/list")
async def order_list(
        page_size: int = 10, page_no: int = 1, user=Depends(get_current_user),
        async_db: deps.DBSessionDep = deps.DBSessionDep
):
    """
    获取粉丝以及订单列表
    :param page_no: 页码
    :param page_size: 每页数量
    :param user: 请求用户
    :param async_db
    :return: 分销商订单列表
    """
    user_id = user.biz_id
    data = await affiliates_service.search_union_paginate(
        async_db,
        page_no,
        page_size,
        user_id
    )
    return common_success(data)
