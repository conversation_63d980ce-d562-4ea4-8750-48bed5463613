import random
from datetime import datetime, timedelta, timezone
from uuid import UUID

from fastapi import APIRouter, Depends

from src.logger.logUtil import get_logger
from src.model.affiliates_fans import AffiliatesFans, get_fans_by_user_id, get_fans_count_and_page_by_affiliates_id
from src.model.affiliates_manager import AffiliatesStatus, get_affiliates_by_token, get_affiliates_by_user_id
from src.model.order_manager import update_points
from src.model.User_action_record_manager import UserActionProcess, UserActionType, add_user_action_record
from src.orm.pgrepo import db
from src.util.commonutil import common_error, common_success
from src.util.TokenUtil import get_current_user

logger = get_logger(__name__)

router = APIRouter(prefix="/api/v1/affiliates/fans", tags=["affiliates"])


@router.get("/bind")
async def bind_fans(token, user=Depends(get_current_user)):
    """
    绑定粉丝
    :param token: 邀请code
    :param user: 用户id
    :param request: 请求
    :return:
    """
    user_id = user.biz_id
    if token:
        affiliates = get_affiliates_by_token(token)
        if not affiliates:
            logger.error(f"token not exists, token={token}")
            return common_error(f"邀请码{token} 不存在")
        if affiliates.user_id == user_id:
            return common_error("can't bind yourself")
        if affiliates.status != AffiliatesStatus.SUCCESS:
            logger.error("Affiliates not audit success , can't bind fans")
            return common_error("Affiliates not audit success , can't bind fans")
    else:
        return common_error("token is empty")

    if affiliates.biz_id == user_id:
        return common_error("can't bind yourself")

    fans = get_fans_by_user_id(user_id)
    if fans and fans.affiliates_id != affiliates.biz_id:
        # 如果绑定时间小于 当前时间 -30，说明无法换绑
        if fans.bind_time < (datetime.now() - timedelta(days=30)):
            logger.error("fans already bind to another affiliates")
            return common_error("fans already bind to another affiliates")
        else:
            fans.affiliates_id = affiliates.biz_id
            fans.bind_time = datetime.now(tz=timezone.utc)
            fans.update_time = datetime.now(tz=timezone.utc)
            fans.save()

    if not fans:
        fans = AffiliatesFans.create(
            biz_id=str(UUID(int=random.getrandbits(128))),
            user_id=user_id,
            affiliates_id=affiliates.biz_id,
            name=user.name,
            bind_time=datetime.now(),
        )
        # send point and record
        try:
            with db.atomic():
                update_points(affiliates.user_id, "30积分")
                add_user_action_record(
                    affiliates.user_id, UserActionType.INVITE, UserActionProcess.ADD, "邀请用户", 30, fans.user_id
                )
        except Exception:
            logger.exception(f"Error add platform point,user_id={affiliates.biz_id}")
    return common_success(data={"id": fans.biz_id})


@router.get("/list")
async def get_fans_list(page_size: int = 10, page_no: int = 1, user=Depends(get_current_user)):
    """
    分页获取粉丝列表
    :param user: 请求用户
    :return: 粉丝列表
    """
    user_id = user.biz_id
    affiliates = await get_affiliates_by_user_id(user_id)
    if not affiliates:
        return common_success()
    total, fans = get_fans_count_and_page_by_affiliates_id(affiliates.biz_id, page_size, page_no)
    return common_success({"total": total, "list": [fan.__data__ for fan in fans]})
