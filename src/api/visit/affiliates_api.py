import json
import random
import string
from typing import Optional

from fastapi import APIRouter, Depends

from config import settings
from src.logger.logUtil import get_logger
from src.model.affiliates_manager import (
    AffiliatesApply,
    AffiliatesStatus,
    audit_affiliates_by_biz_id,
    create_affiliates_by_user_id,
    get_affiliates_by_token,
    get_affiliates_by_user_id,
)
from src.model.member_manager import get_member_info
from src.util.commonutil import common_error, common_success
from src.util.TokenUtil import get_current_user

logger = get_logger(__name__)

router = APIRouter(prefix="/api/v1/affiliates", tags=["affiliates"])


@router.post("/apply")
async def create_affiliates(applyReq: AffiliatesApply, user=Depends(get_current_user)):
    """
    创建分销商
    :param applyReq:  |
    :param token: 邀请code
    :param remark: 备注json, 例如{"occupation":"teacher","school":"xxx"}
    :param user: 用户id
    :param request: 请求
    :return:
    """
    user_id = user.biz_id
    affiliates = await get_affiliates_by_user_id(user_id)
    if affiliates:
        return common_error("Affiliates already exists")
    if applyReq.token:
        affiliates = get_affiliates_by_token(applyReq.token)
        if affiliates:
            return common_error("token already exists")
    else:
        applyReq.token = "".join(random.sample(string.ascii_letters + string.digits, 8))
    await create_affiliates_by_user_id(
        user_id, applyReq.contact, bind_token=applyReq.token, remark=json.dumps(applyReq.dict(), ensure_ascii=False)
    )
    return common_success()


@router.get("/get")
async def get_affiliates(user=Depends(get_current_user)):
    """
    获取分销商
    :param user: 请求用户
    :return: 分销商信息
    """
    user_id = user.biz_id
    affiliates = await get_affiliates_by_user_id(user_id)
    if not affiliates:
        return common_success()

    member = get_member_info(user_id)
    point = member.points if member else 0

    return common_success(
        {
            "current_point": point,
            "total_point": affiliates.total_point or 0,
            "bind_token": affiliates.bind_token,
            "biz_id": affiliates.biz_id,
            "fans_count": affiliates.fans_count,
        }
    )


@router.get("/get_or_create")
async def get_or_create_affiliates(user=Depends(get_current_user)):
    """
    获取分销商
    :param user: 请求用户
    :return: 分销商信息
    """
    user_id = user.biz_id
    affiliates = await get_affiliates_by_user_id(user_id)
    if not affiliates:
        token = "".join(random.sample(string.ascii_letters + string.digits, 8))
        aff = get_affiliates_by_token(token)
        if not aff:
            token = await create_affiliates_by_user_id(
                user_id,
                user.mobile or user.email,
                token,
                "{}",
            )
        return common_success({"token": token})
    return common_success(data={"token": affiliates.bind_token})


@router.post("/admin/create")
async def admin_create_affiliates(user_id: str, token: Optional[str] = None, user=Depends(get_current_user)):
    """
    创建分销商 by admin
    :param user_id: 用户id
    :param token: 邀请code
    :param user: 用户id
    :return:
    """
    customers = settings.CUSTOMER_SERVICE_SEND.split(",")
    if user.biz_id not in customers:
        return common_error("非法操作")
    affiliates = await get_affiliates_by_user_id(user_id)
    if affiliates:
        return common_error("Affiliates already exists")
    affiliates = get_affiliates_by_token(token)
    if affiliates:
        return common_error("token already exists")

    token = await create_affiliates_by_user_id(user_id, user.mobile or user.email, token)

    await audit_affiliates_by_biz_id(user_id, AffiliatesStatus.SUCCESS)

    return common_success({"token": token})


@router.post("/admin/audit")
async def audit_affiliates(affiliates_id: str, status: AffiliatesStatus, user=Depends(get_current_user)):
    """
    audit_affiliates
    :param status:
    :param affiliates_id
    :return: void
    """
    customers = settings.CUSTOMER_SERVICE_SEND.split(",")
    if user.biz_id not in customers:
        return common_error("非法操作")
    await audit_affiliates_by_biz_id(affiliates_id, status)
    return common_success()
