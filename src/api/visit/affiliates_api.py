import json
import random
import string
from typing import Optional

from fastapi import APIRouter, Depends

from config import settings
from src.logger.logUtil import get_logger
from src.model.affiliates_manager import (
    AffiliatesApply,
    AffiliatesStatus,
    audit_affiliates_by_biz_id,
    create_affiliates_by_user_id,
    get_affiliates_by_token,
    get_affiliates_by_user_id,
)
from src.model.member_manager import get_member_info
from src.util.commonutil import common_error, common_success
from src.util.TokenUtil import get_current_user
from src.core import deps
from src.services import share_service

logger = get_logger(__name__)

router = APIRouter(prefix="/api/v1/affiliates", tags=["affiliates"])


@router.post("/apply")
async def create_affiliates(applyReq: AffiliatesApply, user=Depends(get_current_user)):
    """
    创建分销商
    :param applyReq:  |
    :param token: 邀请code
    :param remark: 备注json, 例如{"occupation":"teacher","school":"xxx"}
    :param user: 用户id
    :param request: 请求
    :return:
    """
    user_id = user.biz_id
    affiliates = await get_affiliates_by_user_id(user_id)
    if affiliates:
        return common_error("Affiliates already exists")
    if applyReq.token:
        affiliates = get_affiliates_by_token(applyReq.token)
        if affiliates:
            return common_error("token already exists")
    else:
        applyReq.token = "".join(random.sample(string.ascii_letters + string.digits, 8))
    await create_affiliates_by_user_id(
        user_id, applyReq.contact, bind_token=applyReq.token, remark=json.dumps(applyReq.dict(), ensure_ascii=False)
    )
    return common_success()


@router.get("/get")
async def get_affiliates(
        user=Depends(get_current_user),
        async_db: deps.DBSessionDep = deps.DBSessionDep
):
    """
    获取分销商
    :param user: 请求用户
    :param async_db
    :return: 分销商信息
    """
    user_id = user.biz_id
    affiliates = await get_affiliates_by_user_id(user_id)
    invite_points = 0
    bind_token = None
    biz_id = None
    fans_count = 0
    if affiliates:
        invite_points = affiliates.total_point
        bind_token = affiliates.bind_token
        biz_id = affiliates.biz_id
        fans_count = affiliates.fans_count

    share_points = await share_service.points_all(async_db, user_id)
    member = get_member_info(user_id)
    point = member.points if member else 0
    share_fans = await share_service.fans_all(async_db, user_id)
    return common_success(
        {
            "current_point": point,
            "total_point": share_points + invite_points,
            "bind_token": bind_token,
            "biz_id": biz_id,
            "fans_count": fans_count + share_fans,
        }
    )


@router.get("/get_or_create")
async def get_or_create_affiliates(user=Depends(get_current_user)):
    """
    获取分销商
    :param user: 请求用户
    :return: 分销商信息
    """
    user_id = user.biz_id
    affiliates = await get_affiliates_by_user_id(user_id)
    if not affiliates:
        token = "".join(random.sample(string.ascii_letters + string.digits, 8))
        aff = get_affiliates_by_token(token)
        if not aff:
            token = await create_affiliates_by_user_id(
                user_id,
                user.mobile or user.email,
                token,
                "{}",
            )
        return common_success({"token": token})
    return common_success(data={"token": affiliates.bind_token})


@router.post("/admin/create")
async def admin_create_affiliates(user_id: str, token: Optional[str] = None, user=Depends(get_current_user)):
    """
    创建分销商 by admin
    :param user_id: 用户id
    :param token: 邀请code
    :param user: 用户id
    :return:
    """
    customers = settings.CUSTOMER_SERVICE_SEND.split(",")
    if user.biz_id not in customers:
        return common_error("非法操作")
    affiliates = await get_affiliates_by_user_id(user_id)
    if affiliates:
        return common_error("Affiliates already exists")
    affiliates = get_affiliates_by_token(token)
    if affiliates:
        return common_error("token already exists")

    token = await create_affiliates_by_user_id(user_id, user.mobile or user.email, token)

    await audit_affiliates_by_biz_id(user_id, AffiliatesStatus.SUCCESS)

    return common_success({"token": token})


@router.post("/admin/audit")
async def audit_affiliates(affiliates_id: str, status: AffiliatesStatus, user=Depends(get_current_user)):
    """
    audit_affiliates
    :param status:
    :param affiliates_id
    :return: void
    """
    customers = settings.CUSTOMER_SERVICE_SEND.split(",")
    if user.biz_id not in customers:
        return common_error("非法操作")
    await audit_affiliates_by_biz_id(affiliates_id, status)
    return common_success()
