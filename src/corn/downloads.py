import ast
import asyncio
import json
import os
import time
from datetime import datetime, timezone
from pathlib import Path
from typing import List

import aiofiles
import aiohttp

from PIL import Image
from config import settings
from src.logger.logUtil import get_celery_logger
from src.model.article_detail_manager import LectureArticleDetail
from src.model.article_manager import LectureArticle, get_article_by_id
from src.model.download_manager import ExportFormatEnum, LectureAsyncDownload
from src.util.article_utils import parse_total_summary
from src.util.celery_util import celery
from src.util.html_utils.styles_renderer import render_article
from src.util.pandoc_util import convert_md_to_docx, convert_md_to_pdf

celery_logger = get_celery_logger(__name__)

FILENAME_MAX_LENGTH = 60


def default_markdown_header(article, downloads: LectureAsyncDownload):
    text = f"# {article.name}\n\n" + f"## 视频来源\n\n{article.video_url}\n\n"
    if downloads.ai_features:
        if "textOutline" in downloads.ai_features:
            outline = (article.translated_outline_json or {}).get(downloads.language or "zh")
            text += f"## 大纲\n\n{outline}\n\n"
        if "AISummary" in downloads.ai_features:
            total_summary = parse_total_summary(
                (article.translated_total_summary_json or {}).get(downloads.language or "zh") or {},
                str(downloads.language or "zh"),
            )
            text += f"## 总结\n\n{total_summary}\n\n"
    return text


def webp_to_jpg(input_path, output_path=None):
    """
    将 WebP 图片转换为 JPG 格式。

    Args:
        input_path: 输入的 .webp 文件路径
        output_path: 输出的 .jpg 文件路径；如果为 None，则使用相同文件名替换扩展名
    """
    if output_path is None:
        output_path = os.path.splitext(input_path)[0] + '.jpg'

    with Image.open(input_path) as img:
        img = img.convert('RGB')  # 转为 RGB，JPG 不支持透明通道
        img.save(output_path, 'JPEG')
    return output_path

def insert_local_img(oss_pic_path, article_id: str, downloads):
    if not oss_pic_path:
        return ""  # 或者返回 None，根据业务逻辑决定是否插入图片
    path_prefix = f"{settings.DOWNLOAD_PREFIX}/{article_id}/img"
    # 尝试解析 JSON 列表或字符串
    paths = []
    try:
        paths = json.loads(oss_pic_path.replace("'", '"'))
    except (json.JSONDecodeError, TypeError):
        if isinstance(oss_pic_path, str):
            paths = [oss_pic_path.strip("[]").strip("'")]
        else:
            paths = []
    Path(path_prefix).mkdir(parents=True, exist_ok=True)
    string = ""
    for path in paths:
        if not path or not path.startswith("http"):
            continue
        filename = path.split("/")[-1]
        local_path = f"{path_prefix}/{filename}"
        if 'webp' in filename:
            local_path = webp_to_jpg(local_path)
        # 处理导出格式为 DOCX/PDF 的情况
        if downloads.export_format in (ExportFormatEnum.DOCX.value, ExportFormatEnum.PDF.value):
            md_path = local_path
        else:
            md_path = path
        # 确保 md_path 不为空再进行 replace 操作
        if md_path:
            md_path = md_path.replace(" ", "%20")
            string += f"![]({md_path})\n"
    return string


def default_markdown_content(
    detail_arr: list[LectureArticleDetail], downloads: LectureAsyncDownload, article: LectureArticle
) -> str:
    text = ""
    for section in detail_arr:
        if "withImages" in (downloads.display_options or []):
            has_img = bool(ast.literal_eval(str(section.oss_pic_path)))
            if has_img:
                oss_pic_path = insert_local_img(section.oss_pic_path, str(section.article_id), downloads) + "\n"
                text += oss_pic_path

        if "speaker" in (downloads.display_options or []) and "timestamp" in (downloads.display_options or []):
            # 说话人0：xxxx
            if section.speaker:
                text += section.speaker + ": "
            text += "*" + str(section.start_time) + " - " + str(section.end_time) + "*\n\n"
        elif "speaker" in (downloads.display_options or []):
            # 说话人0：
            text += section.speaker + ":\n\n" if section.speaker else ""
        elif "timestamp" in (downloads.display_options or []):
            time_range = "*" + str(section.start_time) + " - " + str(section.end_time) + "*\n\n"
            text += time_range

        if "originalText" == downloads.reading_mode:
            text += section.origin_text + "\n\n"
        else:
            lan = downloads.language or article.language
            if "bilingual" in (downloads.display_options or []):
                if lan_text := (section.translated_modified_text_json or {}).get(lan):
                    text += lan_text + "\n\n"
                if lan != article.out_language:
                    if out_language_text := (section.translated_modified_text_json or {}).get(article.out_language):
                        text += out_language_text + "\n\n"
                if lan != article.language:
                    if language_text := (section.translated_modified_text_json or {}).get(article.language):
                        text += language_text + "\n\n"
            else:
                text += (section.translated_modified_text_json or {}).get(lan) or ""
        text += "\n\n\n\n"
    return text


async def download_pic_by_url(session, url, path_prefix):
    filename = os.path.join(path_prefix, url.split("/")[-1])
    if os.path.exists(filename):
        return
    try:
        async with session.get(url) as response:
            if response.status == 200:
                async with aiofiles.open(filename, "wb") as f:
                    await f.write(await response.read())
            else:
                celery_logger.info(f"Failed: {url} - Status {response.status}")
    except Exception as e:
        celery_logger.error(f"Error downloading {url}: {e}")


async def download(urls, path_prefix, max_concurrent=10):
    semaphore = asyncio.Semaphore(max_concurrent)  # 限制并发数
    async with aiohttp.ClientSession() as session:
        tasks = [async_download(url, path_prefix, session, semaphore) for url in urls]
        await asyncio.gather(*tasks)


async def async_download(url, path_prefix, session, semaphore):
    async with semaphore:  # 控制同时进行的任务数
        await download_pic_by_url(session, url, path_prefix)


def download_pics(detail_arr: list[LectureArticleDetail], article_id: str):
    if not os.path.exists(settings.DOWNLOAD_PREFIX):
         os.makedirs(settings.DOWNLOAD_PREFIX, exist_ok=True)
    path_prefix = Path(settings.DOWNLOAD_PREFIX) / Path(article_id) / Path("img")
    ## 创建对应文件夹如果不存在
    if path_prefix:
        os.makedirs(path_prefix, exist_ok=True)
    urls = []
    for section in detail_arr:
        if section.oss_pic_path:
            oss_pic_path = ast.literal_eval(str(section.oss_pic_path))
            if isinstance(oss_pic_path, list):
                urls.extend(oss_pic_path)
            elif isinstance(oss_pic_path, str) and oss_pic_path.startswith("http"):
                urls.append(oss_pic_path)

    start = time.time()
    asyncio.run(download(urls, path_prefix, max_concurrent=10))
    celery_logger.info(f"download {len(urls)} pics spend: {time.time() - start}")


def generate_file_url(relate_biz_id, downloads_biz_id, article_name, suffix):
    article_name = article_name[:FILENAME_MAX_LENGTH]
    file_url = (
        Path(settings.DOWNLOAD_PREFIX) / Path(relate_biz_id) / Path(downloads_biz_id) / Path(f"{article_name}.{suffix}")
    )
    if not os.path.exists(file_url.parent):
        os.makedirs(file_url.parent)
    return file_url


def generate_file(markdown, downloads, article):
    file_url = generate_file_url(downloads.relate_biz_id, downloads.biz_id, article.name, "md")
    if downloads.export_format == ExportFormatEnum.MD.value:
        with open(file_url, "w") as f:
            f.write(markdown)
        downloads.download_url = file_url
        downloads.status = 3
    elif downloads.export_format == ExportFormatEnum.HTML.value:
        html_content = render_article(markdown)
        html_file_url = generate_file_url(downloads.relate_biz_id, downloads.biz_id, article.name, "html")
        with open(html_file_url, "w") as f:
            f.write(html_content)
        downloads.status = 3
        downloads.download_url = html_file_url
    elif downloads.export_format == ExportFormatEnum.PDF.value:
        pdf_file_url = generate_file_url(downloads.relate_biz_id, downloads.biz_id, article.name, "pdf")
        start = time.time()
        if convert_md_to_pdf(markdown=markdown, pdf_file_url=str(pdf_file_url)):
            celery_logger.info(f"md to pdf spend: {time.time() - start}")
            downloads.download_url = pdf_file_url
            downloads.status = 3
        else:
            downloads.status = 4
            celery_logger.info(f"md to pdf {pdf_file_url} failed")
    elif downloads.export_format == ExportFormatEnum.DOCX.value:
        docx_file_url = generate_file_url(downloads.relate_biz_id, downloads.biz_id, article.name, "docx")
        start = time.time()
        if convert_md_to_docx(markdown=markdown, docx_file_url=str(docx_file_url)):
            celery_logger.info(f"md to docx spend: {time.time() - start}")
            downloads.download_url = docx_file_url
            downloads.status = 3
        else:
            downloads.status = 4
            celery_logger.info(f"md to docx {docx_file_url} failed")
    else:
        celery_logger.info(f"不支持 export_format: {downloads.export_format}")
    downloads.update_time = datetime.now(tz=timezone.utc)
    downloads.save()


@celery.task
def article_async_download(export_id: str):
    """下载word"""
    celery_logger.info(f"download_file: {export_id}")
    downloads: LectureAsyncDownload = (
        LectureAsyncDownload.select().where(LectureAsyncDownload.biz_id == export_id).get_or_none()
    )

    if not downloads:
        return

    if downloads.status != 1:
        return
    try:
        update_to_processing(downloads)

        # 下载文件
        article = get_article_by_id(downloads.relate_biz_id)
        if not article:
            downloads.status = 4
            downloads.remark = "文章不存在"
            downloads.update_time = datetime.now(tz=timezone.utc)
            downloads.save()
            return

        # 下载文件detail
        # 获取文章列表
        query = LectureArticleDetail.select().where(
            (LectureArticleDetail.delete_flag == 0) & (LectureArticleDetail.article_id == downloads.relate_biz_id)
        )
        details: List[LectureArticleDetail] = query.order_by(LectureArticleDetail.start_time)
        if not details:
            downloads.status = 4
            downloads.remark = "文章详细不存在"
            downloads.update_time = datetime.now(tz=timezone.utc)
            downloads.save()
            return

        # 拼装组合
        markdown = default_markdown_header(article, downloads)

        if downloads.reading_mode:
            # 下载图片
            if downloads.display_options and "withImages" in downloads.display_options:
                download_pics(details, str(downloads.relate_biz_id))
            # 拼接文章
            markdown = markdown + default_markdown_content(details, downloads, article)
        # 生成文件
        generate_file(markdown, downloads, article)

    except Exception as e:
        celery_logger.error(f"download_word {export_id} error", exc_info=True)
        downloads.status = 4
        downloads.remark = str(e)
        downloads.update_time = datetime.now(tz=timezone.utc)
        downloads.save()


def update_to_processing(downloads):
    if downloads.status == 1:
        downloads.status = 2
        downloads.update_time = datetime.now(tz=timezone.utc)
        downloads.save()


def _remove_downloads_with_files():
    # 只用于线上更新某篇文章的所有任务为失败，这是由 20250311 日前 Bug 失败时没有更新 status 为 4 而不能创建相同任务
    # 本次代码已经修复这个问题，而这段代码就是手动更新
    # 已经更新这些 article_biz_id
    article_biz_ids = [
        "0aac7579-e263-e0d5-fd25-da84850387f1",
        "0ea121b1-f86f-1451-bd39-b65c78fa6752",
        "0ef3dfaf-febc-c689-535e-4e522baf70f5",
        "5eca19a6-cab2-2848-704c-1dcdf44398d4",
        "6d004285-2dc0-0c34-345c-d8da3cd02f06",
        "6e63b65e-f327-a36b-61ea-edf883c68961",
        "763fd72e-a40d-32fc-83e9-40d141484126",
        "88da4208-8a2b-96c8-94af-05e92d512fbd",
        "99c53b16-70da-23c4-4397-455b7b6b8ba6",
        "a745a06e-8c86-92d5-4917-92e64e86e13c",
        "a8694b25-41c2-e121-d283-e45bf6737a3a",
        "aba974c5-1e32-695f-3489-a7c7fee7fb2b",
        "ce354200-92d9-4ac7-3647-55ef243812b9",
        "ddc17f93-df3b-2e77-92d7-d1cc05844f01",
    ]
    for article_biz_id in article_biz_ids:
        downloads = LectureAsyncDownload.select().where(LectureAsyncDownload.relate_biz_id == article_biz_id)
        for download in downloads:
            download.status = 4
            download.save()
            download.update_time = datetime.now(tz=timezone.utc)
            print(download.biz_id, download.status)
