import json

from config import settings
from src.logger.logUtil import get_logger
from src.model.track_event import TrackEvent
from src.util.redis_util import get_redis_client

logger = get_logger(__name__)


def _store_track_event(message):
    event = json.loads(message["data"])
    try:
        TrackEvent.create(**event)
        logger.info("store track event success")
    except Exception:
        logger.error(f"store track event: {event} failed")


def subscribe_track_event():
    redis_client = get_redis_client()
    if redis_client:
        pubsub = redis_client.pubsub()
        pubsub.subscribe(**{settings.REDIS_EVENT_CHANNEL: _store_track_event})
        pubsub.run_in_thread(sleep_time=1.0)
    else:
        logger.error("There is not a redis_client")
