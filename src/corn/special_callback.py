import asyncio
import json
from datetime import datetime
from urllib.parse import parse_qs, urlparse

import aiohttp

from src.logger.logUtil import get_celery_logger
from src.util.celery_util import celery

celery_logger = get_celery_logger(__name__)


def extract_url_and_params(full_url):
    if not full_url:
        raise ValueError("callback_url is None")
    parsed_url = urlparse(full_url)  # 解析 URL
    url = f"{parsed_url.scheme}://{parsed_url.netloc}{parsed_url.path}"  # 组装基础 URL
    params = {k: v[0] for k, v in parse_qs(parsed_url.query).items()}  # 解析查询参数
    return url, params


def json_serializer(obj):
    """自定义 JSON 序列化器，处理 datetime 类型"""
    if isinstance(obj, datetime):
        return obj.isoformat()  # 转换为 ISO 8601 格式字符串
    raise TypeError(f"Type {type(obj)} not serializable")


async def async_post_request(url, data):
    """使用 aiohttp 发送异步 POST 请求"""
    async with aiohttp.ClientSession() as session:
        async with session.post(url, data=json.dumps(data, default=json_serializer), timeout=10) as response:
            return response.status, await response.json()


@celery.task
def sync_special_callback(task_id: str, callback_url: str, param: dict):
    try:
        celery_logger.info(f"start callback task:{task_id}\ncallback_url:{callback_url}\nparam::{param}")
        status, response = asyncio.run(async_post_request(callback_url, param))

        if status == 200:
            celery_logger.info(
                f"{task_id} notifying {callback_url} with status {status},  response {response} sueecssed"
            )
        else:
            celery_logger.error(
                f"{task_id} notifying {callback_url} with status {status}, response {response} failed"
            )
    except Exception:
        celery_logger.error(f"{task_id} notifying exception", exc_info=True)
