from src.model.user_manager import get_user_by_mobile, hash_password


def bind_email(phone: str, email: str, password: str):
    """
    :param phone: 电话
    :param email: 邮箱
    :param password: password
    :return:
    """

    po = get_user_by_mobile(phone)

    if not po:
        print(f"手机号用户：{phone} 不存在")

    # 更新是否有验证邮箱
    po.mail_access = True
    po.email = email
    po.password = hash_password(password)
    po.save()
    print(f"Bind email success for phone: {phone}")


if __name__ == "__main__":
    bind_email("", "<EMAIL>", "YOUR_PASSWORD")
