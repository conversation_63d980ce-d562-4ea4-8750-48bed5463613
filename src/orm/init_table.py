from peewee import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, SmallIntegerField, TextField
from playhouse.migrate import PostgresqlMigrator, migrate
from playhouse.postgres_ext import <PERSON><PERSON><PERSON><PERSON>

from src.model.User_action_record_manager import LectureUserActionRecord
from src.model.affiliates_fans import AffiliatesFans
from src.model.affiliates_manager import Affiliates
from src.model.affiliates_order import AffiliatesOrder
from src.model.article_detail_manager import LectureArticleDetail
from src.model.article_manager import LectureArticle
# 文章内容划线记录表
from src.model.article_record_manager import LectureArticleRecord
from src.model.book2audio import BookTask
from src.model.download_manager import LectureAsyncDownload
from src.model.exchange_code import ExchangeCode, ExchangeRecord
from src.model.folder import Folder
from src.model.lecture_ai import LectureAIDetail, LectureAIGroup, LectureAITask
from src.model.lecture_plan_manager import LecturePlan
from src.model.member_manager import LectureMember
from src.model.oplatform_manager import OpenPlatform
from src.model.order_manager import LectureOrders
from src.model.task_manager import LectureTask
from src.model.track_event import TrackEvent
from src.model.url_detail import LectureUrlDetail
from src.model.user_manager import UserPO
from src.model.special_manager import SpecialMarkdownTask, SpecialMarkdownContent
from src.model import temp_create_manager

from src.model.platform_user_manager import PlatformUser, init_platform_admin
from src.orm.pgrepo import db


# 创建表
def create_table(table):
    if not table.table_exists():
        table.create_table()


def init_all_tables():
    create_table(AffiliatesFans)
    create_table(Affiliates)
    create_table(AffiliatesOrder)
    create_table(LectureArticleDetail)
    create_table(LectureArticle)
    create_table(ExchangeCode)
    create_table(ExchangeRecord)
    create_table(LecturePlan)
    create_table(LectureMember)
    create_table(OpenPlatform)
    create_table(LectureOrders)
    create_table(LectureTask)
    create_table(LectureUrlDetail)
    create_table(LectureUserActionRecord)
    create_table(UserPO)
    create_table(TrackEvent)
    create_table(LectureAIGroup)
    create_table(LectureAIDetail)
    create_table(LectureAITask)
    create_table(LectureAsyncDownload)
    create_table(Folder)
    # BookTask.drop_table()
    create_table(BookTask)

    # 文章内容划线记录表
    create_table(LectureArticleRecord)
    create_table(PlatformUser)

    # 特供接口 AI播客
    create_table(SpecialMarkdownContent)
    create_table(SpecialMarkdownTask)

    create_table(temp_create_manager.TempAppVersion)
    create_table(temp_create_manager.TempApprovalConfig)
    create_table(temp_create_manager.TempApprovalRequest)
    create_table(temp_create_manager.TempApprovalProcess)

    create_table(temp_create_manager.TempShare)
    create_table(temp_create_manager.TempShareAccess)

    create_table(temp_create_manager.TempUserRoleModel)
    create_table(temp_create_manager.TempRoleModel)
    create_table(temp_create_manager.TempPermissionModel)
    # temp_create_manager.init_approval_config()


def migrate_table_lecture_article():
    migrator = PostgresqlMigrator(db)

    author = CharField(null=True)
    audio_url = CharField(null=True, max_length=2048)
    oss_pdf_url = CharField(null=True, max_length=2048)
    oss_word_url = CharField(null=True, max_length=2048)
    corpus = TextField(null=True)
    translated_outline_json = JSONField(null=True)
    translated_total_summary_json = JSONField(null=True)
    with db.atomic():
        migrate(
            migrator.add_column("lecture_article", "author", author),
            migrator.add_column("lecture_article", "audio_url", audio_url),
            migrator.add_column("lecture_article", "oss_pdf_url", oss_pdf_url),
            migrator.add_column("lecture_article", "oss_word_url", oss_word_url),
            migrator.add_column("lecture_article", "corpus", corpus),
            migrator.add_column("lecture_article", "translated_outline_json", translated_outline_json),
            migrator.add_column("lecture_article", "translated_total_summary_json", translated_total_summary_json),
            migrator.alter_column_type("lecture_article", "img_url", CharField(null=True)),
        )


def migrate_table_lecture_article_detail():
    migrator = PostgresqlMigrator(db)

    index = IntegerField(null=True)
    local_pic_path = TextField(null=True)
    pic_keywords = TextField(null=True)
    translated_modified_text_json = JSONField(null=True)

    with db.atomic():
        migrate(
            migrator.add_column("lecture_article_detail", "index", index),
            migrator.add_column("lecture_article_detail", "local_pic_path", local_pic_path),
            migrator.add_column("lecture_article_detail", "pic_keywords", pic_keywords),
            migrator.add_column(
                "lecture_article_detail", "translated_modified_text_json", translated_modified_text_json
            ),
        )


def migrate_20250228():
    migrator = PostgresqlMigrator(db)

    # lecture_tasks
    folder_id = IntegerField(null=True)

    # affiliates
    total_point = IntegerField(default=0)

    # affiliates_order
    affiliates_goods_type = SmallIntegerField(default=1)
    order_point = IntegerField(null=True)

    with db.atomic():
        migrate(migrator.add_column("lecture_tasks", "folder_id", folder_id))

        migrate(migrator.add_column("affiliates", "total_point", total_point))
        migrate(migrator.drop_column("affiliates", "amount"))
        migrate(migrator.drop_column("affiliates", "withdraw_amount"))

        migrate(migrator.add_column("affiliates_order", "affiliates_goods_type", affiliates_goods_type))
        migrate(migrator.add_column("affiliates_order", "order_point", order_point))
        migrate(migrator.drop_column("affiliates_order", "product_distribution_config_code"))
        migrate(migrator.drop_column("affiliates_order", "affiliates_amount"))
        migrate(migrator.drop_column("affiliates_order", "affiliates_percent"))


def migrate_folder():
    migrator = PostgresqlMigrator(db)

    depth = IntegerField(default=1, null=True)  # 文件夹层级
    total = IntegerField(default=0, null=True)  # 该文件夹下的子文件夹及任务的数量
    caption_list = JSONField(null=True)

    with db.atomic():
        migrate(migrator.add_column("folder", "depth", depth))
        migrate(migrator.add_column("folder", "total", total))
        migrate(migrator.add_column("booktask", "caption_list", caption_list))
