import random
from uuid import UUI<PERSON>

from src.api.task.folder_api import get_folder_content_count, get_folder_depth
from src.model.folder import Folder
from src.model.task_manager import LectureTask
from src.model.user_manager import User<PERSON>
from src.orm.pgrepo import db

DEFAULT_FOLDER = "默认笔记本"


def init_folder():
    all_users = UserPO.select(UserPO.id, UserPO.biz_id).where(UserPO.delete_flag == 0)

    for user in all_users:
        folder, _ = Folder.get_or_create(
            name=DEFAULT_FOLDER,
            parent_id=None,
            user_id=user.biz_id,
            delete_flag=0,
            defaults={"biz_id": f"folder_{UUID(int=random.getrandbits(128))}"},
        )
        with db.atomic():
            tasks = LectureTask.select(LectureTask.id, LectureTask.folder_id).where(
                LectureTask.user_id == user.biz_id,
                LectureTask.delete_flag == 0,
                LectureTask.folder_id == None,  # noqa
            )
            for task in tasks:
                print(f"task, {task.biz_id} folder.id: {folder.id}")
                task.folder_id = folder.id
                task.save()


def depth_folder():
    for folder in Folder.select():
        with db.atomic():
            folder.depth = get_folder_depth(folder.id)
            folder.total = get_folder_content_count(folder, folder.user_id)
            folder.save()


if __name__ == "__main__":
    depth_folder()
