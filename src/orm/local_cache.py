import datetime

from dogpile.cache import make_region
from wechatpy.session import SessionStorage


class LocalCache(SessionStorage):
    def __init__(self, name, ttl: int = 7):
        self.my_dictionary = {}
        self.region = make_region(name=name).configure(
            "dogpile.cache.memory",
            expiration_time=datetime.timedelta(days=ttl).total_seconds(),
            arguments={"cache_dict": self.my_dictionary},
        )

    def get_region(self):
        return self.region

    def get(self, key, default=None):
        return self.region.get(key)

    def set(self, key, value, ttl=None):
        if value is None:
            return
        self.region.set(key, value)

    def delete(self, key):
        self.region.delete(key)


# WeChatClient 里的 session 需要 set 能传递 3 个参数，故这个没有替换成 Redis 缓存
local_common_cache = LocalCache("common")
