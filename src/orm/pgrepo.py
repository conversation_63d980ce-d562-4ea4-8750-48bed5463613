import time

from playhouse.pool import PooledPostgresqlExtDatabase
from playhouse.shortcuts import ReconnectMixin

from config import settings

db_config = {
    "database": settings.DB_NAME,
    "user": settings.DB_USER,
    "max_connections": settings.DB_MAX_CONNECTIONS,
    "stale_timeout": settings.DB_STALE_TIMEOUT,
    "password": settings.DB_PASSWORD,
    "host": settings.DB_HOST,
    "port": settings.DB_PORT,
}


class ReconnectPooledPostgresqlExtDatabase(ReconnectMixin, PooledPostgresqlExtDatabase):
    _instance = None

    @classmethod
    def get_db_instance(cls, db_config):
        if not cls._instance:
            cls._instance = cls(**db_config)
        return cls._instance

    def execute_sql(self, sql, params=None, commit=True):
        max_retries = 3
        delay = 1
        retries = 0

        while retries < max_retries:
            try:
                return super(ReconnectPooledPostgresqlExtDatabase, self).execute_sql(sql, params, commit)
            except Exception as e:
                print(f"数据库操作失败: {e}. 正在重试 {retries + 1}/{max_retries}...")
                retries += 1
                time.sleep(delay)
                self.close()  # 关闭现有连接
                self.connect()  # 重连数据库

        raise Exception(f"数据库操作在 {max_retries} 次重试后失败.")


db = ReconnectPooledPostgresqlExtDatabase.get_db_instance(db_config)
