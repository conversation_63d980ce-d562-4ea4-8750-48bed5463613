from dogpile.cache import make_region

from config import settings

# 创建缓存区域
common_cache = make_region().configure(
    "dogpile.cache.redis",
    arguments={
        "host": settings.REDISHOST,
        "port": settings.REDISPORT,
        "redis_expiration_time": 60 * 60 * 24,  # 24小时，Redis的过期时间
        # 如果需要密码
        "username": settings.REDISUSER,
        "password": settings.REDISPASSWORD,
    },
    # dogpile 的过期时间
    expiration_time=60 * 60 * 24,
)
