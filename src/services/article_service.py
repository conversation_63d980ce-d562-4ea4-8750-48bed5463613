import asyncio
import json
from datetime import datetime
from typing import Literal

from pydantic import BaseModel, Field
from redis.asyncio import Redis
from sqlalchemy import or_, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload

from config import settings
from src.database import models, constants, myredis
from src.model.task_manager import LectureTask
from src.util import redis_keys_util, stringify, dateUtil, pandoc_util
from src.util.commonutil import common_error, ErrorCodeEnum, common_enum_error


class UnderlineSchema(BaseModel):
    """创建划线记录"""
    biz_id: str = Field(..., description="content 列表的 biz_id")
    type: Literal['polish', 'original'] = Field(..., description="限定值 polish or original")
    change_text: str = Field(..., description="content 改变后的内容 应该带有 包含 record_id的标签内容")
    record_id: str = Field(..., description="record_id 与划线位置的标签内的 record_id 一致")
    record_context: str = Field(..., description="记录内容")
    language: str = Field('zh',
                          description="如果是polish，则记录位置和修改内容与当前划线时选择的语言对应，不处理翻译对应内容")


class UpdateUnderlineSchema(BaseModel):
    """更新划线记录"""
    record_id: str = Field(..., description="记录Id")
    context_id: str = Field(..., description="记录的每一条数据对应的 Id 后台生成的唯一值")
    record_context: str = Field(..., description="新的内容")
    language: str = Field(..., description="当前内容的语言类型, 影响返回结果对应的数据")


class HighLightSchema(BaseModel):
    """高亮"""
    biz_id: str = Field(..., description="content 列表的 biz_id")
    type: Literal['polish', 'original'] = Field(..., description="限定值 polish or original")
    change_text: str = Field(..., description="content 改变后的内容 应该带有 包含 record_id的标签内容")
    language: str = Field('zh',
                          description="如果是 polish ，则记录位置和修改内容 与当前划线时选择的语言对应，不处理翻译对应内容")


class CorrectAllSchema(BaseModel):
    biz_id: str = Field(..., description="文章任务的biz_id")
    old_context: str = Field(..., description="旧值")
    language: str = Field('zh',
                          description="如果是 polish ，则记录位置和修改内容 与当前划线时选择的语言对应，不处理翻译对应内容")
    contain: bool = Field(default=True, description="是否包含大纲 true 包含、false 不包含")


class UpdateCorrectAllSchema(CorrectAllSchema):
    new_context: str = Field(..., description="新值")


def can_access_article(user_id: str, biz_id: str) -> (bool, str | None):
    if not user_id:
        return False, None
    customers = settings.CUSTOMER_SERVICE_SEND.split(",")
    customers_article = settings.FREE_ARTICLE.split(",")
    all_flag = customers and user_id in customers
    all_flag = all_flag or customers_article and biz_id in customers_article

    item = (
        LectureTask.select()
        .where(
            (LectureTask.biz_id == biz_id)
            & (LectureTask.delete_flag == 0)
            & (all_flag or (LectureTask.user_id == user_id))
        )
        .first()
    )
    return item and (item.status == "finish" or item.status == "finished"), item and item.article_biz_id


def can_access_article_and_task(user_id: str, biz_id: str) -> (bool, str, LectureTask):
    if not user_id:
        return False, None, None
    customers = settings.CUSTOMER_SERVICE_SEND.split(",")
    customers_article = settings.FREE_ARTICLE.split(",")
    all_flag = customers and user_id in customers
    all_flag = all_flag or customers_article and biz_id in customers_article

    item = (
        LectureTask.select()
        .where(
            (LectureTask.biz_id == biz_id)
            & (LectureTask.delete_flag == 0)
            & (all_flag or (LectureTask.user_id == user_id))
        )
        .first()
    )
    return item and (item.status == "finish" or item.status == "finished"), item and item.article_biz_id, item


async def detail_article_task(async_db: AsyncSession, detail_id, user_id, keys_ids):
    """根据文章内容，反差task"""
    detail = await models.LectureArticleDetailModel.query_model(
        async_db,
        filters=[models.LectureArticleDetailModel.biz_id == detail_id]
    )
    if detail is None:
        return False, None
    task = await models.LectureTaskModel.query_model(
        async_db,
        fields=[models.LectureTaskModel.biz_id],
        filters=[
            or_(models.LectureTaskModel.user_id == user_id,
                models.LectureTaskModel.open_platform_id.in_(keys_ids)),
            models.LectureTaskModel.article_biz_id == detail.article_id]
    )
    if task is None:
        return False, None
    return True, detail


async def async_query_article_details(async_db: AsyncSession, biz_id):
    article = await models.LectureArticleModel.query_model(
        async_db,
        filters=[
            models.LectureArticleModel.biz_id == biz_id,
            models.LectureArticleModel.delete_flag == 0
        ],
        joins=[joinedload(models.LectureArticleModel.details)]
    )
    return article


async def can_access_article_cache(
        async_db: AsyncSession, redis_: Redis,
        user_id: str, task_biz_id: str, keys_ids=None, all_flag=False, flush=False):
    if keys_ids is None:
        keys_ids = []

    async def task_article():
        task = await models.LectureTaskModel.query_model(
            async_db,
            filters=[
                models.LectureTaskModel.biz_id == task_biz_id,
                models.LectureTaskModel.delete_flag == 0,
                or_(all_flag,
                    or_(models.LectureTaskModel.user_id == user_id,
                        models.LectureTaskModel.open_platform_id.in_(keys_ids))
                    )
            ],
            joins=[joinedload(models.LectureTaskModel.article)],
        )
        return await models.LectureTaskModel.serialize_data(task)

    if all_flag:
        flush = True

    data = await myredis.get_cached_or_fetch(
        redis_,
        key=redis_keys_util.key_task_id.format(user_id=user_id, task_id=task_biz_id),
        fetch_func=task_article,
        ttl=redis_keys_util.ttl_task_id,
        flush=flush
    )
    if data:
        return models.LectureTaskModel(**data)
    return data


async def search_article_details(
        async_db: AsyncSession,
        redis_: Redis,
        user_id, task_id,
        article, page_no, page_size,
        start_time_str=None, end_time_str=None,
        flush=False
):
    """文章详情列表"""
    filters = [
        models.LectureArticleDetailModel.user_id == "system",
        models.LectureArticleDetailModel.delete_flag == 0,
        models.LectureArticleDetailModel.article_id == article.biz_id
    ]

    if start_time_str:
        start_time = datetime.strptime(start_time_str, "%H:%M:%S").time()
        filters.append(models.LectureArticleDetailModel.start_time >= start_time)
        flush = True
    if end_time_str:
        end_time = datetime.strptime(end_time_str, "%H:%M:%S").time()
        filters.append(models.LectureArticleDetailModel.end_time <= end_time)
        flush = True

    async def task_article_details():
        model_data = await models.LectureArticleDetailModel.search_model_paginate(
            async_db,
            page_size=page_size,
            current_page=page_no,
            filters=filters,
            joins=[
                joinedload(models.LectureArticleDetailModel.records)
            ],
            sorts=[models.LectureArticleDetailModel.start_time, models.LectureArticleDetailModel.index]
        )
        data = {"content": [], "total": model_data.get("total")}
        for index, article_detail in enumerate(model_data.get('items')):
            detail_data = article_detail.to_vo(article.out_language, article.language)
            record_list = list()
            for item in article_detail.records:
                record_detail_list = item.context_json or []
                record_list.append({
                    "record_id": item.record_id,
                    "record_context": record_detail_list
                })
            detail_data['record'] = record_list
            detail_data['record_count'] = len(record_list)
            data['content'].append(detail_data)
        return data

    key = redis_keys_util.key_article_details_paginate.format(user_id=user_id, task_id=task_id, page_no=page_no,
                                                              page_size=page_size)
    response_data = await myredis.get_cached_or_fetch(
        redis_,
        key=key,
        fetch_func=task_article_details,
        ttl=redis_keys_util.ttl_article_details_paginate,
        flush=flush
    )
    return response_data


async def context_json_create_one(record_context: str, record_detail_list: list, max_index: int = 0):
    biz_id = stringify.get_uid()
    record_detail_list.append({
        "id": biz_id,
        "context": record_context,
        "sort": max_index + 1,
        "create_time": dateUtil.get_current_time_in_beijing().strftime('%Y-%m-%d %H:%M:%S'),
        "update_time": dateUtil.get_current_time_in_beijing().strftime('%Y-%m-%d %H:%M:%S')
    })
    return record_detail_list


async def create_record_and_detail(async_db: AsyncSession, req: UnderlineSchema, user_id):
    """创建划线"""
    old_record = await models.LectureArticleRecordModel.query_model(
        async_db,
        filters=[
            models.LectureArticleRecordModel.record_id == req.record_id,
            models.LectureArticleRecordModel.user_id == user_id,
        ]
    )
    if old_record:
        record_detail_list = old_record.context_json or []
        if len(record_detail_list) > constants.MAX_RECORD:
            return common_error(f"每条记录最多 {constants.MAX_RECORD} 条数据")
        max_index = 0
        for item in record_detail_list:
            if item.get('sort') >= max_index:
                max_index = item.get('sort')
        await context_json_create_one(req.record_context, record_detail_list, max_index)
        old_record.context_json = record_detail_list
        old_record.update_time = dateUtil.get_current_time_in_beijing()
        old_record.delete_flag = 0
        await async_db.commit()
        return old_record.biz_id

    record_detail_list = list()
    await context_json_create_one(req.record_context, record_detail_list)

    detail = await models.LectureArticleDetailModel.query_model(
        async_db,
        filters=[models.LectureArticleDetailModel.biz_id == req.biz_id]
    )
    if detail is None:
        return common_enum_error(ErrorCodeEnum.DETAIL_NOT_EXISTS)
    record_biz_id = stringify.get_uid()

    translated_modified_text_json = detail.translated_modified_text_json or {}
    if req.type == 'polish':
        translated_modified_text_json[req.language] = req.change_text
        detail.translated_modified_text_json = translated_modified_text_json
    else:
        detail.origin_text = req.change_text

    await models.LectureArticleRecordModel.generate_model(
        async_db,
        data={
            "biz_id": record_biz_id,
            "article_detail_id": req.biz_id,
            "type_name": req.type,
            "user_id": user_id,
            "record_id": req.record_id,
            "context_json": record_detail_list
        }
    )
    return record_biz_id


async def update_record_and_detail(async_db: AsyncSession, req: UpdateUnderlineSchema, user_id):
    filters = [
        models.LectureArticleRecordModel.record_id == req.record_id,
        models.LectureArticleRecordModel.user_id == user_id
    ]
    record = await models.LectureArticleRecordModel.query_model(
        async_db,
        filters=filters
    )
    if not record:
        return common_enum_error(ErrorCodeEnum.NO_PERMISSION)
    context_json_list = record.context_json or []

    if not context_json_list:
        return common_enum_error(ErrorCodeEnum.NOT_FOUND)
    update_data = None
    for item in context_json_list:
        if item['id'] == req.context_id:
            item['context'] = req.record_context
            item['update_time'] = dateUtil.get_current_time_in_beijing().strftime('%Y-%m-%d %H:%M:%S')
            update_data = item
            break
    if update_data is None:
        return common_enum_error(ErrorCodeEnum.NOT_FOUND)

    await async_db.execute(
        update(models.LectureArticleRecordModel).filter(*filters).values(
            context_json=context_json_list,
            update_time=dateUtil.get_current_time_in_beijing()
        )
    )
    await async_db.commit()
    return record.article_detail_id


async def delete_record_and_detail(async_db: AsyncSession, record_id, context_id, user_id):
    filters = [
        models.LectureArticleRecordModel.record_id == record_id,
        models.LectureArticleRecordModel.user_id == user_id
    ]
    record = await models.LectureArticleRecordModel.query_model(
        async_db,
        filters=filters
    )
    if not record:
        return common_enum_error(ErrorCodeEnum.NO_PERMISSION)
    context_json_list = record.context_json or []

    if not context_json_list:
        return common_enum_error(ErrorCodeEnum.NOT_FOUND)
    new_list = list()
    remove_data = None
    for item in context_json_list:
        if item['id'] == context_id:
            remove_data = item
        else:
            new_list.append(item)
    if remove_data is None:
        return common_enum_error(ErrorCodeEnum.NOT_FOUND)
    record.context_json = context_json_list
    record.update_time = dateUtil.get_current_time_in_beijing()
    delete_flag = record.delete_flag
    if len(new_list) == 0:
        delete_flag = 1
    await async_db.execute(
        update(models.LectureArticleRecordModel).filter(*filters).values(
            context_json=new_list,
            update_time=dateUtil.get_current_time_in_beijing(),
            delete_flag=delete_flag
        )
    )
    await async_db.commit()
    return record.article_detail_id


async def update_hi_part_detail(async_db: AsyncSession, req: HighLightSchema, user_id, keys_ids):
    """高亮/局部纠错"""
    success, detail = await detail_article_task(async_db, req.biz_id, user_id, keys_ids)
    if not success:
        return common_enum_error(ErrorCodeEnum.NO_PERMISSION)
    translated_modified_text_json = detail.translated_modified_text_json or {}
    update_data = dict()
    if req.type == 'polish':
        translated_modified_text_json[req.language] = req.change_text
        update_data["translated_modified_text_json"] = translated_modified_text_json
    else:
        update_data["origin_text"] = req.change_text
    await async_db.execute(
        update(models.LectureArticleDetailModel).filter(
            models.LectureArticleDetailModel.biz_id == detail.biz_id
        ).values(update_data)
    )
    await async_db.commit()
    return detail.biz_id


async def correct_all_count(
        async_db: AsyncSession, redis_: Redis,
        req: CorrectAllSchema, user_id,
        keys_ids=None, all_flag=False, flush=False
):
    """全局纠错数量统计"""
    if all_flag:
        flush = True
    task = await can_access_article_cache(async_db, redis_, user_id, req.biz_id, keys_ids,
                                          all_flag=all_flag, flush=flush)
    if task is None:
        return common_enum_error(ErrorCodeEnum.NO_PERMISSION)
    article = task.article
    if not article:
        return common_enum_error(ErrorCodeEnum.ARTICLE_NOT_EXISTS)

    detail_data_list = await models.LectureArticleDetailModel.search_model_all(
        async_db,
        filters=[models.LectureArticleDetailModel.article_id == article.biz_id,
                 models.LectureArticleDetailModel.delete_flag == 0],
        sorts=[models.LectureArticleDetailModel.start_time, models.LectureArticleDetailModel.index],
        serialize=True
    )
    count_task, modified_task = list(), list()
    for article_detail in detail_data_list:
        # 原文的内容
        count_task.append(stringify.context_count(article_detail.get('origin_text', ''), req.old_context))

        # ai润色对应原文语种
        translated_modified_text_json = article_detail.get('translated_modified_text_json', {})
        modified_task.append(
            stringify.context_count(translated_modified_text_json.get(req.language, ''), req.old_context))
    context_count_results = await asyncio.gather(*count_task, return_exceptions=True)
    modified_count_results = await asyncio.gather(*modified_task, return_exceptions=True)
    outline_count, summary_count = 0, 0
    if req.contain:
        # 大纲
        translated_outline_json = article.translated_outline_json or {}
        outline_count += translated_outline_json.get(req.language, '').count(req.old_context)
        # ai总结
        translated_total_summary_json = article.translated_total_summary_json or {}
        for key, value in translated_total_summary_json.get(req.language, {}).items():
            str_value = json.dumps(value, indent=4, ensure_ascii=False)
            summary_count += str_value.count(req.old_context)
    response = {
        'origin_count': sum(context_count_results),
        'modified_count': sum(modified_count_results),
        'outline_count': outline_count,
        'summary_count': summary_count,
        'total': sum(context_count_results) + sum(modified_count_results) + outline_count + summary_count
    }
    return response


async def meta_article_data(task, article, translated_outline_json, translated_total_summary_json):
    only_audio = task.ctrl_params.get("video_style") == "audio" or task.ctrl_params.get("image_mode") == "no"
    outline = pandoc_util.convert_latex_in_markdown((translated_outline_json or {}).get(article.out_language) or "")
    enable_speaker_recognition = task.ctrl_params.get("enable_speaker_recognition", False)
    enable_speaker_recognition = stringify.str_to_bool(enable_speaker_recognition)
    article_response = {
        "task_id": task.id,
        "task_biz_id": task.biz_id,
        "biz_id": article.biz_id,
        "name": task.name,
        "task_source": task.task_source,
        "author": task.author,
        "video_url": article.video_url,
        "img_url": task.img_url,
        "create_time": article.create_time,
        "language": article.language,
        "out_language": article.out_language,
        "has_speaker": enable_speaker_recognition,
        "out_language_outline": outline,
        "out_language_total_summary_json": (translated_total_summary_json or {}).get(article.out_language) or {},
        "podcast_transcript_json": article.podcast_transcript_json,
        "audio_url": article.audio_url,
        "has_ai_polishing_finished": True,
        "only_audio": only_audio,
    }
    return article_response


async def correct_all_count_update(
        async_db: AsyncSession, redis_: Redis,
        req: UpdateCorrectAllSchema, user_id,
        keys_ids=None, all_flag=False, flush=False
):
    """全局纠错"""
    not_update_str = ['\n', '-']
    for item in not_update_str:
        if item in req.new_context:
            return common_error("新值不能包含‘\n’，‘-’等字符")
    if all_flag:
        flush = True
    task = await can_access_article_cache(async_db, redis_, user_id, req.biz_id, keys_ids,
                                          all_flag=all_flag, flush=flush)
    if task is None:
        return common_enum_error(ErrorCodeEnum.NO_PERMISSION)
    article = task.article
    if not article:
        return common_enum_error(ErrorCodeEnum.ARTICLE_NOT_EXISTS)

    detail_data_list = await models.LectureArticleDetailModel.search_model_all(
        async_db,
        filters=[models.LectureArticleDetailModel.article_id == article.biz_id,
                 models.LectureArticleDetailModel.delete_flag == 0],
        sorts=[models.LectureArticleDetailModel.start_time, models.LectureArticleDetailModel.index]
    )
    context_update_count, outline_update_count = 0, 0
    for article_detail in detail_data_list:
        update_count = 0
        update_count += article_detail.origin_text.count(req.old_context)
        translated_modified_text_json = article_detail.translated_modified_text_json or {}
        update_count += translated_modified_text_json.get(req.language, '').count(req.old_context)
        if update_count > 0:
            context_update_count += update_count
            translated_modified_text_json = article_detail.translated_modified_text_json or {}
            old_context = translated_modified_text_json.get(req.language, '')
            translated_modified_text_json[req.language] = old_context.replace(req.old_context, req.new_context)

            article_detail.origin_text = article_detail.origin_text.replace(req.old_context, req.new_context)
            article_detail.translated_modified_text_json = translated_modified_text_json
            article_detail.update_time = dateUtil.get_current_time_in_beijing()

    translated_outline_json = article.translated_outline_json or {}
    translated_total_summary_json = article.translated_total_summary_json or {}
    if req.contain:
        # 大纲
        old_context = translated_outline_json.get(req.language, '')
        outline_update_count = old_context.count(req.old_context)
        translated_outline_json[req.language] = old_context.replace(req.old_context, req.new_context)

        # AI总结
        summary_count = 0
        new_translated_total_summary_json_by_language = dict()
        for key, value in translated_total_summary_json.get(req.language, {}).items():
            str_value = json.dumps(value, indent=4, ensure_ascii=False)
            summary_count += str_value.count(req.old_context)
            new_value = str_value.replace(req.old_context, req.new_context)
            new_translated_total_summary_json_by_language[key] = json.loads(new_value)
        translated_total_summary_json[req.language] = new_translated_total_summary_json_by_language
        if outline_update_count > 0 or summary_count > 0:
            await async_db.execute(
                update(models.LectureArticleModel).filter(
                    models.LectureArticleModel.biz_id == article.biz_id
                ).values(
                    translated_outline_json=translated_outline_json,
                    translated_total_summary_json=translated_total_summary_json,
                    update_time=dateUtil.get_current_time_in_beijing()
                )
            )

    await async_db.commit()

    article_response = await meta_article_data(task, article, translated_outline_json, translated_total_summary_json)
    details_response = {"content": [], "total": 0}
    details_response.update(
        {
            "content": [article_detail.to_vo(article.out_language, article.language) for article_detail in
                        detail_data_list],
            "total": len(detail_data_list),
        }
    )
    response = {
        'article_data': article_response,
        'details_data': details_response
    }
    return response


async def get_meta_article(
        async_db: AsyncSession, redis_: Redis,
        user_id, task_id,
        keys_ids=None, all_flag=False, flush=False
):
    if all_flag:
        flush = True
    task = await can_access_article_cache(async_db, redis_, user_id, task_id, keys_ids,
                                          all_flag=all_flag, flush=flush)
    if task is None:
        return common_enum_error(ErrorCodeEnum.NO_PERMISSION)
    article = task.article
    if not article:
        return common_enum_error(ErrorCodeEnum.ARTICLE_NOT_EXISTS)

    data = await meta_article_data(
        task, article, article.translated_outline_json, article.translated_total_summary_json
    )
    return data


async def get_record_by_detail_id(async_db: AsyncSession, detail_id):
    detail = await models.LectureArticleDetailModel.query_model(
        async_db,
        filters=[models.LectureArticleDetailModel.biz_id == detail_id],
        joins=[
            joinedload(models.LectureArticleDetailModel.records.and_(
                models.LectureArticleRecordModel.delete_flag==0
            )),
            joinedload(models.LectureArticleDetailModel.article)
        ]
    )
    if detail:
        records = detail.records
        record_list = list()
        for item in records:
            record_detail_list = item.context_json or []
            record_list.append({
                "record_id": item.record_id,
                "record_context": record_detail_list
            })
        article = detail.article
        article_data = detail.to_vo(article.out_language, article.language)
        article_data['record'] = record_list
        article_data['record_count'] = len(record_list)
        return article_data
    return None
