from pydantic import BaseModel


class PaginateSchema(BaseModel):
    page_no: int = 1
    page_size: int = 5


class UrlSchema(BaseModel):
    url: str
    task_source: str
    ctrl_params: dict = {}
    folder_id: int


class CreateTaskSchema(UrlSchema):
    name: str | None = None
    duration_minutes: int = 0
    url_biz_id: str | None = None
    author: str | None = None
    img_url: str | None = None
    oss_key: str | None = None


class BatchCreateTaskSchema(BaseModel):
    """批量创建任务"""
    task_list: list[CreateTaskSchema]
