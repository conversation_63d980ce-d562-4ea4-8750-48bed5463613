import json

from sqlalchemy import insert, update
from sqlalchemy.orm import joinedload
from sqlalchemy.ext.asyncio import AsyncSession

from src.database import myredis, models, session_maker, enums, constants

from src.logger.logUtil import get_logger
from src.model.User_action_record_manager import UserActionType, UserActionProcess
from src.util import stringify, dateUtil
from src.util.redis_keys_util import key_points_lock, key_source_task_points

logger = get_logger(__name__)

"""
C端用户-原文任务积分
"""


async def points_verify(
        async_db: AsyncSession,
        user_id: str,
        duration_minutes: int,
        points_mode: int = 0
):
    """原文积分验证"""

    @myredis.lock(key_points_lock.format(user_id=user_id))
    async def create_record():
        member = await models.LectureMemberModel.query_model(
            async_db,
            filters=[models.LectureMemberModel.user_id == user_id]
        )

        return member.points < duration_minutes

    return await create_record()


async def points_expend_source_task(
        user_id: str,
        duration_minutes: int,
        points_mode: int = 0,
        **kwargs
):
    """原文任务积分支出"""

    @myredis.lock(key_points_lock.format(user_id=user_id))
    async def create_record():
        async with session_maker.get_db_manager() as async_db:
            member = await models.LectureMemberModel.query_model(
                async_db,
                filters=[models.LectureMemberModel.user_id == user_id]
            )
            if member.points < duration_minutes:
                return False, "积分不足"

            try:
                record_biz_id = stringify.get_uid()
                # 操作1：扣除积分
                await async_db.execute(
                    update(models.LectureMemberModel).filter(
                        models.LectureMemberModel.user_id == user_id
                    ).values(points=models.LectureMemberModel.points - duration_minutes)
                )
                # 操作2：创建任务
                lec: dict = kwargs.get("lec")
                lec["order"] = member.membership_type
                lec["point_id"] = record_biz_id
                lec = stringify.sa_instance_state_remove(lec)
                if lec.get("status") != enums.TaskStatusEnum.FINISHED.value:
                    processing_task = await models.LectureTaskModel.search_model_all(
                        async_db,
                        filters=[
                            models.LectureTaskModel.user_id == user_id,
                            models.LectureTaskModel.status.in_([
                                enums.TaskStatusEnum.CONFIRM.value,
                                enums.TaskStatusEnum.PROCESSING.value,
                            ]),
                            models.LectureTaskModel.delete_flag == 0
                        ],
                        limit_size=2
                    )
                    if processing_task:
                        lec["status"] = enums.TaskStatusEnum.WAITING.value
                await async_db.execute(insert(models.LectureTaskModel).values([lec]))
                # 操作3：创建文章
                article: dict = kwargs.get("article")
                await async_db.execute(insert(models.LectureArticleModel).values([article]))

                # 操作4：创建文章详情
                article_details: dict = kwargs.get("article_details")
                if article_details:
                    await async_db.execute(insert(models.LectureArticleDetailModel).values(article_details))
                # 操作5：创建Ai润色
                new_ai_polish = kwargs.get("new_ai_polish")
                if new_ai_polish:
                    new_ai_polish["order"] = member.membership_type
                    await async_db.execute(insert(models.LectureAITaskModel).values(new_ai_polish))

                # 操作6：记录交易日志
                record_data = {
                    "biz_id": record_biz_id,
                    "user_id": user_id,
                    "user_action_type": UserActionType.UNLOCK_VIDEO.value,
                    "process_status": UserActionProcess.SUB.value,
                    "process_remark": "用户转换视频",
                    "points": duration_minutes,
                    "action_relate_id": lec.get("biz_id")
                }
                await async_db.execute(insert(models.LectureUserActionRecordModel).values([record_data]))

                # 操作7：文件夹任务 +1
                folder_id = kwargs.get("folder_id")
                await async_db.execute(update(models.FolderModel).filter(
                    models.FolderModel.id == folder_id
                ).values(total=models.FolderModel.total + 1))

                await async_db.commit()
                return True, record_biz_id
            except Exception:
                await async_db.rollback()
                logger.exception("创建任务事务失败")
                return False, "创建任务事务失败"

    return await create_record()


async def points_expend_source_retry_task(
        async_db: AsyncSession,
        task_id: str,
        user_id: str
):
    """原文任务重试积分支出"""

    @myredis.lock(key_points_lock.format(user_id=user_id))
    async def create_record():
        task: models.LectureTaskModel = await models.LectureTaskModel.query_model(
            async_db,
            filters=[
                models.LectureTaskModel.status == enums.TaskStatusEnum.FAILED.value,
                models.LectureTaskModel.user_id == user_id,
                models.LectureTaskModel.biz_id == task_id,
                models.LectureTaskModel.delete_flag == 0
            ]
        )
        if task is None:
            return False, "任务不存在"
        if task.retry_count >= constants.MAX_RETRY:
            return False, f"已达到最大重试次数{constants.MAX_RETRY}, {constants.MESSAGE_CONTACT}"
        duration_minutes = task.duration_minutes
        member = await models.LectureMemberModel.query_model(
            async_db,
            filters=[models.LectureMemberModel.user_id == user_id]
        )
        if member.points < duration_minutes:
            return False, "积分不足"
        try:
            record_biz_id = stringify.get_uid()
            # 操作1：扣除积分
            await async_db.execute(
                update(models.LectureMemberModel).filter(
                    models.LectureMemberModel.user_id == user_id
                ).values(points=models.LectureMemberModel.points - duration_minutes)
            )
            # 操作2：更新任务状态
            task.start_time = dateUtil.get_current_time_in_beijing()
            task.process_percent = 0
            task.remark = ""
            task.end_time = None
            task.status = enums.TaskStatusEnum.CONFIRM.value
            task.retry_count += 1
            task.point_id = record_biz_id
            processing_task = await models.LectureTaskModel.search_model_all(
                async_db,
                filters=[
                    models.LectureTaskModel.user_id == user_id,
                    models.LectureTaskModel.status.in_([
                        enums.TaskStatusEnum.CONFIRM.value,
                        enums.TaskStatusEnum.PROCESSING.value,
                    ]),
                    models.LectureTaskModel.delete_flag == 0
                ],
                limit_size=2
            )
            if processing_task:
                task.status = enums.TaskStatusEnum.WAITING.value

            # 操作3：记录交易日志
            record_data = {
                "biz_id": record_biz_id,
                "user_id": user_id,
                "user_action_type": UserActionType.UNLOCK_VIDEO.value,
                "process_status": UserActionProcess.SUB.value,
                "process_remark": "重试：用户转换视频",
                "points": duration_minutes,
                "action_relate_id": task_id
            }
            await async_db.execute(insert(models.LectureUserActionRecordModel).values([record_data]))

            await async_db.commit()
            return True, record_biz_id
        except Exception:
            await async_db.rollback()
            logger.exception("重试任务事务失败")
            return False, "重试任务事务失败"

    return await create_record()


async def points_rectification_source_task(
        task_id: str,
        duration_minutes: int,
        **kwargs
):
    """修正扣除积分"""
    key = key_source_task_points.format(task_id=task_id)
    async with myredis.get_async_redis() as redis_:
        task_points = await redis_.get(key)
        if task_points is None:
            async with session_maker.get_db_manager() as async_db01:
                task_points = await models.LectureTaskModel.query_model(
                    async_db01,
                    filters=[
                        models.LectureTaskModel.biz_id == task_id,
                        models.LectureTaskModel.delete_flag == 0
                    ]
                )
                if task_points:
                    data_points = {
                        "task_id": task_points.biz_id,
                        "duration_minutes": task_points.duration_minutes,
                        "user_id": task_points.user_id,
                        "record_biz_id": task_points.point_id
                    }

        else:
            data_points = json.loads(task_points)
    msg = '无需要修正的扣减积分'
    if data_points is None:
        logger.error(f"{msg}：{task_id}")
        return True, msg
    old_points = int(data_points.get("duration_minutes"))
    if old_points != duration_minutes:
        rectification_points = duration_minutes - old_points
    else:
        logger.error(f"{msg}：{task_id}--old:{old_points}--new:{duration_minutes}")
        return True, msg
    record_biz_id = data_points.get("record_biz_id")
    user_id = data_points.get("user_id")

    @myredis.lock(key_points_lock.format(user_id=user_id))
    async def create_record():
        async with session_maker.get_db_manager() as async_db:
            member = await models.LectureMemberModel.query_model(
                async_db,
                filters=[models.LectureMemberModel.user_id == user_id]
            )
            # 操作1：补扣返回积分
            if member.points < rectification_points:
                member.points = 0
            else:
                member.points -= rectification_points
            try:
                await async_db.execute(
                    update(models.LectureUserActionRecordModel).filter(
                        models.LectureUserActionRecordModel.biz_id == record_biz_id,
                        models.LectureUserActionRecordModel.user_id == user_id
                    ).values(points=duration_minutes, updated_at=dateUtil.get_current_time_in_beijing())
                )
                # 操作2：记录交易日志
                if rectification_points > 0:
                    remark = f"补扣：{rectification_points}"
                    process_status = UserActionProcess.SUB.value
                else:
                    remark = f"返还：{rectification_points}"
                    process_status = UserActionProcess.ADD.value
                record_data = {
                    "biz_id": stringify.get_uid(),
                    "user_id": user_id,
                    "user_action_type": UserActionType.UNLOCK_VIDEO.value,
                    "process_status": process_status,
                    "process_remark": f"补扣积分,原扣除:{old_points},实际扣除{duration_minutes},{remark}",
                    "points": rectification_points,
                    "action_relate_id": record_biz_id
                }
                await async_db.execute(insert(models.LectureUserActionRecordModel).values([record_data]))
                await async_db.commit()
                async with myredis.get_async_redis() as redis_:
                    await redis_.setex(key, 60 * 10, stringify.from_json({
                        "task_id": task_id,
                        "duration_minutes": duration_minutes,
                        "user_id": user_id,
                        "record_biz_id": record_biz_id
                    }))
                logger.info(f"修正积分：{remark}")
                return True, "success"
            except Exception:
                await async_db.rollback()
                logger.exception("修正积分扣减失败")
                return False, "修正积分扣减失败"

    return await create_record()


async def points_revert_source_task(
        task_id: str,
        reason: str,
        user_action_type: int = 0,
        delete_flag: int = 0,
        **kwargs
):
    """原文任务积分还原"""
    async with session_maker.get_db_manager() as async_db01:

        task = await models.LectureTaskModel.query_model(
            async_db01,
            filters=[
                models.LectureTaskModel.biz_id == task_id,
                models.LectureTaskModel.delete_flag == delete_flag
            ],
            joins=[joinedload(models.LectureTaskModel.point_record.and_(
                models.LectureUserActionRecordModel.user_action_type == user_action_type,
                models.LectureUserActionRecordModel.process_status == UserActionProcess.SUB.value,
                models.LectureUserActionRecordModel.rollback_id == 0,
                models.LectureUserActionRecordModel.delete_flag == 0,
            ))]
        )
        if task is None:
            return False, f"没有需要处理的任务{task_id}", None
        record = task.point_record
        if record is None:
            record = await models.LectureUserActionRecordModel.query_model(
                async_db01,
                filters=[
                    models.LectureUserActionRecordModel.user_action_type == user_action_type,
                    models.LectureUserActionRecordModel.action_relate_id == task_id,
                    models.LectureUserActionRecordModel.process_status == UserActionProcess.SUB.value,
                    models.LectureUserActionRecordModel.rollback_id == 0,
                    models.LectureUserActionRecordModel.delete_flag == 0,
                ],
                sorts=[models.LectureUserActionRecordModel.id]
            )
        if record is None:
            return False, f"无积分扣减记录{task_id}", None

        user_id = task.user_id
        if record.points == 0:
            record.rollback_id = -1
            await async_db01.commit()
            return False, f"无需要返还的积分：{task_id}", user_id

    @myredis.lock(key_points_lock.format(user_id=user_id))
    async def create_record():
        async with session_maker.get_db_manager() as async_db:
            member = await models.LectureMemberModel.query_model(
                async_db,
                filters=[models.LectureMemberModel.user_id == user_id]
            )
            if not member:
                return False, f"积分返还，无法指定用户{task_id}", user_id

            try:
                # 操作1：恢复积分
                await async_db.execute(
                    update(models.LectureMemberModel).filter(
                        models.LectureMemberModel.user_id == user_id
                    ).values(points=models.LectureMemberModel.points + record.points)
                )

                # 操作2：记录交易日志
                record_data = {
                    "user_id": user_id,
                    "user_action_type": UserActionType.UNLOCK_VIDEO.value,
                    "process_status": UserActionProcess.ADD.value,
                    "process_remark": f"由于 {reason},执行取消积分扣款,回滚记录:{task_id}",
                    "points": -record.points,
                    "action_relate_id": task_id,
                    "rollback_id": record.id
                }
                await async_db.execute(insert(models.LectureUserActionRecordModel).values([record_data]))

                # 操作3：标记已回滚
                await async_db.execute(
                    update(models.LectureUserActionRecordModel).filter(
                        models.LectureUserActionRecordModel.id == record.id
                    ).values(rollback_id=-1)
                )
                await async_db.commit()
                return True, f"返回积分：{record.points}", user_id
            except Exception:
                await async_db.rollback()
                logger.exception("任务返还积分回滚事务失败")
                return False, "任务返还积分回滚事务失败", user_id

    return await create_record()


"""
开放平台-原文任务积分
"""

async def open_point_increase(
        async_db: AsyncSession,
        user_id: str,
        requester_id: str,
        points: int,
        remark: str = None
):
    """开放平台积分充值"""
    user = await models.PlatformUserModel.query_model(
        async_db,
        filters=[
            models.PlatformUserModel.biz_id == user_id,
            models.PlatformUserModel.delete_flag == 0
        ],
        joins=[joinedload(models.PlatformUserModel.platform_keys.and_(
            models.PlatformKeyModel.delete_flag == 0
        ))]
    )
    if user is None:
        logger.error(f"开放平台积分充值失败，用户不存在：{user_id}, 审批：{requester_id}")
        return

    user.points += points
    platform_keys = user.platform_keys
    if len(platform_keys) > 0:
        platform = platform_keys[0]
        platform.points += points
    await models.LectureUserActionRecordModel.generate_model(
        async_db,
        data={
            "biz_id": stringify.get_uid(),
            "user_id": user_id,
            "action_relate_id": requester_id,
            "user_action_type": UserActionType.RECHARGE.value,
            "process_status": UserActionProcess.ADD.value,
            "process_remark": remark,
            "points": points
        }
    )

async def open_points_verify(
        async_db: AsyncSession,
        user_id: str,
        duration_minutes: int,
        points_mode: int = 0
):
    """原文积分验证"""

    @myredis.lock(key_points_lock.format(user_id=user_id))
    async def create_record():
        member = await models.LectureMemberModel.query_model(
            async_db,
            filters=[models.LectureMemberModel.user_id == user_id]
        )

        return member.points < duration_minutes

    return await create_record()