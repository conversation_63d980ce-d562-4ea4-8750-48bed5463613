from src.model.dto.task import TaskCreate
from src.model.member_manager import (
    LectureMember,
    get_lecture_member_by_user_id,
    sub_member_lock_count,
    sub_member_point,
)
from src.model.task_manager import LectureTask
from src.model.User_action_record_manager import UserActionProcess, UserActionType, add_user_action_record


class MemberService:
    @staticmethod
    def get_member(user_id: str) -> LectureMember:
        return get_lecture_member_by_user_id(user_id)

    @staticmethod
    def validate_resources(user_id: str, duration_minutes: int) -> None:
        """验证会员资源是否充足"""
        if duration_minutes < 0:
            raise ValueError("视频时长不合法")

        member = get_lecture_member_by_user_id(user_id)
        if member.points < duration_minutes:
            raise ValueError("您剩余使用额度不足以转录当前内容，请充值。")

    def process_resources(self, user_id: str, tasks: TaskCreate) -> None:
        """处理会员资源（积分、解锁次数等）"""
        if tasks.status == "finished":
            lock_count_flag = sub_member_lock_count(user_id)
            if not lock_count_flag:
                tasks.duration_minutes = max(tasks.duration_minutes // 2, 1)
            else:
                self._handle_unlock_article(user_id, tasks)
                tasks.duration_minutes = 0
                return

        if tasks.duration_minutes > 0:
            if not sub_member_point(user_id, tasks.duration_minutes):
                raise ValueError("您剩余使用额度不足以转录当前内容，请充值。")

    def _handle_unlock_article(self, user_id: str, tasks: TaskCreate) -> None:
        """处理文章解锁相关逻辑"""
        add_user_action_record(
            user_id, UserActionType.UNLOCK_ARTICLE, UserActionProcess.SUB, "用户解锁文章", 1, tasks.biz_id
        )
        LectureTask.create(**tasks.__dict__)
