import asyncio
import json
import random
import re
import string
from datetime import datetime

from redis.asyncio import Redis
from sqlalchemy import or_, and_
from sqlalchemy.ext.asyncio import AsyncSession

from src.database import models, enums, myredis, constants
from src.logger.logUtil import get_logger
from src.model.lecture_ai import AITYPE
from src.model.task_manager import (
    TASK_SOURCE_CHOICES,
)
from src.services import action_schema, action_task
from src.services.article_service import async_query_article_details
from src.services.lock_points_services import points_verify, points_expend_source_task
from src.services.url_service import get_url_biz
from src.util import MD5Util, dateUtil, stringify, redis_keys_util
from src.util.commonutil import common_enum_error, ErrorCodeEnum
from pydantic import BaseModel, Field, HttpUrl


class SearchTaskFolderSchema(BaseModel):
    url: HttpUrl = Field(..., max_length=500, description="任务资源 URL")
    task_source: str = Field(..., max_length=100, description="任务来源")
    ctrl_params: dict = Field({}, description="控制字段")


# async def create_open_task(async_db: AsyncSession, user, lec, folder, task_param: dict = None):
#     task_ = lec.__dict__
#     if task_param:
#         task_.update(task_param)
#     lec = models.LectureTaskModel(**task_)
#     lec.user_id = user.biz_id
#     lec.biz_id = stringify.get_uid()
#     lec.token = "".join(random.sample(string.ascii_letters + string.digits, 6))
#     try:
#         # 积分验证
#         if await points_verify(async_db, user.biz_id, lec.duration_minutes):
#             return response_task(lec, message="您剩余使用额度不足以转录当前内容，请充值。")
#         lec.ctrl_params = action_task.task_default_ctrl_params(lec.ctrl_params)
#         param_md5 = action_task.task_ctrl_params_unique(lec.ctrl_params, lec.url)
#         lec.md5 = param_md5
#         # 判断是否有任务已经完成
#         task_arr = await models.LectureTaskModel.search_model_all(
#             async_db,
#             filters=[
#                 models.LectureTaskModel.md5 == param_md5,
#                 models.LectureTaskModel.delete_flag == 0
#             ],
#             limit_size=20
#         )
#         finish_task = None
#         for task in task_arr:
#             if task and task.user_id == lec.user_id:
#                 return response_task(lec, message="任务已经创建过")
#             if task.status.lower() == enums.TaskStatusEnum.FINISH.value.lower():
#                 finish_task = task
#
#         new_article_biz_id = stringify.get_uid()
#
#         lec.name = lec.name or lec.url
#         lec.article_biz_id = new_article_biz_id
#         lec.start_time = dateUtil.get_current_time_in_beijing()
#         lec.end_time = dateUtil.get_current_time_in_beijing()
#
#         new_article = {
#             "biz_id": new_article_biz_id,
#             "name": lec.name,
#             "article_source": lec.task_source,
#             "language": "zh",
#             "video_url": lec.url,
#             "img_url": lec.img_url,
#             "author": lec.author,
#         }
#         new_article_details = list()
#         new_ai_polish = None
#         if finish_task:
#             article = await async_query_article_details(async_db, finish_task.article_biz_id)
#             new_article.update({
#                 "name": article.name,
#                 "author": article.author,
#                 "article_source": article.article_source,
#                 "language": article.language,
#                 "video_url": article.video_url,
#                 "img_url": article.img_url,
#                 "out_language": article.out_language,
#                 "translated_outline_json": article.translated_outline_json,
#                 "translated_total_summary_json": article.translated_total_summary_json,
#                 "corpus": article.corpus
#             })
#             for item in article.details:
#                 origin_text = re.sub(r'<font[^>]*>|</font>', '', item.origin_text)
#                 translated_modified_text = item.translated_modified_text_json.get(article.out_language)
#                 translated_modified_text_json = {
#                     article.out_language: re.sub(r'<font[^>]*>|</font>', '', translated_modified_text)
#                 }
#                 new_article_details.append({
#                     "biz_id": stringify.get_uid(),
#                     "article_id": new_article_biz_id,
#                     "index": item.index,
#                     "start_time": item.start_time,
#                     "end_time": item.end_time,
#                     "origin_text": origin_text,
#                     "speaker": item.speaker,
#                     "translated_modified_text_json": translated_modified_text_json,
#                     "oss_pic_path": item.oss_pic_path,
#                     "local_pic_path": item.local_pic_path,
#                     "pic_keywords": item.pic_keywords
#                 })
#             lec.status = enums.TaskStatusEnum.FINISHED.value
#             lec.process_percent = 100
#
#             if lec.ctrl_params.get("enable_ai_polish", True):
#                 new_ai_polish = {
#                     "biz_id": "task_" + stringify.get_uid(),
#                     "user_id": user.biz_id,
#                     "article_biz_id": new_article_biz_id,
#                     "task_type": AITYPE.AI_POLISHING.value,
#                     "name": AITYPE.AI_POLISHING.name,
#                     "status": enums.TaskStatusEnum.FINISHED.value,
#                     "process_percent": 100,
#                     "start_time": dateUtil.get_current_time_in_beijing(),
#                     "end_time": dateUtil.get_current_time_in_beijing(),
#                     "ctrl_params": lec.ctrl_params
#                 }
#         kwargs = {
#             "lec": lec.__dict__,
#             "article": new_article,
#             "article_details": new_article_details,
#             "folder_id": folder.id,
#             "new_ai_polish": new_ai_polish
#         }
#         # 扣除积分创建任务事务
#         success, message = await points_expend_source_task(user.biz_id, lec.duration_minutes, **kwargs)
#         if success:
#             key = redis_keys_util.key_source_task_points.format(task_id=lec.biz_id)
#             async with myredis.get_async_redis() as redis_:
#                 await redis_.setex(key, 60 * 30, stringify.from_json({
#                     "task_id": lec.biz_id,
#                     "duration_minutes": lec.duration_minutes,
#                     "user_id": user.biz_id,
#                     "record_biz_id": message
#                 }))
#             return response_task(lec, "success", success)
#         return response_task(lec, message, success)
#     except Exception:
#         logger.exception("create task error")
#         return response_task(lec, message="create task error")
