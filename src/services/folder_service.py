"""
笔记本
"""
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload, subqueryload, contains_eager
from sqlalchemy import or_
from redis.asyncio import Redis
from src.database import models, constants
from src.util import stringify, dateUtil
from src.util.commonutil import common_enum_error, ErrorCodeEnum
from typing import Literal

from pydantic import BaseModel, Field

from src.services import approval_service


class ApprovalConfigSchema(BaseModel):
    request_type: str
    name: str
    is_active: bool
    steps: list[approval_service.StepData]


class NameSchema(BaseModel):
    name: str = Field(..., max_length=100, description="文件夹名称")


class GenerateFolderSchema(NameSchema):
    parent_id: int = Field(default=None, description="父级文件夹")


def get_folder_uid():
    return f"folder_{stringify.get_uid()}"


async def get_folder(async_db: AsyncSession, filters):
    data = await models.FolderModel.query_model(
        async_db,
        filters=filters
    )
    return data


async def create_folder(async_db: AsyncSession, param: GenerateFolderSchema, user_id):
    """创建笔记本"""
    parent_id = None if param.parent_id == 0 else param.parent_id
    filters = [
        models.FolderModel.name == param.name,
        models.FolderModel.parent_id == parent_id,
        models.FolderModel.delete_flag == 0
    ]
    repeat_folder = await get_folder(async_db, filters)
    if repeat_folder:
        return common_enum_error(ErrorCodeEnum.FOLDER_EXISTS_ERROR)
    parent_folder = None
    if parent_id:
        filters = [
            models.FolderModel.id == parent_id,
            models.FolderModel.user_id == user_id,
            models.FolderModel.delete_flag == 0
        ]
        parent_folder = await get_folder(async_db, filters)
        if parent_folder is None:
            return common_enum_error(ErrorCodeEnum.FOLDER_NOT_EXISTS)

    if parent_folder is None:
        depth = 1
    else:
        depth = parent_folder.depth + 1

    if depth > constants.MAX_FOLDER_DEPTH:
        return common_enum_error(ErrorCodeEnum.FOLDER_NESTED_ERROR)

    folder = await models.FolderModel.generate_model(
        async_db,
        data={
            "biz_id": get_folder_uid(),
            "name": param.name,
            "parent_id": parent_id,
            "depth": depth,
            "user_id": user_id,
        }
    )
    folder = await folder.serialize_data(folder)
    return folder


async def get_recursion(async_db: AsyncSession, user_id, parent_id=None, serialize=True):
    data = await models.FolderModel.search_model_all(
        async_db,
        filters=[
            models.FolderModel.user_id == user_id,
            models.FolderModel.delete_flag == 0,
            models.FolderModel.parent_id == parent_id
        ],
        limit_size=5,
        joins=[
            joinedload(models.FolderModel.children.and_(models.FolderModel.delete_flag == 0)).options(
                joinedload(models.FolderModel.children.and_(models.FolderModel.delete_flag == 0))
            )
        ],
        serialize=serialize
    )
    return data


async def rename_folder(async_db: AsyncSession, folder_id, user_id, name):
    filters = [
        models.FolderModel.id == folder_id,
        models.FolderModel.user_id == user_id,
        models.FolderModel.delete_flag == 0
    ]
    folder: models.FolderModel = await get_folder(async_db, filters)
    if folder is None:
        return common_enum_error(ErrorCodeEnum.FOLDER_NOT_EXISTS)
    if folder.name == constants.DEFAULT_FOLDER and folder.parent_id in [None, 0]:
        return common_enum_error(ErrorCodeEnum.FOLDER_CAN_NOT_RENAME)

    filters = [
        models.FolderModel.id != folder_id,
        models.FolderModel.name == name,
        models.FolderModel.parent_id == folder.parent_id,
        models.FolderModel.delete_flag == 0
    ]
    repeat_folder = await get_folder(async_db, filters)
    if repeat_folder:
        return common_enum_error(ErrorCodeEnum.FOLDER_EXISTS_ERROR)

    folder.name = name
    folder.updated_at = dateUtil.get_current_time_in_beijing()
    await async_db.commit()
    data = await models.FolderModel.serialize_data(folder)
    return data


def update_sub_folder(folders):
    folder_ids = list()
    for folder in folders:
        folder.delete_flag = 1
        folder.total = 0
        folder.updated_at = dateUtil.get_current_time_in_beijing()
        folder_ids.append(folder.id)
        if folder.children:
            sub_ids = update_sub_folder(folder.children)
            folder_ids.extend(sub_ids)
    return folder_ids


async def remove_folder(async_db: AsyncSession, folder_id, user_id):
    filters = [
        models.FolderModel.id == folder_id,
        models.FolderModel.user_id == user_id,
        models.FolderModel.delete_flag == 0
    ]
    folder: models.FolderModel = await get_folder(async_db, filters)
    if folder is None:
        return common_enum_error(ErrorCodeEnum.FOLDER_NOT_EXISTS)
    if folder.name == constants.DEFAULT_FOLDER and folder.parent_id in [None, 0]:
        return common_enum_error(ErrorCodeEnum.FOLDER_CAN_NOT_DELETE)

    folder.delete_flag = 1
    folder.total = 0
    folder.updated_at = dateUtil.get_current_time_in_beijing()
    conditions = [
        models.FolderModel.name == constants.DEFAULT_FOLDER,
        models.FolderModel.user_id == user_id,
        models.FolderModel.parent_id.is_(None),
        models.FolderModel.delete_flag == 0
    ]
    default_folder: models.FolderModel = await get_folder(async_db, conditions)
    if default_folder is None:
        return common_enum_error(ErrorCodeEnum.DEFAULT_FOLDER_NOT_EXISTS)

    folders = await get_recursion(async_db, user_id, parent_id=folder_id, serialize=False)
    folder_ids = update_sub_folder(folders)
    folder_ids.append(folder_id)

    row_count = await models.LectureTaskModel.update_model(
        async_db,
        filters=[
            models.LectureTaskModel.folder_id.in_(folder_ids),
        ],
        data={"folder_id": default_folder.id}
    )
    default_folder.total += row_count
    await async_db.commit()
    data = await models.FolderModel.serialize_data(folder)
    return data


async def folder_page_data(f, remove_col):
    sub = list()

    for item in f.children:
        folder = item.to_dict(remove_col)
        if folder:
            folder["type"] = "folder"
            sub.append(folder)
    for index, item in enumerate(f.tasks):
        if index > 4:
            break
        task = await models.LectureTaskModel.serialize_data(item)
        if task:
            task["type"] = "task"
            sub.append(task)
    return sub


async def search_first_page_mobile(
        redis_: Redis,
        async_db: AsyncSession,
        user_id: str,
        key_ids: list = None,
        page_no=1,
        page_size=4,
        keyword: str = None,
        flush=True
):
    """笔记本首页 包含任务"""
    if key_ids is None:
        key_ids = []
    filters = [
        models.FolderModel.delete_flag == 0,
        models.FolderModel.user_id == user_id,
        models.FolderModel.parent_id.is_(None)
    ]
    task_filters = [
        models.LectureTaskModel.delete_flag == 0,
        or_(models.LectureTaskModel.user_id, models.LectureTaskModel.open_platform_id.in_(key_ids))
    ]
    sub_folder_filters = [models.FolderModel.delete_flag == 0]
    sub_task_filters = [models.LectureTaskModel.delete_flag == 0]
    if not stringify.is_empty(keyword):
        filters.append(models.FolderModel.name.like(f"%{keyword}%"))
        task_filters.append(models.LectureTaskModel.name.like(f"%{keyword}%"))
        sub_folder_filters.append(models.FolderModel.name.like(f"%{keyword}%"))
        sub_task_filters.append(models.LectureTaskModel.name.like(f"%{keyword}%"))
    total_name = "page_total"
    paginate_data = await models.FolderModel.search_model_paginate(
        async_db,
        current_page=page_no,
        page_size=page_size,
        filters=filters,
        joins=[
            joinedload(models.FolderModel.tasks.and_(*sub_task_filters)),
            joinedload(models.FolderModel.children.and_(*sub_folder_filters)).options(
                joinedload(models.FolderModel.tasks.and_(*sub_task_filters)),
                joinedload(models.FolderModel.children.and_(*sub_folder_filters)).options(
                    joinedload(models.FolderModel.tasks.and_(*sub_task_filters))
                )
            )
        ],
        # serialize=True,
        total_name=total_name
    )
    response_list = list()
    remove_col = ["children", "tasks"]
    for f in paginate_data.get("items"):
        content = await folder_page_data(f, remove_col)

        parent_folder = f.to_dict(remove_col)

        response_list.append({
            "folder": parent_folder,
            "content": content
        })
    return {
        "page_no": page_no,
        "page_size": page_size,
        "result": response_list,
        "total": paginate_data.get(total_name)
    }


async def init_folder(async_db: AsyncSession, user_id, open_key_id):
    """初始化笔记本"""
    data = await models.FolderModel.query_model(
        async_db,
        filters=[
            models.FolderModel.user_id == user_id,
            models.FolderModel.delete_flag == 0
        ]
    )
    if data is None:
        default_folder = await models.FolderModel.generate_model(
            async_db,
            data={
                "biz_id": get_folder_uid(),
                "name": constants.DEFAULT_FOLDER,
                "parent_id": None,
                "user_id": user_id,
                "depth": 1
            }
        )
        row_count = await models.LectureTaskModel.update_model(
            async_db,
            filters=[
                or_(models.LectureTaskModel.user_id == open_key_id,
                    models.LectureTaskModel.open_platform_id == open_key_id),
                models.LectureTaskModel.delete_flag == 0,
                models.LectureTaskModel.folder_id.is_(None)
            ],
            data={
                "folder_id": default_folder.id
            }
        )
        default_folder.total = row_count
        await async_db.commit()
