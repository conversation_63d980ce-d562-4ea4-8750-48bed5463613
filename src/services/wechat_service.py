import asyncio
import hashlib
from enum import Enum
from urllib.parse import urljoin
import time
import aiohttp
from redis.asyncio import Redis
import requests
from src.database import myredis
from config import settings
from src.logger.logUtil import get_logger
from src.util.cacheUtil import open_api_cache
from src.util.redis_keys_util import (
    key_pc_wechat_access_token_by_code,
    key_app_wechat_access_token_by_code,
    key_pc_wechat_access_token,
    key_app_wechat_access_token,
    key_js_ticket
)
from src.util import stringify

logger = get_logger(__name__)
API_BASE_URL = 'https://api.weixin.qq.com/'
OPEN_BASE_URL = 'https://open.weixin.qq.com/connect/'


class CategoryEnum(Enum):
    LOGOFF = "log_off"  # 注销
    LOGON = "log_on"  # 登录
    BINDING = "binding"  # 绑定
    UNBIND = "unbind"  # 解绑


class WxSourceEnum(Enum):
    PC = "pc"
    ANDROID = "android"
    IOS = "ios"


def get_key(source, code=None):
    redis_key = key_pc_wechat_access_token if code is None else key_pc_wechat_access_token_by_code.format(code=code)
    app_id = settings.WECHAT_APP_ID
    secret = settings.WECHAT_APP_SECRET
    if source.lower() in ['android', 'ios']:
        redis_key = key_app_wechat_access_token if code is None else key_app_wechat_access_token_by_code.format(
            code=code)
        app_id = settings.WECHAT_MOBILE_APP_ID
        secret = settings.WECHAT_MOBILE_APP_SECRET
    return redis_key, app_id, secret


def direct_access_token_by_code(source, code):
    """直接根据code 获取access_token 会带有 unionid"""
    redis_key, app_id, secret = get_key(source, code)
    response = open_api_cache.get(redis_key)
    if response:
        data = response
    else:
        url = urljoin(
            API_BASE_URL,
            f'/sns/oauth2/access_token?appid={app_id}&secret={secret}&code={code}&grant_type=authorization_code'
        )
        response = requests.get(url)
        data = response.json()
        if int(data.get('errcode', 0)) != 0:
            logger.error(f'根据code直取token失败：{data}')
            return None

        expires_in = data.get('expires_in', 7200)
        open_api_cache.set(redis_key, data)
        open_api_cache.backend.writer_client.expire(redis_key, expires_in - 200)
    # {
    #   "access_token":"ACCESS_TOKEN",
    #   "expires_in":7200,
    #   "refresh_token":"REFRESH_TOKEN",
    #   "openid":"OPENID",
    #   "scope":"SCOPE",
    #   "is_snapshotuser": 1,
    #   "unionid": "UNIONID"
    # }
    return data


def get_wechat_access_token(source):
    """后台直取 access_token 没有 unionid"""
    try:
        redis_key, app_id, secret = get_key(source)
        response = open_api_cache.get(redis_key)
        if response:
            access_token = response.get('access_token')
        else:
            url = urljoin(
                API_BASE_URL,
                f'cgi-bin/token?grant_type=client_credential&appid={app_id}&secret={secret}'
            )
            response = requests.get(url)
            data = response.json()
            if int(data.get('errcode', 0)) != 0:
                raise Exception(f"errcode: {data.get('errcode')}  errmsg: {data.get('errmsg')}")
            access_token = data.get('access_token')
            expires_in = data.get('expires_in', 7200)
            open_api_cache.set(redis_key, data)
            open_api_cache.backend.writer_client.expire(redis_key, expires_in - 200)
        return access_token
    except Exception:
        logger.exception(f'error get_wechat_access_token:{source}')
        return None


async def wechat_cgi_token(redis_: Redis, source, return_id:bool=False):
    """后台直取 access_token 没有 unionid"""
    redis_key, app_id, secret = get_key(source)

    url = urljoin(
        API_BASE_URL,
        f'cgi-bin/token?grant_type=client_credential&appid={app_id}&secret={secret}'
    )
    async def get_cgi_bin_token():
        timeout = aiohttp.ClientTimeout(total=10)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            last_exception = None
            for _ in range(3):
                try:
                    async with session.get(url) as response:
                        json_data = await response.json()
                        error_code = int(json_data.get('errcode', 0))
                        if error_code != 0:
                            if error_code in [40243, 50004, 50007]:
                                # 紧急问题发送邮件
                                # 40243 AppSecret已被冻结，请解冻后再次调用。
                                # 50004 禁止使用 token 接口
                                # 50007 账号已冻结
                                pass
                            raise Exception(f"errcode: {json_data.get('errcode')}  errmsg: {json_data.get('errmsg')}")
                        else:
                            return json_data.get("access_token")
                except asyncio.TimeoutError as e:
                    last_exception = e
                    continue
                except Exception as ex:
                    logger.error(ex)
                    return None
            logger.error(last_exception)
            return None

    data = await myredis.get_cached_or_fetch(
        redis_,
        redis_key,
        get_cgi_bin_token,
        ttl=7100
    )
    if return_id:
        return data, app_id
    return data

async def js_sdk_ticket(redis_:Redis, source, access_token):
    url = urljoin(
        API_BASE_URL,
        f"cgi-bin/ticket/getticket?access_token={access_token}&type=jsapi"
    )
    async def get_cgi_bin_jsapi_ticket():
        timeout = aiohttp.ClientTimeout(total=10)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            last_exception = None
            for _ in range(3):
                try:
                    async with session.get(url) as response:
                        json_data = await response.json()
                        error_code = int(json_data.get('errcode', 0))
                        if error_code != 0:
                            raise Exception(f"errcode: {json_data.get('errcode')}  errmsg: {json_data.get('errmsg')}")
                        else:
                            return json_data.get("ticket")
                except asyncio.TimeoutError as e:
                    last_exception = e
                    continue
                except Exception as ex:
                    logger.error(ex)
                    return None
            logger.error(last_exception)
            return None

    data = await myredis.get_cached_or_fetch(
        redis_,
        key_js_ticket.format(source=source),
        get_cgi_bin_jsapi_ticket,
        ttl=7100
    )
    return data


async def js_sdk_signa(redis_:Redis, source, url):
    """前端需要 JS SDK 签名"""
    access_token, app_id = await wechat_cgi_token(redis_, source, return_id=True)
    if access_token is None:
        return None
    ticket = await js_sdk_ticket(redis_, source, access_token)
    if ticket is None:
        return None
    noncestr = stringify.random_letters(16)
    timestamp = int(time.time())
    txt = f"jsapi_ticket={ticket}&noncestr={noncestr}&timestamp={timestamp}&url={url}"
    return {
        "noncestr": noncestr,
        "signature":hashlib.sha1(txt.encode("utf-8")).hexdigest(),
        "timestamp": timestamp,
        "app_id": app_id
    }


def delete_access_token_cache(source, code=None):
    redis_key, app_id, secret = get_key(source, code)
    try:
        open_api_cache.delete(redis_key)
    except Exception:
        logger.exception(f'error delete_access_token_cache:source={source} code={code}')


def get_unionid(access_token, openid):
    """openId 和 access_token 获取 unionid"""

    # response = {
    #     'subscribe': 1,
    #     'openid': 'o9ei_7Y8ezRuWqSvBjku5MvovXCw',
    #     'nickname': '',
    #     'sex': 0,
    #     'language': 'zh_CN',
    #     'city': '',
    #     'province': '',
    #     'country': '',
    #     'headimgurl': '',
    #     'subscribe_time': 1748593224,
    #     'unionid': 'ozW_k6tZJ3US1PLnhWsNaOHWAGSM',
    #     'remark': '',
    #     'groupid': 0,
    #     'tagid_list': [],
    #     'subscribe_scene': 'ADD_SCENE_QR_CODE',
    #     'qr_scene': 0,
    #     'qr_scene_str': 'f2ab22785aedb0e8ef769184aa44166b'
    # }
    try:
        url = urljoin(
            API_BASE_URL,
            f'/cgi-bin/user/info?access_token={access_token}&openid={openid}&lang=zh_CN'
        )
        response = requests.get(url)
        data = response.json()
        if int(data.get('errcode', 0)) != 0:
            if int(data.get('errcode', 0)) == 40001:
                return False, int(data.get('errcode', 0))
            raise Exception(f"errcode: {data.get('errcode')}  errmsg: {data.get('errmsg')}")
        return True, data.get('unionid')
    except Exception:
        logger.exception('获取unionid失败：{}')
        return False, None


def get_authorize_url(source, redirect_uri, state=None, scope="snsapi_userinfo"):
    """调起认证二维码地址"""
    redis_key, app_id, secret = get_key(source)
    url_list = [
        OPEN_BASE_URL,
        'oauth2/authorize?appid=',
        app_id,
        '&redirect_uri=',
        redirect_uri,
        '&response_type=code&scope=',
        scope
    ]
    if state:
        url_list.extend(['&state=', state])
    url_list.append('#wechat_redirect')
    return ''.join(url_list)
