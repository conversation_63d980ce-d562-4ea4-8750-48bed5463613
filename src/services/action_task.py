import json

from pydantic import BaseModel

from src.database import enums
from src.util import MD5Util


class UrlSchema(BaseModel):
    url: str
    task_source: str
    ctrl_params: dict = {}
    folder_id: int


class CreateTaskSchema(UrlSchema):
    name: str | None = None
    duration_minutes: int = 0
    url_biz_id: str | None = None
    author: str | None = None
    img_url: str | None = None
    oss_key: str | None = None


class BatchCreateTaskSchema(BaseModel):
    """批量创建任务"""
    task_list: list[CreateTaskSchema]


def task_default_ctrl_params(ctrl_params: dict):
    ctrl_params.setdefault("input_language", "auto")
    ctrl_params.setdefault("output_language", "zh")
    ctrl_params.setdefault("image_mode", "more")
    ctrl_params.setdefault("enable_speaker_recognition", True)
    ctrl_params.setdefault("enable_ai_polish", True)
    ctrl_params.setdefault("enable_total_summary", True)
    ctrl_params.setdefault("total_summary_template", "default")
    ctrl_params.setdefault("enable_outline", True)
    ctrl_params.setdefault("enable_podcast_summary", True)
    ctrl_params.setdefault("custom_words", [])
    return ctrl_params


def task_ctrl_params_unique(data: dict, url):
    """任务参数以及url唯一的可以复用数据"""
    unique_keys = [
        "input_language",
        "output_language",
        "image_mode",
        "enable_speaker_recognition",
        "enable_ai_polish",
        "enable_total_summary",
        "total_summary_template",
        "enable_outline",
        "enable_podcast_summary",
        "custom_words"
    ]
    ctrl_params = dict()
    for key, value in data.items():
        if key in unique_keys:
            ctrl_params[key] = value
    target_data = dict(sorted(ctrl_params.items()))
    md5_params = f"{json.dumps(target_data)}--{url}"
    param_md5 = MD5Util.get_md5_hash(md5_params, enums.Md5Key.TASK.value)
    return param_md5
