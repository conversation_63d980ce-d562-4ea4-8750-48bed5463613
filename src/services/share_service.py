"""
分享
"""

from pydantic import BaseModel, Field
from redis.asyncio import Redis
from sqlalchemy import update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload, load_only

from src.database import models, myredis, enums
from src.services import action_schema
from src.util import redis_keys_util, stringify, dateUtil, MD5Util
from src.util.commonutil import ErrorCodeEnum, common_enum_error


class CreateShareTokenSchema(BaseModel):
    """默认创建分享链接"""
    task_id: str = Field(..., description="任务 biz_id")


class CreateShareDefaultSchema(CreateShareTokenSchema):
    """默认创建分享链接"""
    share_token: str = Field(..., description="share_token")


class CreateShareSchema(CreateShareDefaultSchema):
    """创建分享链接"""
    share_type: str = Field(default=enums.ShareTypeEnum.PUBLIC.value, description="分享类型 默认开放")
    encryption_key: str = Field(..., description="加密密码")
    access_limit: int = Field(default=-1, description="限制访问次数, 默认-1 无限制")
    expiry_date: str = Field(default=None, description="过期时间, 默认 null 永久")


class SearchShareSchema(action_schema.PaginateSchema):
    """我分享链接"""
    name: str = Field(default=None, description="任务名称")


async def create_token(redis_: Redis, async_db: AsyncSession, task_id, user_id):
    async def get_share_by_id():
        shared = await models.ShareModel.query_model(
            async_db,
            filters=[
                models.ShareModel.task_id == task_id,
                models.ShareModel.delete_flag == 0,
                models.ShareModel.user_id == user_id
            ],
            serialize=True
        )
        return shared

    data = await myredis.get_cached_or_fetch(
        redis_,
        redis_keys_util.key_share_id.format(task_id=task_id),
        get_share_by_id,
        model=models.ShareModel
    )
    return data


async def create_share(redis_: Redis, async_db: AsyncSession, req: CreateShareDefaultSchema, user_id):
    """创建分享链接"""
    shared = await create_token(redis_, async_db, req.task_id, user_id)
    if shared:
        if shared.share_token != req.share_token:
            return common_enum_error(ErrorCodeEnum.SHARE_TOKEN_ERROR)
        return await models.ShareModel.serialize_data(shared)
    task: models.LectureTaskModel = await models.LectureTaskModel.query_model(
        async_db,
        fields=[models.LectureTaskModel.name],
        filters=[
            models.LectureTaskModel.delete_flag == 0,
            models.LectureTaskModel.user_id == user_id,
            models.LectureTaskModel.biz_id == req.task_id
        ]
    )
    if not task:
        return common_enum_error(ErrorCodeEnum.ARTICLE_NOT_EXISTS)
    share_data = await models.ShareModel.generate_model(
        async_db,
        data={
            "biz_id": stringify.get_uid(),
            "user_id": user_id,
            "name": task.name,
            "share_token": stringify.random_letters(18),
            **req.__dict__
        }
    )
    key = redis_keys_util.key_share_all.format(user_id=user_id)
    await myredis.clear_redis_cache(redis_, key)
    return await models.ShareModel.serialize_data(share_data)


async def remove_or_active_share(redis_: Redis, async_db: AsyncSession, share_id, user_id, remove=False):
    """删除/关闭启用分享"""
    share: models.ShareModel = await models.ShareModel.query_model(
        async_db,
        fields=[models.ShareModel.name, models.ShareModel.is_active, models.ShareModel.delete_flag,
                models.ShareModel.update_time, models.ShareModel.share_token],
        filters=[
            models.ShareModel.delete_flag == 0,
            models.ShareModel.user_id == user_id,
            models.ShareModel.biz_id == share_id
        ]
    )
    if not share:
        return common_enum_error(ErrorCodeEnum.SHARE_NOT_EXISTS)
    if remove:
        share.delete_flag = 1
        share.update_time = dateUtil.get_current_time_in_beijing()
    else:
        share.is_active = not share.is_active
        share.update_time = dateUtil.get_current_time_in_beijing()
    await async_db.commit()
    key = redis_keys_util.key_share_all.format(user_id=user_id)
    await myredis.clear_redis_cache(redis_, key)
    await redis_.delete(key)
    token_key = redis_keys_util.key_share_token.format(token=share.share_token)
    await redis_.delete(token_key)
    return await models.ShareModel.serialize_data(share)


async def search_share(redis_: Redis, async_db: AsyncSession, req: SearchShareSchema, user_id):
    """我的分享列表"""
    filters = [models.ShareModel.delete_flag == 0, models.ShareModel.user_id == user_id]
    conditions = "default"
    if not stringify.is_empty(req.name):
        filters.append(models.ShareModel.name.like(f"%{req.name}%"))
        conditions = MD5Util.get_md5_hash(req.name, enums.Md5Key.CONDITIONS.value)

    async def search_my_share():
        share_data = await models.ShareModel.search_model_paginate(
            async_db,
            filters=filters,
            current_page=req.page_no,
            page_size=req.page_size,
            sorts=[models.ShareModel.id.desc()],
            joins=[joinedload(models.ShareModel.task).options(
                load_only(*[
                    models.LectureTaskModel.task_source,
                    models.LectureTaskModel.img_url,
                    models.LectureTaskModel.start_time,
                    models.LectureTaskModel.duration_minutes
                ])
            )],
            serialize=True
        )
        return share_data

    data = await myredis.get_cached_or_fetch(
        redis_,
        redis_keys_util.key_share_paginate.format(user_id=user_id, page_no=req.page_no, page_size=req.page_size,
                                                  conditions=conditions),
        search_my_share,
        ttl=redis_keys_util.ttl_share_paginate
    )
    return data


async def get_share_by_token(redis_: Redis, async_db: AsyncSession, share_token):
    """获取分享, 暂时只有永久分享"""
    filters = [
        models.ShareModel.delete_flag == 0,
        models.ShareModel.share_token == share_token,
        models.ShareModel.is_active.is_(True)
    ]

    async def share_data_token():
        share_data = await models.ShareModel.query_model(
            async_db,
            filters=filters,
            serialize=True
        )
        return share_data

    data = await myredis.get_cached_or_fetch(
        redis_,
        redis_keys_util.key_share_token.format(token=share_token),
        share_data_token,
        ttl=redis_keys_util.ttl_share_paginate
    )
    return data


async def access_record(async_db: AsyncSession, share_id, ip_address, user_agent, user=None):
    """分享访问记录"""
    try:
        access_user_id = None
        filters = [
            models.ShareAccessModel.share_id == share_id
        ]
        if user:
            access_user_id = user.biz_id
            filters.append(models.ShareAccessModel.user_id == access_user_id)
        else:
            filters.append(models.ShareAccessModel.ip_address == ip_address)
            filters.append(models.ShareAccessModel.user_agent == user_agent)
        data = await models.ShareAccessModel.query_model(
            async_db,
            filters=filters
        )
        if data is None:
            data = models.ShareAccessModel(
                biz_id=stringify.get_uid(),
                ip_address=ip_address,
                user_agent=user_agent,
                user_id=access_user_id,
                share_id=share_id
            )
            async_db.add(data)
        await async_db.execute(
            update(models.ShareModel).filter(models.ShareModel.biz_id == share_id).values(
                access_count=models.ShareModel.access_count + 1
            )
        )
        await async_db.commit()
        return data
    except Exception as ex:
        return None
