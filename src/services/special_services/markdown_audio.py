import json

from sqlalchemy import update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload

from src.database import constants, myredis, enums, models, session_maker
from src.database.enums import TaskStatusEnum
from src.logger.logUtil import get_logger
from src.util.MD5Util import get_md5_hash
from src.util.dateUtil import get_current_time_in_beijing
from src.util.redis_keys_util import key_special_lock_task
from src.util.stringify import is_empty, get_uid
from config import settings

logger = get_logger(__name__)

md5key = "markdown"


def mark_uid():
    return f"markdown_{get_uid()}"


# async def response_data(task: SpecialMarkdownTask, update_data: dict = None):
#     data = {
#         "task_id": task.biz_id,
#         "status": task.status,
#         "audio_url": task.audio_url,
#         "caption_list": task.caption_list,
#         "duration": task.duration,
#         "size": task.size,
#         "created_at": task.created_at,
#         "updated_at": task.updated_at,
#         "ctrl_params": task.ctrl_params,
#     }
#     if update_data:
#         data.update(update_data)
#     return data


async def crete_markdown_task(async_db: AsyncSession, param: dict, platform):
    # - PropagateID：文件唯一ID
    # - ReservedCode2:存储ID
    # - Propagate： 传播者ID   智谱提供
    content = param.get("content")
    callback_url = param.get("callback_url")
    podcast_params = param.get("ctrl_params")
    propagate_id = param.get("PropagateID")
    reserved_code2 = param.get("ReservedCode2")
    propagate = param.get("Propagate")
    audio_name = param.get("audio_name")
    category = param.get("category", constants.PODCAST_CATEGORY_DEFAULT)

    if category not in constants.PODCAST_CATEGORY:
        category = constants.PODCAST_CATEGORY_DEFAULT

    if settings.APP_DEBUG:
        male_voice = "zh_male_linke"
        female_voice = "zh_female_donghan"
    else:
        female_voice = podcast_params.get("femaleTimbre", "zh_f_qingqing")
        if female_voice not in constants.voice_female:
            female_voice = constants.voice_female[0]
        male_voice = podcast_params.get("maleTimbre", "zh_male_linke")
        if male_voice not in constants.voice_male:
            male_voice = constants.voice_male[0]

    ctrl_params = {
        "maleName": podcast_params.get("maleName", ""),
        "femaleName": podcast_params.get("femaleName", ""),
        "maleTimbre": male_voice,
        "femaleTimbre": female_voice,
        "language": "zh",
        "PropagateID": propagate_id,
        "ReservedCode2": reserved_code2,
        "ContentPropagator": propagate,
        "category": category
    }

    if is_empty(content):
        return False, "内容不能为空"

    # 验证文件地址或内容是否存在
    content_md5 = get_md5_hash(content, md5key)
    mark_content = await models.SpecialContentModel.query_model(
        async_db,
        filters=[
            models.SpecialContentModel.delete_flag == 0,
            models.SpecialContentModel.md5 == content_md5
        ]
    )
    add_models = list()

    if mark_content is None:
        mark_content = models.SpecialContentModel(
            biz_id=mark_uid(),
            content=content,
            md5=content_md5
        )
        add_models.append(mark_content)
    target_data = dict(sorted(ctrl_params.items()))
    md5_params = f"{json.dumps(target_data)}--{mark_content.biz_id}"

    param_md5 = get_md5_hash(md5_params, md5key)

    task = await models.SpecialTaskModel.query_model(
        async_db,
        filters=[
            models.SpecialTaskModel.md5 == param_md5,
            models.SpecialTaskModel.status == TaskStatusEnum.FINISH.value
        ]
    )
    user_platform_id = platform.get("biz_id")

    ctrl_params.update({
        "audio_name": audio_name
    })

    current_task_biz_id = get_uid()
    task_param = {
        "biz_id": current_task_biz_id,
        "ctrl_params": ctrl_params,
        "callback_url": callback_url,
        "md5": param_md5,
        "content_id": mark_content.biz_id,
        "user_platform_id": user_platform_id,
        "category": category
    }
    if task and str(task.status).lower() == TaskStatusEnum.FINISH.value.lower():
        # 如果任务存在 则复用数据
        task_param.update({
            "status": TaskStatusEnum.FINISH.value,
            "audio_url": task.audio_url,
            "duration": task.duration,
            "caption_list": task.caption_list,
            "remark": task.remark
        })
    add_models.append(models.SpecialTaskModel(**task_param))

    await models.SpecialTaskModel.generate_list_model(async_db, add_models)
    # if task_param.get("status"):
    #     # 回调
    #     param = await response_data(task)
    #     sync_special_callback.apply_async(args=(task.biz_id, callback_url, param))

    return True, current_task_biz_id


async def get_last_task(task_id: str = None):
    filters = [models.SpecialTaskModel.delete_flag == 0]
    if task_id:
        filters.append(models.SpecialTaskModel.biz_id == task_id)
    else:
        filters.append(models.SpecialTaskModel.status == enums.TaskStatusEnum.CONFIRM.value)

    # redis 锁
    @myredis.lock(key_special_lock_task.format(task_id=task_id))
    async def lock_task():
        async with session_maker.get_db_manager() as async_db:

            task = await models.SpecialTaskModel.query_model(
                async_db,
                filters=filters,
                joins=[joinedload(models.SpecialTaskModel.source_content)],
                sorts=[models.SpecialTaskModel.created_at.asc()]
            )
            if task:
                task.status = enums.TaskStatusEnum.PROCESSING.value
                task.updated_at = get_current_time_in_beijing()
                data = task.to_vo()
                source_content = task.source_content
                data.update({
                    "content": source_content.content
                })
                await async_db.commit()
                return data
            else:
                return None

    return await lock_task()


async def task_finish(async_db: AsyncSession, task_id, status, param: dict):
    conditions = [
        models.SpecialTaskModel.biz_id == task_id,
        models.SpecialTaskModel.delete_flag == 0
    ]
    task = await models.SpecialTaskModel.query_model(async_db, filters=conditions)
    if not task:
        return False, f"task: {task_id} not found"

    if task.status in [enums.TaskStatusEnum.INVALID.value,
                       enums.TaskStatusEnum.FAILED.value,
                       enums.TaskStatusEnum.FINISH.value]:
        return False, f"task: {task_id} updated"

    update_keys = ["audio_url", "duration", "caption_list", "size", "remark"]

    update_data = dict()
    for key, value in param.items():
        if key in update_keys and value:
            update_data[key] = value

    if update_data:
        update_data.update({"status": status})
        update_data.update({"updated_at": get_current_time_in_beijing()})
        await async_db.execute(update(models.SpecialTaskModel).filter(*conditions).values(update_data))
        await async_db.commit()
        return True, task_id

    return False, f"task: {task_id} no data available for updating"


async def get_task_status(async_db: AsyncSession, task_id, platform):
    user_platform_id = platform.get("biz_id")
    task = await models.SpecialTaskModel.query_model(
        async_db,
        fields=[
            models.SpecialTaskModel.biz_id,
            models.SpecialTaskModel.status,
            models.SpecialTaskModel.audio_url,
            models.SpecialTaskModel.caption_list,
            models.SpecialTaskModel.created_at,
            models.SpecialTaskModel.updated_at,
            models.SpecialTaskModel.duration,
            models.SpecialTaskModel.size,
            models.SpecialTaskModel.ctrl_params,
            models.SpecialTaskModel.user_platform_id,
            models.SpecialTaskModel.category
        ],
        filters=[
            models.SpecialTaskModel.biz_id == task_id,
            models.SpecialTaskModel.user_platform_id==user_platform_id,
            models.SpecialTaskModel.delete_flag == 0
        ]
    )
    if task is None:
        return False, "任务不存在或已删除"
    data = await task.serialize_data(task)
    ctrl_params = data.get("ctrl_params", {})
    data["audio_name"] = ctrl_params.get("audio_name")
    data["task_id"] = data.get("biz_id")
    if "biz_id" in data:
        del data["biz_id"]
    for item in ["ContentPropagator", "audio_name"]:
        if item in ctrl_params:
            del ctrl_params[item]
    return True, data
