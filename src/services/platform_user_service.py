from datetime import datetime, time as dti

from peewee import <PERSON><PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload, load_only

from src.database import enums, models
from src.model.User_action_record_manager import LectureUserActionRecord, UserActionProcess
from src.model.oplatform_manager import OpenPlatform
from src.model.platform_user_manager import (
    PlatformUser
)
from src.model.task_manager import LectureTask
from src.util.dateUtil import get_beijing_time


async def update_user(update_data: dict, conditions: list, operate_user_id: str):
    update_data.update({
        "update_by": operate_user_id,
        "update_time": get_beijing_time()
    })
    PlatformUser.update(**update_data).where(*conditions).execute()
    user = PlatformUser.select().where(*conditions).first()
    return user


async def get_platform_user_info(conditions: list) -> PlatformUser:
    user = PlatformUser.select().where(*conditions).first()
    return user


async def platform_level_info(level: int):
    data = {
        "level": level,
        "level_mark": "",
        "level_title": ""
    }
    for item in enums.PlatformLevelEnum:
        if item.code == level:
            data = {
                "level": level,
                "level_mark": item.mark,
                "level_title": item.message
            }
            return data
    return data


async def platform_user_transaction_records(user_id, page_size, page_no):
    app_ids = OpenPlatform.select(OpenPlatform.biz_id).where(OpenPlatform.user_id == user_id)

    query = LectureUserActionRecord.select(
        LectureUserActionRecord.action_relate_id.alias('task_id'),
        LectureUserActionRecord.user_action_type,
        LectureUserActionRecord.process_status,
        LectureUserActionRecord.process_remark,
        LectureUserActionRecord.points,
        LectureUserActionRecord.created_at,
        LectureTask.name,
        LectureTask.status,
        LectureTask.remark
    ).join(
        LectureTask, join_type=JOIN.LEFT_OUTER, on=(LectureTask.biz_id == LectureUserActionRecord.action_relate_id)
    ).where(
        ((LectureUserActionRecord.user_id.in_(app_ids)) | (LectureUserActionRecord.user_id == user_id))
        & (LectureUserActionRecord.delete_flag == 0)
        # & (LectureTask.status == 'finish')
    ).switch(LectureUserActionRecord)
    total = query.count()
    secret_list = query.order_by(LectureUserActionRecord.id.desc()).paginate(page_no, page_size)
    data = {"content": [], "total": 0}
    for secret in secret_list:
        secret_data = secret.__data__
        task_exist = hasattr(secret, 'lecturetask')
        secret_data["lecturetask"] = secret.lecturetask.__data__ if task_exist else None
        data['content'].append(secret_data)
    data['total'] = total
    return data, app_ids


async def platform_user_summary(user_id, app_ids):
    today = get_beijing_time()
    start = datetime.combine(today, dti.min)  # 00:00:00
    end = datetime.combine(today, dti.max)  # 23:59:59.999999
    first_day = datetime(today.year, today.month, 1)

    # app_ids = OpenPlatform.select(OpenPlatform.biz_id).where(OpenPlatform.user_id == user_id)
    # app_ids
    query_current_day = LectureUserActionRecord.select().where(
        ((LectureUserActionRecord.user_id.in_(app_ids)) | (LectureUserActionRecord.user_id == user_id))
        & (LectureUserActionRecord.delete_flag == 0)
        & (LectureUserActionRecord.created_at.between(first_day, end))
    ).switch(LectureUserActionRecord)
    day_add_points, day_sub_points, month_add_points, month_sub_points = 0, 0, 0, 0
    for secret in query_current_day:
        if secret.process_status == UserActionProcess.ADD.value:
            month_add_points += secret.points
            if start < secret.created_at <= end:
                day_add_points += secret.points
        elif secret.process_status == UserActionProcess.SUB.value:
            month_sub_points += secret.points
            if start < secret.created_at <= end:
                day_sub_points += secret.points

    return {
        "day_add_points": day_add_points,
        "day_sub_points": day_sub_points,
        "month_add_points": month_add_points,
        "month_sub_points": month_sub_points
    }


async def get_key(async_db: AsyncSession, app_key):
    """key信息与关联用户信息"""
    data = await models.PlatformKeyModel.query_model(
        async_db,
        filters=[models.PlatformKeyModel.app_key == app_key, models.PlatformKeyModel.delete_flag == 0],
        joins=[
            joinedload(models.PlatformKeyModel.user)
        ]
    )
    if data is None:
        return None, None
    elif data.user is None:
        return None, None
    user = await models.PlatformUserModel.query_model(
        async_db,
        filters=[models.PlatformUserModel.biz_id == data.user.biz_id, models.PlatformUserModel.delete_flag == 0],
        joins=[
            joinedload(models.PlatformUserModel.folders),
            joinedload(models.PlatformUserModel.platform_keys.and_(
                models.PlatformKeyModel.delete_flag == 0
            )).options(
                load_only(*[
                    models.PlatformKeyModel.biz_id,
                    models.PlatformKeyModel.name,
                    models.PlatformKeyModel.app_key,
                    models.PlatformKeyModel.secret_key
                ])
            )
        ]
    )
    return data, user
