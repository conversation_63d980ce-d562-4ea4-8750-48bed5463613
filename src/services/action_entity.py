from enum import Enum
from functools import lru_cache

from pydantic import BaseModel, Field
from sqlalchemy import func, DateTime, text

delete_flag_filters = ['delete_flag', "EQUAL".lower(), 0]


class FilterEnum(Enum):
    EQUAL = ("EQUAL", "等于", "EQUAL".lower())
    GT = ("GT", "大于", "GT".lower())
    GE = ("GE", "大于等于", "GE".lower())
    LT = ("LT", "小于", "LT".lower())
    LE = ("LE", "小于等于", "LE".lower())
    CONTAIN = ("CONTAIN", "包含", "CONTAIN".lower())
    INCLUDE = ("INCLUDE", "包含于", "INCLUDE".lower())

    def __init__(self, mark, message, code):
        self.mark = mark
        self.message = message
        self.code = code


@lru_cache
def get_filters_conditions():
    data = list()
    for item in FilterEnum:
        data.append({
            'mark': item.mark,
            'message': item.message,
            'code': item.code,
        })
    return data


class FiltersItem(BaseModel):
    conditions: list = Field(title='查询条件',
                             description='[["字段","条件","值"], ["字段","条件","值"]] 关联关系 logical_operator')
    logical_operator: str = Field(title='', description='关联条件=and/or default and')


class SchemaSearchEntity(BaseModel):
    filters: list | None = Field(default=None, title='查询条件',
                                 examples=[[["字段", "条件", "值"]]],
                                 description='查询条件如果是list 默认是and 关系 [["字段","条件","值"], ["字段","条件","值"]]')
    sorts: list | dict | None = Field(default=None, title='排序条件',
                                      examples=[[{'field': 'id', 'order': 'desc'}]],
                                      description="{'field': 'id', 'order': 'desc'}")


def get_filters(table_model, filter_object: list):
    filter_list = []
    for item in filter_object:
        if isinstance(item, list):
            field = item[0]
            condition = str(item[1]).lower()
            value = item[2]
            _mf = getattr(table_model, field)

            if hasattr(_mf, "type"):
                if isinstance(_mf.type, DateTime):
                    value = text(f"'{value}'::timestamp")

            if condition == FilterEnum.EQUAL.code:
                filter_list.append(_mf == value)
            elif condition == FilterEnum.CONTAIN.code:
                filter_list.append(_mf.like("%{}%".format(value)))
            elif condition == FilterEnum.INCLUDE.code:
                filter_list.append(_mf.in_(value))
            elif condition == FilterEnum.GE.code:
                filter_list.append(_mf >= value)
            elif condition == FilterEnum.LE.code:
                filter_list.append(_mf <= value)
            elif condition == FilterEnum.GT.code:
                filter_list.append(_mf > value)
            elif condition == FilterEnum.LT.code:
                filter_list.append(_mf < value)
    return filter_list


def get_sorts(table_model, order_object):
    _sorts = []
    if isinstance(order_object, list):
        for item in order_object:
            if item.get('field') is None:
                continue
            _mf = getattr(table_model, item['field'])
            if _mf is None:
                continue
            if item.get('order') and item.get('order') == 'desc':
                _sorts.append(getattr(table_model, item['field']).desc())
            else:
                _sorts.append(getattr(table_model, item['field']))
    elif isinstance(order_object, dict):
        item = order_object
        if item.get('field'):
            _mf = getattr(table_model, item['field'])
            if _mf:
                if item.get('order') and item.get('order') == 'desc':
                    _sorts.append(_mf.desc())
                else:
                    _sorts.append(_mf)
    return _sorts


def set_filters_sorts(table_model, param: SchemaSearchEntity):
    conditions = get_filters(table_model, param.filters)

    sorts = get_sorts(table_model, param.sorts)
    if not sorts:
        sorts.append(table_model.id.desc())
    return conditions, sorts
