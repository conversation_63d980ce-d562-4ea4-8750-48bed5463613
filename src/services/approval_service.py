"""
审批流
"""
from sqlalchemy import insert, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload
from src.database import myredis, models, session_maker, enums
from pydantic import BaseModel
from src.util import stringify, dateUtil


class StepData(BaseModel):
    approver_id: str
    step: int
    last_step: bool = False


async def create_approval_config(async_db: AsyncSession, ):
    await models.ApprovalConfigModel.generate_model(
        async_db,
        data={

        }
    )


async def query_approval_config(async_db: AsyncSession, page_on, page_size):
    data = await models.ApprovalConfigModel.search_model_paginate(
        async_db,
        filters=[
            models.ApprovalConfigModel.delete_flag == 0
        ],
        page_size=page_size,
        current_page=page_on,
        serialize=True
    )
    return data


async def create_request(async_db: AsyncSession, request_type, request_data: dict):
    """创建审批请求"""
    config = await models.ApprovalConfigModel.query_model(
        async_db,
        filters=[
            models.ApprovalConfigModel.request_type == request_type,
            models.ApprovalConfigModel.is_active == True,
            models.ApprovalConfigModel.delete_flag == 0
        ]
    )
    if not config:
        raise ValueError(f"未找到类型为 {request_type} 的审批配置")

    model_list = list()
    request_biz_id = stringify.get_uid()
    request = models.ApprovalRequestModel(
        biz_id=request_biz_id,
        request_type=request_type,
        requester_id=request_data['user_id'],
        title=request_data.get('title', ''),
        content=request_data.get('content', {})
    )
    model_list.append(request)
    for step_config in config.steps:
        step = StepData(**step_config)
        status = enums.ApprovalStatusEnum.PENDING.value if step.step == 1 else enums.ApprovalStatusEnum.DRAFT.value
        process = models.ApprovalProcessModel(
            biz_id=stringify.get_uid(),
            approval_id=request_biz_id,
            step=step.step,
            approver_id=step.approver_id,
            last_step=step.last_step,
            status=status
        )
        model_list.append(process)

    await models.ApprovalRequestModel.generate_list_model(async_db, model_list)
    return request


async def cancel_request(async_db: AsyncSession, biz_id, user_id):
    request: models.ApprovalRequestModel = await models.ApprovalRequestModel.query_model(
        async_db,
        filters=[
            models.ApprovalRequestModel.biz_id == biz_id,
            models.ApprovalRequestModel.requester_id == user_id,
            models.ApprovalRequestModel.status.notin_([
                enums.ApprovalStatusEnum.APPROVED.value,
                enums.ApprovalStatusEnum.REJECTED.value,
                enums.ApprovalStatusEnum.CANCELED.value
            ])
        ],
        joins=[joinedload(models.ApprovalRequestModel.processes)]
    )
    if request is None:
        raise ValueError("申请不存在或状态不允许取消")
    request.status = enums.ApprovalStatusEnum.CANCELED.value
    request.update_time = dateUtil.get_current_time_in_beijing()

    for process in request.processes:
        if process.status in [enums.ApprovalStatusEnum.DRAFT.value, enums.ApprovalStatusEnum.PENDING.value]:
            process.status = enums.ApprovalStatusEnum.CANCELED.value
            process.update_time = dateUtil.get_current_time_in_beijing()
    await async_db.commit()
    return  await request.serialize_data(request)


async def get_process(async_db, process_id, approver_id):
    process: models.ApprovalProcessModel = await models.ApprovalProcessModel.query_model(
        async_db,
        filters=[
            models.ApprovalProcessModel.biz_id == process_id,
            models.ApprovalProcessModel.approver_id == approver_id,
            models.ApprovalProcessModel.status == enums.ApprovalStatusEnum.PENDING.value
        ],
        joins=[joinedload(models.ApprovalProcessModel.request)]
    )
    return process


async def approve_process(async_db: AsyncSession, process_id: str, approver_id: str, comment: str = ""):
    """审批通过"""
    process: models.ApprovalProcessModel = await get_process(async_db, process_id, approver_id)

    if not process:
        raise ValueError("审批任务不存在或状态不正确")

    # 更新审批任务状态
    process.status = enums.ApprovalStatusEnum.APPROVED.value
    process.comment = comment
    process.action_time = dateUtil.get_current_time_in_beijing()

    # 获取关联的审批请求
    request = process.request

    if process.last_step:
        # 最后一步审批完成
        request.status = enums.ApprovalStatusEnum.APPROVED.value
        request.current_step = process.step
        request.update_time = dateUtil.get_current_time_in_beijing()
        # await async_db.execute(update(models.ApprovalRequestModel).filter(
        #     models.ApprovalRequestModel.biz_id == process.approval_id
        # ).values(
        #     status=enums.ApprovalStatusEnum.APPROVED.value,
        #     current_step=process.step,
        #     update_time=dateUtil.get_current_time_in_beijing()
        # ))
    else:
        # 激活下一步审批
        next_step = process.step + 1
        await async_db.execute(update(models.ApprovalProcessModel).filter(
            models.ApprovalProcessModel.approval_id == process.approval_id,
            models.ApprovalProcessModel.step == next_step,
            models.ApprovalProcessModel.status == enums.ApprovalStatusEnum.DRAFT
        ).values(
            status=enums.ApprovalStatusEnum.PENDING.value
        ))
        # await async_db.execute(update(models.ApprovalRequestModel).filter(
        #     models.ApprovalRequestModel.biz_id == process.approval_id
        # ).values(
        #     current_step=process.step + 1,
        #     update_time=dateUtil.get_current_time_in_beijing()
        # ))
        request.current_step = next_step
        request.update_time = dateUtil.get_current_time_in_beijing()
    await async_db.commit()
    return request


async def reject_process(async_db: AsyncSession, process_id: str, approver_id: str, comment: str = ""):
    """审批拒绝"""
    process: models.ApprovalProcessModel = await get_process(async_db, process_id, approver_id)

    if not process:
        raise ValueError("审批任务不存在或状态不正确")

        # 更新审批任务状态
    process.status = enums.ApprovalStatusEnum.REJECTED.value
    process.comment = comment
    process.action_time = dateUtil.get_current_time_in_beijing()

    # 更新审批请求状态
    request = process.request
    request.status = enums.ApprovalStatusEnum.REJECTED.value
    request.update_time = dateUtil.get_current_time_in_beijing()

    await async_db.commit()
    return request
