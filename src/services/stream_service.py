"""播客流"""
from redis.asyncio import Redis
from src.database import myredis
import time
from src.util import redis_keys_util


async def generate_redis_stream(redis_: Redis, redis_key, task_id):
    count = 0
    first_count = 0
    while True:
        time.sleep(0.2)
        data = await myredis.get_push(redis_, key=redis_key)
        if data:
            first_count = -1
            count = 0
            yield f"{data}\n"
        else:
            count += 1
        if first_count == -1:
            if count > 80:
                break
        else:
            if count > 300:
                yield ""
                break
    lock_task_stream = redis_keys_util.key_audio_stream_lock.format(task_id=task_id)
    await redis_.delete(lock_task_stream)