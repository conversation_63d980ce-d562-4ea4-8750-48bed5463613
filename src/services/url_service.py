import json
import math
import time
from typing import Optional
from urllib.parse import parse_qs, urlparse

import isodate
from prompt_toolkit.contrib.telnet import TelnetServer
from pydantic import BaseModel
from redis.asyncio import Redis
from sqlalchemy import or_
from sqlalchemy.ext.asyncio import AsyncSession

from config import settings
from src.database import models, enums, constants, myredis, session_maker
from src.logger.logUtil import get_logger
from src.model.task_manager import TASK_SOURCE_CHOICES
from src.model.url_detail import LectureUrlDetail
from src.util import stringify, MD5Util, redis_keys_util
from src.util.cacheUtil import open_api_cache
from src.util.commonutil import common_enum_error, ErrorCodeEnum
from src.util.url_detail_utils import base_util, url_default_util, douyin_util

logger = get_logger(__name__)


class DetailUrlSchema(BaseModel):
    url: str
    task_source: str


class BatchDetailUrlSchema(BaseModel):
    url_list: list[DetailUrlSchema]


class UrlService:
    @staticmethod
    def get_url_by_biz_id(biz_id: str) -> Optional[LectureUrlDetail]:
        """根据biz_id获取url详情"""
        if not biz_id:
            raise ValueError("biz_id is required")
        return (
            LectureUrlDetail.select()
            .where(LectureUrlDetail.biz_id == biz_id, LectureUrlDetail.delete_flag == 0)
            .first()
        )

    @staticmethod
    def get_task_url(task_source: str, oss_key: str, url_biz_id: str) -> Optional[str]:
        """获取任务URL"""
        if task_source == TASK_SOURCE_CHOICES["upload"]:
            if not oss_key:
                raise ValueError("请上传文件")
            return oss_key
        url_detail = UrlService.get_url_by_biz_id(url_biz_id)
        if not url_detail:
            raise ValueError("url not found")
        return url_detail.origin_url


def repeat_url(params: list):
    """过滤重复连接"""
    repeat_data = set()
    no_repeat_data = list()
    for item in params:
        repeat_key = f'{item.url}::{item.task_source}'
        if repeat_key not in repeat_data:
            no_repeat_data.append(item)
            repeat_data.add(repeat_key)
    return no_repeat_data


async def get_url_biz(async_db: AsyncSession, biz_id):
    return await models.LectureUrlModel.query_model(
        async_db,
        filters=[
            models.LectureUrlModel.biz_id == biz_id,
            models.LectureUrlModel.delete_flag == 0
        ]
    )


async def parse_baidu_url(url):
    """百度网盘地址解析"""
    if match := constants.BAIDUPAN_ARG_PATTERN.search(url):
        fs_id = match.group(1)
    else:
        return common_enum_error(ErrorCodeEnum.URL_PAN_NOT_DRIVE)
    fileinfo = open_api_cache.get(f"{constants.BAIDUPAN_REDIS_PREFIX}{fs_id}") or {}

    if fileinfo.get("category") == 1:
        duration_minutes = math.ceil((fileinfo.get("duration") or 0) / 60)
    elif fileinfo.get("category") == 2:
        duration_minutes = math.ceil((fileinfo.get("duration") or 0) / 60)
    else:
        return common_enum_error(ErrorCodeEnum.URL_NOT_DURATION)
    media_info = (fileinfo.get("media_info") or {}).get("meta_info")
    if isinstance(media_info, str):
        media_info = json.loads(media_info)

    vip = fileinfo.get("vid", {})
    media_info.update({
        "is_wangpan_vip": True if vip.get("vip_type", -1) in [1, 2] else False,
        "vip_type": vip.get("vip_type", -1)
    })
    return models.LectureUrlModel(
        biz_id=stringify.get_uid(),
        origin_url=url,
        source_url=fileinfo.get("download_url"),
        title=fileinfo.get("filename"),
        duration_minutes=duration_minutes,
        task_source=enums.UrlSourceEnum.BAIDUPAN.value,
        extra_params=json.dumps(media_info),
    )


async def parse_alipan_url(url):
    """阿里网盘地址解析"""
    if match := constants.ALIPAN_ARG_PATTERN.search(url):
        drive_id, file_id = match.group(1), match.group(2)
    else:
        return common_enum_error(ErrorCodeEnum.URL_PAN_NOT_DRIVE)
    fileinfo = open_api_cache.get(f"{constants.ALIPAN_REDIS_PREFIX}{drive_id}_{file_id}") or {}
    duration = math.ceil(float((fileinfo.get("video_media_metadata") or {}).get("duration") or 0))
    if duration == 0:
        duration = math.ceil(
            float((fileinfo.get("video_preview_metadata") or {}).get("duration") or 0)
        )
    duration_minutes = math.ceil(duration / 60)

    vip = fileinfo.get("vip", {})
    extra_params = {
        "category": fileinfo.get("category"),
        "mime_type": fileinfo.get("mime_type"),
        "file_extension": fileinfo.get("file_extension"),
        "method": fileinfo.get("method"),
        "expiration": fileinfo.get("expiration"),
        "description": fileinfo.get("description"),
        "is_wangpan_vip": True if vip.get("identity", "") in ["vip", "svip"] else False,
        "identity": vip.get("identity", "")
    }
    return models.LectureUrlModel(
        biz_id=stringify.get_uid(),
        origin_url=url,
        source_url=fileinfo.get("download_url"),
        title=fileinfo.get("name"),
        duration_minutes=duration_minutes,
        task_source=enums.UrlSourceEnum.ALIPAN.value,
        extra_params=stringify.from_json(extra_params),
    )


async def parse_xiaohongshu_url(url):
    """小红书解析"""
    clean_url = base_util.remove_url_params(url)
    try:
        xhs_data = url_default_util.get_xhs_dur(url)
    except Exception as ex:
        logger.exception(f"小红书链接解析错误：{url}\n{stringify.from_exception(ex)}")
        return common_enum_error(ErrorCodeEnum.URL_NOT_ANALYSIS)
    duration_minutes = xhs_data.get('duration_minutes')
    nickname = xhs_data.get('nickname')
    avatar = xhs_data.get('avatar')
    return models.LectureUrlModel(
        biz_id=stringify.get_uid(),
        origin_url=url,
        source_url=url,
        title=f"小红书_{clean_url}",
        author_name=nickname,
        author_img=avatar,
        duration_minutes=duration_minutes,
        task_source=enums.UrlSourceEnum.XHS.value
    )


async def parse_douyin_url(url):
    clean_url = base_util.remove_url_params(url)
    duration_minutes = 0
    author_name, author_img, title = "", "", f"抖音_{clean_url}"
    error_ex = None
    for _ in range(3):
        try:
            dy = douyin_util.DouyinDownloader(cookies_files=[])
            return_json = dy.download_json_info(url, save_json=False)
            aweme_detail = return_json.get("aweme_detail")
            if aweme_detail is None:
                raise Exception(f"解析失败：{url}")
            author_name = return_json.get("aweme_detail", {}).get("author", {}).get("nickname")
            author_img_list = return_json.get("aweme_detail", {}).get("author", {}).get("avatar_thumb", {}).get(
                "url_list",
                [])
            author_img = author_img_list[0] if author_img_list else ""
            title = return_json.get("aweme_detail", {}).get("item_title")
            duration_minutes = math.ceil(return_json.get("aweme_detail", {}).get("duration", 0) / 1000 / 60)
            break
        except Exception as ex:
            error_ex = ex
            time.sleep(0.5)
    if error_ex:
        logger.error(f"dy地址解析错误:{url}")
        return common_enum_error(ErrorCodeEnum.URL_NOT_ANALYSIS)
    return models.LectureUrlModel(
        biz_id=stringify.get_uid(),
        origin_url=url,
        source_url=url,
        title=title,
        author_name=author_name,
        author_img=author_img,
        duration_minutes=duration_minutes,
        task_source=enums.UrlSourceEnum.DOUYIN.value
    )


async def parse_kuaishou_url(url):
    """快手解析"""
    clean_url = base_util.remove_url_params(url)
    try:
        data = url_default_util.get_ks_dur(clean_url)
    except Exception as ex:
        logger.exception(f"快手链接解析错误：{url}\n{stringify.from_exception(ex)}")
        return common_enum_error(ErrorCodeEnum.URL_NOT_ANALYSIS)
    if not data:
        return common_enum_error(ErrorCodeEnum.URL_NOT_ANALYSIS)
    duration_minutes = data.get('duration_minutes', 0)
    nickname = data.get('nickname')
    avatar = data.get('avatar')
    title = data.get('title')
    return models.LectureUrlModel(
        biz_id=stringify.get_uid(),
        origin_url=url,
        source_url=url,
        title=title,
        author_name=nickname,
        author_img=avatar,
        duration_minutes=duration_minutes,
        task_source=enums.UrlSourceEnum.KUS.value
    )


async def parse_xyz_url(url):
    result = urlparse(url)
    if result.netloc != "www.xiaoyuzhoufm.com":
        return common_enum_error(ErrorCodeEnum.URL_UNRECOGNIZED)
    try:
        ep_id = result.path.split("/")[2]
        xyz_data = url_default_util.get_xyz_dur(ep_id)
    except Exception as ex:
        logger.error(f"小宇宙解析失败：{url}\n{stringify.from_exception(ex)}")
        return common_enum_error(ErrorCodeEnum.URL_NOT_ANALYSIS)
    if not xyz_data:
        return common_enum_error(ErrorCodeEnum.URL_NOT_ANALYSIS)

    source_url = result.geturl()
    duration_str = xyz_data.get("timeRequired")
    if duration_str:
        duration = int(duration_str.split("T")[1].split("M")[0])
    else:
        duration = 1
    title = xyz_data.get("name")
    author = xyz_data.get("author")
    intro = (xyz_data.get("description") or "")[:3000]
    return models.LectureUrlModel(
        biz_id=stringify.get_uid(),
        origin_url=source_url,
        source_url=source_url,
        title=title,
        author_name=author,
        duration_minutes=duration,
        task_source=enums.UrlSourceEnum.XYZ.value,
        intro=intro,
        extra_params=xyz_data.get("associatedMedia"),
    )


async def parse_bilibili_url(url):
    result = urlparse(url)
    if result.netloc != "www.bilibili.com" and result.netloc != "b23.tv" and result.netloc != "m.bilibili.com":
        return common_enum_error(ErrorCodeEnum.URL_UNRECOGNIZED)
    try:

        data, params = url_default_util.get_bil_dur(result)
        if data is None:
            raise Exception("请求结果错误")
        if data.get("is_upower_exclusive"):
            logger.error(f"b站充电视频，不支持解析：{url}")
            return common_enum_error(ErrorCodeEnum.URL_CHARGING_VIDEO)
    except Exception as ex:
        logger.error(f"b站地址解析失败：{url}\n{stringify.from_exception(ex)}")
        return common_enum_error(ErrorCodeEnum.URL_NOT_ANALYSIS)

    path = result.path.rstrip("/")
    source_url = result.geturl()
    # 如果视频数量大于1
    # 视频时长
    duration = max(int(data["duration"] / 60), 1)
    title = data["title"]
    aid = data["aid"]
    bid = data["bvid"]
    cid = data["cid"]
    url = result._replace(
        path=path, query="&".join([f"{k}={v[0] if isinstance(v, list) and v else v}" for k, v in params.items()])
    ).geturl()
    if data["videos"] > 1:
        # 获取视频信息
        for item in data["pages"]:
            if item["page"] == int(params["p"]):
                duration = max(int(item["duration"] / 60), 1)
                title = item["part"]
                cid = item["cid"]
                break
    intro = data["desc"][:3000]

    return models.LectureUrlModel(
        biz_id=stringify.get_uid(),
        origin_url=url,
        source_url=source_url,
        title=title,
        author_name=data["owner"]["name"],
        author_img=data["owner"]["face"],
        pic_path=data["pic"],
        duration_minutes=duration,
        task_source=enums.UrlSourceEnum.BILI.value,
        tags=data["tname"],
        intro=intro,
        remark=bid,
        extra_params=f"aid={aid}&bvid={data['bvid']}&cid={cid}",
    )


async def parse_youtube_url(url):
    result = urlparse(url)
    if result.netloc != "www.youtube.com" and result.netloc != "m.youtube.com":
        return common_enum_error(ErrorCodeEnum.URL_UNRECOGNIZED)
    try:
        params = parse_qs(result.query)
        if "v" in params:
            params = {"v": params["v"]}
        else:
            raise ValueError("Invalid YouTube URL")
        data = url_default_util.get_youtube_dur(params["v"], settings.GOOGLE_YOUTUBE_API)
    except Exception as ex:
        logger.error(f"YouTube解析失败：{url}\n {stringify.from_exception(ex)}")
        return common_enum_error(ErrorCodeEnum.URL_NOT_ANALYSIS)

    path = result.path.rstrip("/")
    source_url = result.geturl()
    # 如果视频数量大于1
    # 视频时长

    items = data.get("items", [])
    if not items:
        logger.warning(f"No details found for video ID: {source_url}")
        return common_enum_error(ErrorCodeEnum.URL_NOT_ANALYSIS)

    content_details = items[0]["contentDetails"]
    snippet = items[0]["snippet"]
    # 将snippet['tags'] 转换为字符串，以， 分割
    tag_str = ",".join(snippet["tags"]) if "tags" in snippet else ""
    duration_obj = isodate.parse_duration(content_details["duration"])
    # Limit intro to 3000 characters
    intro = snippet["description"][:3000]
    duration = max(int(duration_obj.total_seconds() / 60), 1)

    url = result._replace(
        path=path, query="&".join([f"{k}={v[0] if isinstance(v, list) and v else v}" for k, v in params.items()])
    ).geturl()
    return models.LectureUrlModel(
        biz_id=stringify.get_uid(),
        origin_url=url,
        source_url=source_url,
        title=snippet["title"],
        author_name=snippet["channelTitle"],
        author_img="",
        pic_path=snippet["thumbnails"]["default"]["url"],
        duration_minutes=duration,
        task_source=enums.UrlSourceEnum.YOUTUBE.value,
        tags=tag_str,
        intro=intro,
        remark=items[0]["id"],
        extra_params="",
    )


async def create_url_analysis(redis_: Redis, async_db: AsyncSession, url, task_source: str, flush: bool = False):
    if "Short" in task_source:
        task_source = task_source.replace("Short", "")
        try:
            url = base_util.get_full_link(url)
        except Exception as e:
            logger.exception(f"handle full link error, url={url}, error={e}")

    async def get_detail_url():
        url_data = await models.LectureUrlModel.query_model(
            async_db,
            filters=[
                or_(models.LectureUrlModel.source_url == url,
                    models.LectureUrlModel.origin_url == url),
                models.LectureUrlModel.delete_flag == 0
            ]
        )
        return url_data

    md5_value = MD5Util.get_md5_hash(url, enums.Md5Key.URL.value)
    detail = await myredis.get_cached_or_fetch(
        redis_,
        key=redis_keys_util.key_url_detail.format(md5_value=md5_value),
        fetch_func=get_detail_url,
        model=models.LectureUrlModel,
        ttl=redis_keys_util.ttl_url_detail,
        flush=flush
    )
    if detail:
        return detail

    if task_source == enums.UrlSourceEnum.YOUTUBE.value:
        model_data = await parse_youtube_url(url)
    elif task_source == enums.UrlSourceEnum.BILI.value:
        model_data = await parse_bilibili_url(url)
    elif task_source == enums.UrlSourceEnum.XYZ.value:
        model_data = await parse_xyz_url(url)
    elif task_source == enums.UrlSourceEnum.XHS.value:
        model_data = await parse_xiaohongshu_url(url)
    elif task_source == enums.UrlSourceEnum.DOUYIN.value:
        model_data = await parse_douyin_url(url)
    elif task_source == enums.UrlSourceEnum.KUS.value:
        model_data = await parse_kuaishou_url(url)
    elif task_source == enums.UrlSourceEnum.BAIDUPAN.value:
        model_data = await parse_baidu_url(url)
    elif task_source == enums.UrlSourceEnum.ALIPAN.value:
        model_data = await parse_alipan_url(url)
    else:
        return common_enum_error(ErrorCodeEnum.URL_UNRECOGNIZED), url
    return model_data, url


async def create_url_link(url, task_source: str, flush: bool = False):
    if "Short" in task_source:
        task_source = task_source.replace("Short", "")
        try:
            url = base_util.get_full_link(url)
        except Exception as e:
            logger.exception(f"handle full link error, url={url}, error={e}")

    async def get_detail_url():
        async with session_maker.get_db_manager() as async_db_:
            url_data = await models.LectureUrlModel.query_model(
                async_db_,
                filters=[
                    or_(models.LectureUrlModel.source_url == url,
                        models.LectureUrlModel.origin_url == url),
                    models.LectureUrlModel.delete_flag == 0
                ],
                serialize=True
            )
            return url_data

    md5_value = MD5Util.get_md5_hash(url, enums.Md5Key.URL.value)
    async with myredis.get_async_redis() as redis_:
        detail = await myredis.get_cached_or_fetch(
            redis_,
            key=redis_keys_util.key_url_detail.format(md5_value=md5_value),
            fetch_func=get_detail_url,
            model=models.LectureUrlModel,
            ttl=redis_keys_util.ttl_url_detail,
            flush=flush
        )
    if detail:
        return detail
    else:
        async with session_maker.get_db_manager() as async_db:
            detail = await models.LectureUrlModel.generate_model(
                async_db,
                data={
                    "biz_id": stringify.get_uid(),
                    "origin_url": url,
                    "source_url": url,
                    "duration_minutes": 1,
                    "task_source": task_source,
                    "title":"三方url"
                }
            )
        return detail
