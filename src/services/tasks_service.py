"""
任务相关
"""
import asyncio
import json
import random
import re
import string
from datetime import datetime


from redis.asyncio import Redis
from sqlalchemy import or_, and_, select, func
from sqlalchemy.ext.asyncio import AsyncSession

from src.database import models, enums, myredis, constants
from src.logger.logUtil import get_logger
from src.model.lecture_ai import AITYPE
from src.model.task_manager import (
    TASK_SOURCE_CHOICES,
)
from src.services import action_schema
from src.services.article_service import async_query_article_details
from src.services.lock_points_services import points_verify, points_expend_source_task
from src.services.url_service import get_url_biz
from src.util import MD5Util, dateUtil, stringify, redis_keys_util
from src.util.commonutil import common_enum_error, ErrorCodeEnum

logger = get_logger(__name__)
md5key = "task_source"


async def async_get_folder_by_id(async_db: AsyncSession, user, folder_id):
    folder = await models.FolderModel.query_model(
        async_db,
        filters=[
            models.FolderModel.user_id == user.biz_id,
            models.FolderModel.id == folder_id,
            models.FolderModel.delete_flag == 0
        ]
    )
    return folder


async def task_today(async_db: AsyncSession, user):
    """当天任务数"""
    status_list = [
        enums.TaskStatusEnum.PROCESSING.value,
        enums.TaskStatusEnum.FINISHED.value,
        enums.TaskStatusEnum.FINISH.value
    ]
    today_date = dateUtil.get_current_time_in_beijing().replace(hour=0, minute=0, second=0, microsecond=0)
    tasks = await models.LectureTaskModel.search_model_all(
        async_db,
        filters=[
            models.LectureTaskModel.user_id == user.biz_id,
            models.LectureTaskModel.start_time >= today_date,
            or_(
                models.LectureTaskModel.status.in_(status_list),
                and_(
                    models.LectureTaskModel.status.in_(
                        [enums.TaskStatusEnum.CONFIRM.value,
                         enums.TaskStatusEnum.WAITING.value, ]
                    ),
                    models.LectureTaskModel.delete_flag == 0
                )
            )
        ],
        fields=[
            models.LectureTaskModel.duration_minutes,
            models.LectureTaskModel.user_id,
            models.LectureTaskModel.start_time,
            models.LectureTaskModel.status,
            models.LectureTaskModel.delete_flag
        ]
    )
    return tasks


async def verify_task_info(async_db: AsyncSession, user, lec):
    new_url = lec.url
    # 来源验证
    if stringify.is_empty(lec.task_source):
        return False, "请填写数据来源"
    if lec.task_source == TASK_SOURCE_CHOICES["upload"] or lec.task_source == TASK_SOURCE_CHOICES["recording"]:
        lec.author = user.name
        if not lec.oss_key:
            return False, "请上传文件"
        lec.url = lec.oss_key
    elif lec.url_biz_id:
        pattern = r'http(?:s)?://xhslink\.com/[^\s，]+'
        if re.match(pattern, lec.url):
            lec.url = new_url
            lec.name = f"小红书_{new_url}"
        else:
            urls = await get_url_biz(async_db, lec.url_biz_id)
            if urls:
                lec.img_url = urls.pic_path
                lec.url = urls.origin_url
            else:
                return False, "地址解析未完成"
    # 时长验证
    if lec.duration_minutes is None:
        lec.duration_minutes = 0
    # 如果duration_minutes < 0，就直接返回, 但允许 null
    if lec.duration_minutes < 0:
        return False, "视频时长不合法"

    elif lec.duration_minutes > 240:
        return False, "视频时长不能超过4个小时"
    return True, lec


def response_task(lec, message="", success: bool = False):
    """原文任务创建统一返还结果"""
    return {
        "biz_id": None if not success else lec.biz_id,
        "duration": lec.duration_minutes,
        "url_biz_id": lec.url_biz_id,
        "url": lec.url,
        "success": success,
        "message": message
    }


async def create_task_source(async_db: AsyncSession, user, lec, folder, task_param: dict = None):
    """
    创建原文任务
    """
    task_ = lec.__dict__
    if task_param:
        task_.update(task_param)
    lec = models.LectureTaskModel(**task_)
    lec.user_id = user.biz_id
    lec.biz_id = stringify.get_uid()
    lec.token = "".join(random.sample(string.ascii_letters + string.digits, 6))
    try:
        # 积分验证
        if await points_verify(async_db, user.biz_id, lec.duration_minutes):
            return response_task(lec, message="您剩余使用额度不足以转录当前内容，请充值。")

        lec.ctrl_params.setdefault("input_language", "auto")
        lec.ctrl_params.setdefault("output_language", "zh")
        lec.ctrl_params.setdefault("image_mode", "more")
        lec.ctrl_params.setdefault("enable_speaker_recognition", True)
        lec.ctrl_params.setdefault("enable_ai_polish", True)
        lec.ctrl_params.setdefault("enable_total_summary", True)
        lec.ctrl_params.setdefault("total_summary_template", "default")
        lec.ctrl_params.setdefault("enable_outline", True)
        lec.ctrl_params.setdefault("enable_podcast_summary", True)
        lec.ctrl_params.setdefault("custom_words", [])
        # 以参数校验+URL 校验 md5
        target_data = dict(sorted(lec.ctrl_params.items()))
        if "folder_id" in target_data:
            del target_data["folder_id"]
        md5_params = f"{json.dumps(target_data)}--{lec.url}"
        param_md5 = MD5Util.get_md5_hash(md5_params, md5key)
        lec.md5 = param_md5
        # 判断是否有任务已经完成
        task_arr = await models.LectureTaskModel.search_model_all(
            async_db,
            filters=[
                models.LectureTaskModel.md5 == param_md5,
                models.LectureTaskModel.delete_flag == 0
            ],
            limit_size=20
        )
        finish_task = None
        for task in task_arr:
            if task and task.user_id == lec.user_id:
                return response_task(lec, message="任务已经创建过")
            if task.status.lower() == enums.TaskStatusEnum.FINISH.value.lower():
                finish_task = task

        new_article_biz_id = stringify.get_uid()

        lec.name = lec.name or lec.url
        lec.article_biz_id = new_article_biz_id
        lec.start_time = dateUtil.get_current_time_in_beijing()
        lec.end_time = dateUtil.get_current_time_in_beijing()

        new_article = {
            "biz_id": new_article_biz_id,
            "name": lec.name,
            "article_source": lec.task_source,
            "language": "zh",
            "video_url": lec.url,
            "img_url": lec.img_url,
            "author": lec.author,
        }
        new_article_details = list()
        new_ai_polish = None
        if finish_task:
            article = await async_query_article_details(async_db, finish_task.article_biz_id)
            new_article.update({
                "name": article.name,
                "author": article.author,
                "article_source": article.article_source,
                "language": article.language,
                "video_url": article.video_url,
                "img_url": article.img_url,
                "out_language": article.out_language,
                "translated_outline_json": article.translated_outline_json,
                "translated_total_summary_json": article.translated_total_summary_json,
                "corpus": article.corpus
            })
            for item in article.details:
                origin_text = re.sub(r'<font[^>]*>|</font>', '', item.origin_text)
                translated_modified_text = item.translated_modified_text_json.get(article.out_language)
                translated_modified_text_json = {
                    article.out_language: re.sub(r'<font[^>]*>|</font>', '', translated_modified_text)
                }
                new_article_details.append({
                    "biz_id": stringify.get_uid(),
                    "article_id": new_article_biz_id,
                    "index": item.index,
                    "start_time": item.start_time,
                    "end_time": item.end_time,
                    "origin_text": origin_text,
                    "speaker": item.speaker,
                    "translated_modified_text_json": translated_modified_text_json,
                    "oss_pic_path": item.oss_pic_path,
                    "local_pic_path": item.local_pic_path,
                    "pic_keywords": item.pic_keywords
                })
            lec.status = enums.TaskStatusEnum.FINISHED.value
            lec.process_percent = 100

            if lec.ctrl_params.get("enable_ai_polish", True):
                new_ai_polish = {
                    "biz_id": "task_" + stringify.get_uid(),
                    "user_id": user.biz_id,
                    "article_biz_id": new_article_biz_id,
                    "task_type": AITYPE.AI_POLISHING.value,
                    "name": AITYPE.AI_POLISHING.name,
                    "status": enums.TaskStatusEnum.FINISHED.value,
                    "process_percent": 100,
                    "start_time": dateUtil.get_current_time_in_beijing(),
                    "end_time": dateUtil.get_current_time_in_beijing(),
                    "ctrl_params": lec.ctrl_params
                }
        kwargs = {
            "lec": lec.__dict__,
            "article": new_article,
            "article_details": new_article_details,
            "folder_id": folder.id,
            "new_ai_polish": new_ai_polish
        }
        # 扣除积分创建任务事务
        success, message = await points_expend_source_task(user.biz_id, lec.duration_minutes, **kwargs)
        if success:
            key = redis_keys_util.key_source_task_points.format(task_id=lec.biz_id)
            async with myredis.get_async_redis() as redis_:
                await redis_.setex(key, 60 * 30, stringify.from_json({
                    "task_id": lec.biz_id,
                    "duration_minutes": lec.duration_minutes,
                    "user_id": user.biz_id,
                    "record_biz_id": message
                }))
            return response_task(lec, "success", success)
        return response_task(lec, message, success)
    except Exception:
        logger.exception("create task error")
        return response_task(lec, message="create task error")


async def create_task_batch(async_db: AsyncSession, user, lec_list: list, folder):
    """批量创建任务"""
    try:
        async_task, result_task = list(), list()
        for item in lec_list:
            verify, new_lec = await verify_task_info(async_db, user, item)
            if verify:
                # response = await create_task_source(async_db, user, new_lec, folder)
                # result_task.append(response)
                async_task.append(create_task_source(async_db, user, new_lec, folder))
            else:
                result_task.append(response_task(item, message=new_lec))
        if len(async_task) > 0:
            async_results = await asyncio.gather(*async_task)
            result_task.extend(async_results)
        return True, result_task
    except Exception:
        logger.exception("任务创建失败")
        return False, "任务创建失败"


async def get_ai_task(async_db: AsyncSession, article_biz_id, task_type):
    """ai 任务"""
    ai_tasks = await models.LectureAITaskModel.query_model(
        async_db,
        filters=[
            models.LectureAITaskModel.article_biz_id == article_biz_id,
            models.LectureAITaskModel.task_type == task_type,
            models.LectureTaskModel.delete_flag == 0
        ]
    )
    return ai_tasks


class SearchTaskFolderSchema(action_schema.PaginateSchema):
    folder_id: int = 0
    source: str = "pc"
    name: str = None
    start_time: datetime | str = None
    end_time: datetime | str = None


async def search_task_content(
        redis_: Redis,
        async_db: AsyncSession,
        param: SearchTaskFolderSchema,
        user_id: str,
        keys_ids=None,
        flush=True
):
    """获取目录下所有笔记本与任务"""
    folder_id = param.folder_id

    if folder_id == 0:
        current_folder = await models.FolderModel.query_model(
            async_db,
            filters=[
                models.FolderModel.name == constants.DEFAULT_FOLDER,
                models.FolderModel.parent_id.is_(None),
                models.FolderModel.user_id == user_id
            ]
        )
        if current_folder and current_folder.depth == 1:
            folder_id = current_folder.id
        else:
            return common_enum_error(ErrorCodeEnum.FOLDER_NOT_EXISTS)
    else:
        current_folder = await models.FolderModel.query_model(
            async_db,
            filters=[
                models.FolderModel.id == folder_id,
                models.FolderModel.delete_flag == 0,
                models.FolderModel.user_id == user_id
            ]
        )
        if current_folder is None:
            return common_enum_error(ErrorCodeEnum.FOLDER_NOT_EXISTS)
    conditions = [
        or_(
            models.LectureTaskModel.user_id == user_id,
            models.LectureTaskModel.open_platform_id.in_(keys_ids)
        ),
        models.LectureTaskModel.delete_flag == 0,
        models.LectureTaskModel.folder_id == folder_id
    ]
    if not stringify.is_empty(param.name):
        conditions.append(models.LectureTaskModel.name.like(f"%{param.name}%"))
    if not stringify.is_empty(param.start_time):
        conditions.append(models.LectureTaskModel.start_time >= param.start_time)

    if not stringify.is_empty(param.end_time):
        conditions.append(models.LectureTaskModel.start_time <= param.end_time)

    async def task_list():
        task_content = await models.LectureTaskModel.search_model_paginate(
            async_db,
            page_size=param.page_size,
            current_page=param.page_no,
            filters=conditions,
            sorts=[models.LectureTaskModel.id.desc()],
            serialize=True
        )
        combined_items = task_content.get("items", [])
        if param.source == "mobile":
            sub_folder_con = [
                models.FolderModel.parent_id == folder_id,
                models.FolderModel.delete_flag == 0,
                models.FolderModel.user_id == user_id
            ]
            if not stringify.is_empty(param.name):
                sub_folder_con.append(models.FolderModel.name.like(f"%{param.name}%"))
            sub_folders = await models.FolderModel.search_model_all(
                async_db,
                limit_size=100,
                filters=sub_folder_con,
                serialize=True
            )
            combined_items = sorted(
                [{"type": "folder", **f} for f in sub_folders] +
                [{"type": "task", **t} for t in task_content.get("items", [])],
                key=lambda x: (0 if x.get("type") == "folder" else 1, -x["id"]),
            )

        parent_folders = list()
        parent_id = current_folder.parent_id
        while parent_id not in [None, 0]:
            f = await models.FolderModel.query_model(
                async_db,
                filters=[models.FolderModel.id == parent_id,
                         models.FolderModel.delete_flag == 0, models.FolderModel.user_id == user_id]
            )
            if f is None:
                break
            parent_id = f.parent_id
            parent_folders.append(await models.FolderModel.serialize_data(f))

        current_folder_json = await models.FolderModel.serialize_data(current_folder)
        current_folder_json["total"] = task_content.get("total", 0)
        return {
            "ancestors": parent_folders,
            "current_folder": current_folder_json,
            "content": combined_items,
            "page_no": param.page_no,
            "page_size": param.page_size,
        }

    target_data = dict(sorted(param.__dict__.items()))

    md5_conditions = MD5Util.get_md5_hash(json.dumps(target_data), enums.Md5Key.FILTER.value)

    key = redis_keys_util.key_task_list.format(user_id=user_id, folder_id=folder_id, page_no=param.page_no,
                                               page_size=param.page_size, conditions=md5_conditions)
    data = await myredis.get_cached_or_fetch(
        redis_,
        key,
        task_list,
        ttl=redis_keys_util.ttl_task_list,
        flush=flush
    )
    return data


async def get_task_duration_minutes(async_db: AsyncSession, user_id):
    # 构建查询条件
    filter_conditions = [
        models.LectureTaskModel.user_id == user_id,
        models.LectureTaskModel.status.in_([
            enums.TaskStatusEnum.FINISH.value,
            enums.TaskStatusEnum.FINISHED.value
        ])
    ]
    data = await async_db.execute(
        select(
            func.sum(models.LectureTaskModel.duration_minutes).label("total_duration"),
            func.count(models.LectureTaskModel.id).label("total_count")
        ).filter(*filter_conditions)
    )
    result =data.first()

    if result is None:
        return 0, 0

    total_duration = result.total_duration if result.total_duration else 0
    task_count = result.total_count if result.total_count else 0
    return total_duration, task_count

