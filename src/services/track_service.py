"""
后台埋点、归因、数据统计
"""
import json
from typing import Optional
from sqlalchemy.ext.asyncio import AsyncAttrs, AsyncSession
from src.database import models, enums, session_maker
from src.services import share_service
from pydantic import BaseModel, Field, constr
from src.util import stringify
from src.logger.logUtil import get_logger, LoggerName

logger = get_logger(LoggerName.client)


class ExtraInfo(BaseModel):
    utm_source: Optional[str] = Field(None, title="数据渠道来源", description="用户注册的来源")
    utm_medium: Optional[str] = Field(None, title="流量来源媒介", description="如 email、cpc（按点击付费广告）等。")
    utm_term: Optional[str] = Field(None, title="用于标识广告的关键词", description="一般用在付费搜索广告中。")
    utm_content: Optional[str] = Field(None, title="用于区分同一广告活动中的不同内容或链接",
                                       description="便于分析哪种广告文案效果更好")
    utm_campaign: Optional[str] = Field(None, title="标识具体的营销活动", description="通常用于区分不同的广告活动。")


class TrackService(object):

    def __init__(self):
        pass

    @classmethod
    async def share_article_register(cls, async_db: AsyncSession, user, content: str):
        """
        文章分享 用户注册
        utm_content = {"access_id": "", "share_token":""}
        """
        if stringify.is_empty(content):
            logger.error(f"文章分享用户注册：缺少参数：content={content}")
            return
        try:
            json_data = json.loads(content)
        except Exception as ex:
            logger.error(f"文章分享用户注册：序列化错误：content={content}\n{stringify.from_exception(ex)}")
            return
        access_id = json_data.get("access_id")
        share_token = json_data.get("share_token")
        if stringify.is_empty(access_id) is None or stringify.is_empty(share_token):
            logger.error(f"文章分享用户注册：缺少参数：access_id={access_id}, share_token={share_token}")
            return
        await share_service.access_register(
            async_db,
            access_id, share_token, user
        )

    @classmethod
    async def event_register(cls, user, extra_info: ExtraInfo|dict):
        try:
            if isinstance(extra_info,dict):
                extra_info = ExtraInfo(**extra_info)
            if extra_info.utm_source == enums.TrackChannelEnum.SHARED.value:
                async with session_maker.get_db_manager() as async_db:
                    await cls.share_article_register(async_db, user, extra_info.utm_content)

        except Exception as ex:
            pass
