"""
粉丝
"""
from sqlalchemy import union_all, func, select, literal, desc, text
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload
from src.database import myredis, models, session_maker, enums, constants
from pydantic import BaseModel
from src.util import stringify, dateUtil


async def search_union_paginate(async_db: AsyncSession, page_no, page_size, user_id):
    async def get_fans_order():
        pass

    affiliates = await models.AffiliatesModel.query_model(
        async_db,
        filters=[
            models.AffiliatesModel.user_id == user_id,
            models.AffiliatesModel.delete_flag == 0
        ]
    )
    if affiliates is None:
        return {"total": 0, "list": []}
    # 构建两个select语句
    stmt1 = select(
        models.AffiliatesFansModel.name.label('fans_name'),
        # text("0 AS pay_amount"),
        # text("0 AS order_point"),
        # text("0 AS order_status"),
        literal(None).label("pay_amount"),
        literal(constants.FANS_REGISTER).label("order_point"),
        literal(2).label("order_status"),
        models.AffiliatesFansModel.create_time.label('create_time')
    ).where(models.AffiliatesFansModel.affiliates_id == affiliates.biz_id)

    stmt2 = select(
        models.AffiliatesOrderModel.fans_name.label("fans_name"),
        models.AffiliatesOrderModel.pay_amount.label("pay_amount"),
        models.AffiliatesOrderModel.order_point.label("order_point"),
        models.AffiliatesOrderModel.order_status.label("order_status"),
        models.AffiliatesOrderModel.create_time.label('create_time')
    ).where(models.AffiliatesOrderModel.affiliates_id == affiliates.biz_id)

    # 合并并排序
    combined = union_all(stmt1, stmt2).alias('combined')
    final_query = select(combined).order_by(desc(combined.c.create_time))

    # 添加分页
    paginated_query = final_query.offset((page_no - 1) * page_size).limit(page_size)

    async_result = await async_db.execute(paginated_query)
    data = async_result.unique().all()
    result_data = list()
    for item in data:
        if stringify.is_phone(item.fans_name):
            name = stringify.mask_phone_regex(item.fans_name)
        else:
            name = stringify.mask_string_flexible(item.fans_name, min_length=2)
        sub_data = item._asdict()
        sub_data["fans_name"] = name
        sub_data["create_time"] = item.create_time.strftime(stringify.TIME_FTIME)
        result_data.append(sub_data)
    count_query = select(func.count()).select_from(combined)
    async_count = await async_db.execute(count_query)
    total_count = async_count.scalar()
    return {"total": total_count, "list": result_data}



# async def pay_