from config import settings
from src.logger.logUtil import get_logger
from src.util.stringify import get_uid
from sqlalchemy.ext.asyncio import AsyncSession
from src.database import models
from src.model.member_manager import MembershipType, membership_initial_unlock
from src.services.tasks_service import task_today
from src.database import myredis
from src.util.redis_keys_util import key_source_lock_task, key_source_task_limit
from src.util.dateUtil import get_current_time_in_beijing
from datetime import timedelta, datetime

logger = get_logger(__name__)


def member_forever(member):
    if member.level == -1 or member.points > 9000 * 100 * 100:
        return True
    return False


async def re_enable_account(user_po, login_name: str):
    if user_po.delete_flag == 2:
        user_po.delete_flag = 0
        user_po.deletion_request_time = None
        user_po.save()


def generate_user_biz_id():
    return "user_" + get_uid()


async def system_user(user_po):
    """超级用户权限"""
    customers = settings.CUSTOMER_SERVICE_SEND.split(",")
    all_flag = customers and user_po.biz_id in customers
    return all_flag


async def user_member(async_db: AsyncSession, biz_id):
    member = await models.LectureMemberModel.query_model(
        async_db,
        filters=[
            models.LectureMemberModel.user_id == biz_id,
            models.LectureMemberModel.delete_flag == 0
        ]
    )
    if member is None:
        now = get_current_time_in_beijing()
        today = now.date()
        member = await models.LectureMemberModel.generate_model(
            async_db,
            data={
                "user_id": biz_id,
                "membership_type": MembershipType.FREE.value,
                "start_date": today,
                "reset_date": today,
                "extension_date": today,
                "end_date": today + timedelta(days=30),
                "remaining_unlock": membership_initial_unlock[MembershipType.FREE],
                "points": 90,
                "created_at": now,
                "updated_at": now
            }
        )
    return member


async def user_task_batch_limit(async_db: AsyncSession, user):
    """
    批量任务限制
    """
    member = await user_member(async_db, user.biz_id)

    count = 0
    match member.membership_type:
        case MembershipType.VIP:
            count = MembershipType.VIP.max_batch_count()
        case MembershipType.STANDARD:
            count = MembershipType.STANDARD.max_batch_count()
        case MembershipType.FREE:
            count = MembershipType.FREE.max_batch_count()
    if member_forever(member):
        count = 15
    return count, member


async def user_task_day_limit(async_db: AsyncSession, user, submit_task, member=None):
    if not member:
        member = await user_member(async_db, user.biz_id)

    @myredis.lock(key_source_task_limit.format(user_id=user.biz_id))
    async def lock_task_today():
        submit_minutes, submit_count = 0, 0
        for item in submit_task:
            submit_minutes += item.duration_minutes
            submit_count += 1

        tasks = await task_today(async_db, user)
        max_count, max_minutes = 0, 0

        match member.membership_type:
            case MembershipType.VIP:
                max_count, max_minutes = MembershipType.VIP.max_day_count_minutes()
            case MembershipType.STANDARD:
                max_count, max_minutes = MembershipType.STANDARD.max_day_count_minutes()
            case MembershipType.FREE:
                max_count, max_minutes = MembershipType.FREE.max_day_count_minutes()

        if member_forever(member):
            max_count = 100
            max_minutes = 3000

        user_ids = [
            "user_26c412c9-6ca6-81f1-08c1-553d9496cd35",
            "user_1e8f6d17-cd04-242b-7660-fd2c777925bb",
            "user_bcf826e7-ef2b-a12e-5441-bcd901e7c73a",
            "user_88550477-3348-7311-c3f6-69ddf55af351"
        ]
        current_date = datetime.now()

        target_date = datetime.strptime("2025-08-04", "%Y-%m-%d")
        if user.biz_id in user_ids and current_date < target_date:
            max_count = 200
        if len(tasks) + submit_count > max_count:
            return False, f"超过每日任务数量：{max_count}"

        current_minutes = 0
        for item in tasks:
            current_minutes += item.duration_minutes
        if submit_minutes + current_minutes > max_minutes:
            return False, f"超过每日任务总时长：{max_minutes} 分钟"

        return True, None

    return await lock_task_today()
