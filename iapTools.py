import asyncio

from appstoreserverlibrary.api_client import GetTransactionHistoryVersion
from appstoreserverlibrary.models.NotificationHistoryRequest import NotificationHistoryRequest
from appstoreserverlibrary.models.TransactionHistoryRequest import TransactionHistoryRequest
from datetime import datetime, timedelta

from pytz import timezone

from src.api.pay.iapNew import apple_api_client, create_apple_api_client, signed_data_verifier, create_signed_data_verifier,order_refund
from appstoreserverlibrary.models.Environment import Environment

from src.database import session_maker
from src.logger.logUtil import get_logger
from src.util.third_party.apple_client import apple_client_manager
from src.util.third_party.apple_service import apple_pay_service

logger = get_logger(__name__)


class ApplePayTools:
    """苹果支付工具类"""

    def __init__(self, use_sandbox: bool = False):
        """
        初始化工具类

        Args:
            use_sandbox: 是否使用沙盒环境
        """
        try:
            self.environment = Environment.SANDBOX if use_sandbox else Environment.PRODUCTION
            self.environment_str = "Sandbox" if use_sandbox else "Production"
            self.client_manager = apple_client_manager
            self.pay_service = apple_pay_service


            # 验证客户端是否可用
            if not self.client_manager.get_api_client(self.environment):
                logger.warning(f"⚠️ {self.environment_str} 环境客户端初始化可能有问题")

            logger.info(f"{'🧪 沙盒' if use_sandbox else '🏭 生产'}环境工具类初始化完成")

        except Exception as e:
            logger.error(f"❌ 工具类初始化失败: {str(e)}")
            raise

    def get_api_client(self):
            """获取 API 客户端"""
            return self.client_manager.get_api_client(self.environment)

    def get_data_verifier(self):
            """获取数据验证器"""
            return self.client_manager.get_data_verifier(self.environment)

    def get_transaction_info(self, transaction_id):
            """
            获取交易信息

            Args:
                transaction_id: 交易ID

            Returns:
                交易信息对象
            """
            try:
                client = self.get_api_client()
                if not client:
                    raise Exception("Apple API 客户端未初始化")

                logger.info(f" 获取交易信息: {transaction_id}")
                transaction_info = client.get_transaction_info(transaction_id)

                if transaction_info:
                    logger.info(f"✅ 交易信息获取成功: {transaction_id}")
                else:
                    logger.warning(f"⚠️ 未找到交易信息: {transaction_id}")

                return transaction_info

            except Exception as e:
                logger.error(f"❌ 获取交易信息失败: {str(e)}")
                raise
    def order_refund(self, order_id):
            """退款"""
            asyncio.run(order_refund(order_id))






    def get_notification_history(self):
        client = self.get_api_client()
        verifier = self.get_data_verifier()

        if not client or not verifier:
            raise Exception("客户端未初始化")

        now = datetime.now()
        start_date = now - timedelta(days=10)
        end_date=now+timedelta(days=10)
        request_params=NotificationHistoryRequest()
        request_params.startDate=int(start_date.timestamp() * 1000)
        request_params.endDate=int(end_date.timestamp() * 1000)

        notification_history = client.get_notification_history(None, request_params)
        print(f"paginationToken:{notification_history.paginationToken} ; hasMore:{notification_history.hasMore}")
        if notification_history.notificationHistory:
            for item in notification_history.notificationHistory:
                # print(f"item:{item}")
                signed_payload=item.signedPayload
                send_attempts=item.sendAttempts
                if signed_payload:
                    signed_payload_result=verifier.verify_and_decode_notification(signed_payload)
                    parsed_transaction = verifier.verify_and_decode_signed_transaction(signed_payload_result.data.signedTransactionInfo)
                # print(f"交易记录：：：{signed_payload_result.notificationUUID}:{signed_payload_result.rawNotificationType}:{parsed_transaction}")
                print(f"signed_payload_result:UUID:{signed_payload_result.notificationUUID} type:{signed_payload_result.rawNotificationType} environment:{signed_payload_result.data.rawEnvironment} "
                      f"transactionId:{parsed_transaction.transactionId} appAccountToken:{parsed_transaction.appAccountToken} productId:{parsed_transaction.productId}  purchaseDate:{datetime.fromtimestamp(parsed_transaction.purchaseDate/1000, tz=timezone('Asia/Shanghai'))}"
                      f" signedDate：{datetime.fromtimestamp(parsed_transaction.signedDate/1000, tz=timezone('Asia/Shanghai')) } rawEnvironment:{parsed_transaction.rawEnvironment} price:{parsed_transaction.price}")


    def send_consumption_data(self, transaction_id: str, order_id: str):
        """发送消费数据"""
        async def _async_operation():
            async with session_maker.get_db_manager() as async_db:
               await apple_pay_service.send_consumption_data(async_db, transaction_id, order_id,self.environment_str)
        asyncio.run(_async_operation())

    def test_notification(self):
       test_result= self.get_api_client().request_test_notification()
       result_status= self.get_api_client().get_test_notification_status(test_result.testNotificationToken)
       print(f"test_result:{test_result} result_status:{result_status}")#Ç



if __name__ == "__main__":
    # 使用全局客户端
    tools = ApplePayTools(use_sandbox=True)

    transaction_id = '2000000973025263'
    order_id = '50b98617-d1bb-4bdf-b732-548e023d499d'
    # asyncio.run(a_async_operation())
    # 获取交易信息
    # transaction_info = tools.get_transaction_info(transaction_id)
    # print(f"交易信息: {transaction_info}")

    # notification_history = tools.get_notification_history()
    # print(f"通知历史记录：{notification_history}")
    # tools.order_refund("6d570253-2f9d-45d0-8c86-8913fdf0ef42")

    #测试向苹果发送消息
    # tools.send_consumption_data(transaction_id,order_id)

    #测试消息连通性
    tools.test_notification()
