import asyncio

from appstoreserverlibrary.api_client import GetTransactionHistoryVersion
from appstoreserverlibrary.models.NotificationHistoryRequest import NotificationHistoryRequest
from appstoreserverlibrary.models.TransactionHistoryRequest import TransactionHistoryRequest
from datetime import datetime, timedelta

from pytz import timezone

from src.api.pay.iap import apple_api_client, create_apple_api_client, signed_data_verifier, create_signed_data_verifier,order_refund
from appstoreserverlibrary.models.Environment import Environment
from src.logger.logUtil import get_logger

logger = get_logger(__name__)


class ApplePayTools:
    """苹果支付工具类"""

    def __init__(self, use_sandbox=False):
        if use_sandbox:
            self.client = create_apple_api_client(Environment.SANDBOX)
            self.verifier = create_signed_data_verifier(Environment.SANDBOX)
            logger.info("🧪 使用沙盒环境客户端")
        else:
            self.client =  create_apple_api_client(Environment.PRODUCTION)
            self.verifier = create_signed_data_verifier(Environment.PRODUCTION)
            logger.info("🏭 使用全局环境客户端")

    def get_transaction_info(self, transaction_id):
        """获取交易信息"""
        if not self.client:
            raise Exception("Apple API 客户端未初始化")

        return self.client.get_transaction_info(transaction_id)
    def order_refund(self, order_id):
        """退款"""
        asyncio.run(order_refund(order_id))


    def get_refund_history(self, transaction_id, revision=None):
        """获取退款历史"""
        if not self.client:
            raise Exception("Apple API 客户端未初始化")

        return self.client.get_refund_history(transaction_id, revision)

    def get_transaction_history(self, transaction_id):
        if not self.client:
            raise Exception("Apple API 客户端未初始化")
        request_params = TransactionHistoryRequest()

        transaction_history = self.client.get_transaction_history(transaction_id, None, request_params,
                                                                  GetTransactionHistoryVersion.V2)
        print(
            f"revision:${transaction_history.revision} ; hasMore:${transaction_history.hasMore} ; rawEnvironment:{transaction_history.rawEnvironment}")
        transaction_history_result = signed_data_verifier.verify_and_decode_signed_transaction(
            transaction_history.signedTransactions[0])
        return transaction_history_result

    def get_notification_history(self):
        if not self.client:
            raise Exception("Apple API 客户端未初始化")

        now = datetime.now()
        start_date = now - timedelta(days=10)
        end_date=now+timedelta(days=10)
        request_params=NotificationHistoryRequest()
        request_params.startDate=int(start_date.timestamp() * 1000)
        request_params.endDate=int(end_date.timestamp() * 1000)

        notification_history = self.client.get_notification_history(None, request_params)
        print(f"paginationToken:{notification_history.paginationToken} ; hasMore:{notification_history.hasMore}")
        if notification_history.notificationHistory:
            for item in notification_history.notificationHistory:
                # print(f"item:{item}")
                signed_payload=item.signedPayload
                send_attempts=item.sendAttempts
                if signed_payload:
                    signed_payload_result=self.verifier.verify_and_decode_notification(signed_payload)
                    parsed_transaction = self.verifier.verify_and_decode_signed_transaction(signed_payload_result.data.signedTransactionInfo)
                # print(f"交易记录：：：{signed_payload_result.notificationUUID}:{signed_payload_result.rawNotificationType}:{parsed_transaction}")
                print(f"signed_payload_result:UUID:{signed_payload_result.notificationUUID} type:{signed_payload_result.rawNotificationType} environment:{signed_payload_result.data.rawEnvironment} "
                      f"transactionId:{parsed_transaction.transactionId} appAccountToken:{parsed_transaction.appAccountToken} productId:{parsed_transaction.productId}  purchaseDate:{datetime.fromtimestamp(parsed_transaction.purchaseDate/1000, tz=timezone('Asia/Shanghai'))}"
                      f" signedDate：{datetime.fromtimestamp(parsed_transaction.signedDate/1000, tz=timezone('Asia/Shanghai')) } rawEnvironment:{parsed_transaction.rawEnvironment} price:{parsed_transaction.price}")

    # transaction_history= self.client.get_transaction_history(transaction_id,None,request_params,GetTransactionHistoryVersion.V2)
    # print(f"revision:${transaction_history.revision} ; hasMore:${transaction_history.hasMore} ; rawEnvironment:{transaction_history.rawEnvironment}")
    # transaction_history_result=signed_data_verifier.verify_and_decode_signed_transaction(transaction_history.signedTransactions[0])
    # return transaction_history_result


if __name__ == "__main__":
    # 使用全局客户端
    tools = ApplePayTools(use_sandbox=False)

    # transaction_id = '***************'

    # 获取交易信息
    # transaction_info = tools.get_transaction_info(transaction_id)
    # print(f"交易信息: {transaction_info}")
    # transaction_history=tools.get_transaction_history(transaction_id)
    # print(f"交易历史：{transaction_history}")
    notification_history = tools.get_notification_history()
    # print(f"通知历史记录：{notification_history}")
    # tools.order_refund("6d570253-2f9d-45d0-8c86-8913fdf0ef42")
