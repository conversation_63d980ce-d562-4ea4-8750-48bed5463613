services:
  # app:
  #   build:
  #     context: .
  #     dockerfile: Dockerfile
  #   ports:
  #     - "8000:8000"
  #     - "8010:8010"
  #   volumes:
  #     - .:/app
  #     - ./data:/app/data
  #     - /app/__pycache__
  #   environment:
  #     - REDIS_HOST=redis
  #     - POSTGRES_HOST=postgres
  #     - POSTGRES_USER="abc"
  #     - POSTGRES_PASSWORD="password"
  #     - POSTGRES_DB="readlecture"
  #   env_file:
  #     - .env
  #   depends_on:
  #     - redis
  #     - postgres

  redis:
    image: redis:6.2
    # image: redis:latest
    container_name: redis
    ports:
      - "6379:6379"
    environment:
      - REDISUSER=default # 指定用户名
      - REDISPASSWORD=password # 指定密码
    command: redis-server --requirepass ${REDISPASSWORD}
#   postgres:
#     image: postgres:14
#     container_name: postgres
#     ports:
#       - "5432:5432"
#     environment:
#       POSTGRES_USER: "abc"
#       POSTGRES_PASSWORD: "password"
#       POSTGRES_DB: "readlecture"
#     volumes:
#       - postgres_data:/var/lib/postgresql/data
#
# volumes:
#   postgres_data:
