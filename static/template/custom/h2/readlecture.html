<section
        style="border-bottom-color: rgb(239, 112, 96); margin-top: 30px; margin-bottom: 15px; margin-left: 0px; margin-right: 0px; padding-top: 0px; padding-bottom: 0px; padding-left: 0px; padding-right: 0px; align-items: unset; background-attachment: scroll; background-clip: border-box; background-color: unset; background-image: none; background-origin: padding-box; background-position-x: 0%; background-position-y: 0%; background-repeat: no-repeat; background-size: auto; border-top-style: none; border-bottom-style: solid; border-left-style: none; border-right-style: none; border-top-width: 1px; border-bottom-width: 2px; border-left-width: 1px; border-right-width: 1px; border-top-color: rgb(0, 0, 0); border-left-color: rgb(0, 0, 0); border-right-color: rgb(0, 0, 0); border-top-left-radius: 0px; border-top-right-radius: 0px; border-bottom-left-radius: 0px; border-bottom-right-radius: 0px; box-shadow: none; display: flex; flex-direction: unset; float: unset; height: auto; justify-content: unset; line-height: 1.1em; overflow-x: unset; overflow-y: unset; position: relative; text-align: left; text-shadow: none; transform: none; width: auto; -webkit-box-reflect: unset;">
    <span class="prefix" style="display: none;"></span><span class="content"
                                                             style="font-size: 22px; color: rgb(255, 255, 255); background-color: rgb(239, 112, 96); line-height: 1.5em; letter-spacing: 0em; align-items: unset; background-attachment: scroll; background-clip: border-box; background-image: none; background-origin: padding-box; background-position-x: 0%; background-position-y: 0%; background-repeat: no-repeat; background-size: auto; border-top-style: none; border-bottom-style: none; border-left-style: none; border-right-style: none; border-top-width: 1px; border-bottom-width: 1px; border-left-width: 1px; border-right-width: 1px; border-top-color: rgb(0, 0, 0); border-bottom-color: rgb(0, 0, 0); border-left-color: rgb(0, 0, 0); border-right-color: rgb(0, 0, 0); border-top-left-radius: 3px; border-top-right-radius: 3px; border-bottom-left-radius: 0px; border-bottom-right-radius: 0px; box-shadow: none; display: inline-block; font-weight: bold; flex-direction: unset; float: unset; height: auto; justify-content: unset; margin-top: 0px; margin-bottom: 0px; margin-left: 0px; margin-right: 5px; overflow-x: unset; overflow-y: unset; padding-top: 3px; padding-bottom: 1px; padding-left: 10px; padding-right: 10px; position: relative; text-align: left; text-indent: 0em; text-shadow: none; transform: none; width: auto; -webkit-box-reflect: unset;">{{ text }}</span><span
        class="suffix" style="display: none;"></span><span
        style="border-bottom-color: rgb(239, 235, 233); align-items: unset; background-attachment: scroll; background-clip: border-box; background-color: unset; background-image: none; background-origin: padding-box; background-position-x: 0%; background-position-y: 0%; background-repeat: no-repeat; background-size: auto; border-top-style: none; border-bottom-style: solid; border-left-style: none; border-right-style: solid; border-top-width: 1px; border-bottom-width: 36px; border-left-width: 1px; border-right-width: 20px; border-top-color: rgb(0, 0, 0); border-left-color: rgb(0, 0, 0); border-right-color: transparent; border-top-left-radius: 0px; border-top-right-radius: 0px; border-bottom-left-radius: 0px; border-bottom-right-radius: 0px; box-shadow: none; color: rgb(0, 0, 0); display: inline-block; font-size: 16px; font-weight: bold; flex-direction: unset; float: unset; height: auto; justify-content: unset; letter-spacing: 0px; line-height: 1.1em; margin-top: 0px; margin-bottom: 0px; margin-left: 0px; margin-right: 0px; overflow-x: unset; overflow-y: unset; padding-top: 0px; padding-bottom: 0px; padding-left: 0px; padding-right: 0px; position: relative; text-align: left; text-indent: 0em; text-shadow: none; transform: none; width: auto; -webkit-box-reflect: unset;"> </span>
</section>
