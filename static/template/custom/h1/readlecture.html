<h1
        style="margin-top: 30px; margin-bottom: 15px; margin-left: 0px; margin-right: 0px; align-items: unset; background-attachment: scroll; background-clip: border-box; background-color: transparent; background-image: none; background-origin: padding-box; background-position-x: 0%; background-position-y: 0%; background-repeat: no-repeat; background-size: auto; border-top-style: none; border-bottom-style: none; border-left-style: none; border-right-style: none; border-top-width: 1px; border-bottom-width: 1px; border-left-width: 1px; border-right-width: 1px; border-top-color: rgb(0, 0, 0); border-bottom-color: rgb(0, 0, 0); border-left-color: rgb(0, 0, 0); border-right-color: rgb(0, 0, 0); border-top-left-radius: 0px; border-top-right-radius: 0px; border-bottom-left-radius: 0px; border-bottom-right-radius: 0px; box-shadow: none; display: flex; flex-direction: unset; float: unset; height: auto; justify-content: center; line-height: 1.5em; overflow-x: unset; overflow-y: unset; padding-top: 0px; padding-bottom: 0px; padding-left: 0px; padding-right: 0px; position: relative; text-align: left; text-shadow: none; transform: none; width: auto; -webkit-box-reflect: unset;">
        <span class="prefix"
              style="align-items: unset; background-attachment: scroll; background-clip: border-box; background-color: transparent; background-image: url('https://files.mdnice.com/pic/c6dd0d41-e95d-4d0d-a202-afa9ca0731af.png'); background-origin: padding-box; background-position-x: 0%; background-position-y: 0%; background-repeat: no-repeat; background-size: 100% 100%; border-top-style: none; border-bottom-style: none; border-left-style: none; border-right-style: none; border-top-width: 1px; border-bottom-width: 1px; border-left-width: 1px; border-right-width: 1px; border-top-color: rgb(0, 0, 0); border-bottom-color: rgb(0, 0, 0); border-left-color: rgb(0, 0, 0); border-right-color: rgb(0, 0, 0); border-top-left-radius: 0px; border-top-right-radius: 0px; border-bottom-left-radius: 0px; border-bottom-right-radius: 0px; box-shadow: none; color: rgb(0, 0, 0); display: block; font-size: 22px; font-weight: bold; flex-direction: unset; float: unset; height: 35px; justify-content: unset; letter-spacing: 0px; line-height: 1.5em; margin-top: 0px; margin-bottom: 0px; margin-left: 0px; margin-right: -20px; overflow-x: unset; overflow-y: unset; padding-top: 0px; padding-bottom: 0px; padding-left: 0px; padding-right: 0px; position: relative; text-align: left; text-indent: 0em; text-shadow: none; transform: none; width: 35px; -webkit-box-reflect: unset;"></span><span
        class="content"
        style="font-size: 24px; color: rgb(239, 112, 96); line-height: 1.5em; letter-spacing: 0em; padding-top: 0px; padding-bottom: 0px; padding-left: 0px; padding-right: 0px; align-items: center; background-attachment: scroll; background-clip: border-box; background-color: transparent; background-image: none; background-origin: padding-box; background-position-x: 0%; background-position-y: 0%; background-repeat: no-repeat; background-size: auto; border-top-style: none; border-bottom-style: none; border-left-style: none; border-right-style: none; border-top-width: 1px; border-bottom-width: 1px; border-left-width: 1px; border-right-width: 1px; border-top-color: rgb(0, 0, 0); border-bottom-color: rgb(0, 0, 0); border-left-color: rgb(0, 0, 0); border-right-color: rgb(0, 0, 0); border-top-left-radius: 0px; border-top-right-radius: 0px; border-bottom-left-radius: 0px; border-bottom-right-radius: 0px; box-shadow: none; display: flex; font-weight: bold; flex-direction: unset; float: unset; height: auto; justify-content: unset; margin-top: 0px; margin-bottom: 0px; margin-left: 0px; margin-right: 0px; overflow-x: unset; overflow-y: unset; position: relative; text-align: left; text-indent: 0em; text-shadow: none; transform: none; width: auto; -webkit-box-reflect: unset;">{{text}}</span><span
        class="suffix"
        style="align-items: unset; background-attachment: scroll; background-clip: border-box; background-color: transparent; background-image: url('https://files.mdnice.com/pic/4e116911-86c9-40c7-80ec-bd05e65efa5b.png'); background-origin: padding-box; background-position-x: 0%; background-position-y: 0%; background-repeat: no-repeat; background-size: 100% 100%; border-top-style: none; border-bottom-style: none; border-left-style: none; border-right-style: none; border-top-width: 1px; border-bottom-width: 1px; border-left-width: 1px; border-right-width: 1px; border-top-color: rgb(0, 0, 0); border-bottom-color: rgb(0, 0, 0); border-left-color: rgb(0, 0, 0); border-right-color: rgb(0, 0, 0); border-top-left-radius: 0px; border-top-right-radius: 0px; border-bottom-left-radius: 0px; border-bottom-right-radius: 0px; box-shadow: none; color: rgb(0, 0, 0); display: block; font-size: 22px; font-weight: bold; flex-direction: unset; float: unset; height: 15px; justify-content: unset; letter-spacing: 0px; line-height: 1.5em; margin-top: 0px; margin-bottom: 0px; margin-left: 0px; margin-right: 0px; overflow-x: unset; overflow-y: unset; padding-top: 0px; padding-bottom: 0px; padding-left: 0px; padding-right: 0px; position: relative; text-align: left; text-indent: 0em; text-shadow: none; transform: none; width: 15px; -webkit-box-reflect: unset;"></span>
</h1>
