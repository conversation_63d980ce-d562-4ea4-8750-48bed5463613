<!doctype html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="color-scheme" content="light dark" />
    <title> </title>
    <style type="text/css">
      * {
        margin: 0;
      }
      table,
      td {
        mso-table-lspace: 0pt;
        mso-table-rspace: 0pt;
      }
      pre {
        white-space: pre-wrap;
        word-wrap: break-word;
      }
      .email-column-container {
        table-layout: fixed;
        width: 100%;
      }
      .email-column {
        vertical-align: top;
      }
      @media screen and (max-width: 500px) {
        .email-container {
          width: 100% !important;
        }
        .email-column,
        .email-column-spacer {
          width: 100% !important;
          display: block;
        }
        .email-column-spacer {
          height: 24px;
        }
        .mobile-center {
          text-align: center !important;
        }
        .mobile-right {
          text-align: right !important;
        }
        .mobile-left {
          text-align: left !important;
        }
        .mobile-hidden {
          max-height: 0;
          display: none !important;
          mso-hide: all;
          overflow: hidden;
        }
      }
      @media screen and (prefers-color-scheme: dark) {
        body,
        table,
        .email-content {
          background: #0f1721 !important;
        }
        .email-wrapper td {
          background: #1b232c !important;
        }
        .email-link {
          color: #66bfff !important;
          text-decoration-color: #66bfff !important;
        }
        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
          color: #e7e8e9 !important;
        }
        p,
        ul,
        ol,
        span {
          color: #cfd1d3 !important;
        }
        quote,
        pre {
          color: #cfd1d3 !important;
        }
      }
    </style>
  </head>
  <body
    style="
      background: #f1f3f5;
      padding: 0px;
      font-family: Helvetica, Arial, sans-serif;
      font-size: 16px;
      line-height: 1.618;
      color: #333333;
      margin: 0px;
    "
  >
    <table cellpadding="0" cellspacing="0" width="100%" role="presentation">
      <tr>
        <td class="email-body" align="center" style="padding: 32px 20px 64px">
          <table
            class="email-container"
            width="600"
            cellpadding="0"
            cellspacing="0"
            role="presentation"
          >
            <tr>
              <td
                class="email-content"
                style="
                  margin: 0px;
                  background: #ffffff;
                  padding: 40px;
                  border-radius: 10px;
                "
              >
                <h2
                  style="
                    letter-spacing: 0px;
                    text-transform: none;
                    font-size: 24px;
                    font-weight: bold;
                    line-height: 1.3;
                    color: #000000;
                    font-family: Helvetica, Arial, sans-serif;
                    margin-top: 0px;
                    margin-bottom: 32px;
                    mso-line-height-rule: exactly;
                  "
                >
                  Hi <span data-block="mergetag"> {{user_name}} </span>,<br />
                </h2>
                <p
                  style="
                    font-family: Helvetica, Arial, sans-serif;
                    font-size: 16px;
                    line-height: 26px;
                    margin-top: 0px;
                    margin-bottom: 32px;
                  "
                >
                  您的任务<span data-block="mergetag">【{{task_name}}】</span
                  >已经完成了转换，请登录
                  <a
                    href="https://aihaoji.com"
                    style="color: #046bfb; text-decoration: underline"
                    target="_blank"
                    class="email-link"
                    >aihaoji.com</a
                  >
                </p>
                <table
                  class="email-table email-button"
                  width="100%"
                  cellpadding="0"
                  cellspacing="0"
                  border="0"
                  role="presentation"
                  style="margin-top: 0px; margin-bottom: 32px"
                >
                  <tbody>
                    <tr>
                      <td
                        bgcolor="#000000"
                        align="center"
                        style="
                          border-radius: 8px;
                          font-size: 16px;
                          mso-padding-alt: 16px 0px;
                        "
                      >
                        <a
                          href="https://aihaoji.com/zh/dashboard/tasks/view/article/{{task_id}}"
                          class="email-button"
                          style="
                            padding: 16px 0px;
                            font-size: 16px;
                            font-weight: 500;
                            background: #000000;
                            color: #ffffff;
                            border-radius: 8px;
                            cursor: pointer;
                            line-height: 1;
                            text-decoration: none;
                            text-align: center;
                            width: 100%;
                            margin: 0px;
                            display: inline-block;
                            font-family: Helvetica, Arial, sans-serif;
                            mso-line-height-rule: exactly;
                          "
                          target="_blank"
                          >去查看→</a
                        >
                      </td>
                    </tr>
                  </tbody>
                </table>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  </body>
</html>
