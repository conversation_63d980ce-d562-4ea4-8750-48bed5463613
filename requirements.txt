urllib3 == 1.26.6
requests >= 2.25.1
werobot >= 1.13.1
fastapi ~= 0.110.2
peewee == 3.17.3
uvicorn[standard] >= 0.29.0
python-dotenv >= 1.0.1
psycopg2-binary >= 2.9.9
python-multipart >= 0.0.9
PyJWT >= 2.1.0
cachetools >= 4.2.2
itsdangerous >= 2.0.1
qiniu >= 7.3.0
svix >= 0.3.0
pytz~=2024.1
isodate~=0.6.1
apscheduler~=3.10.0
python-dateutil~=2.8.2
mistune~=2.0.0
bcrypt~=3.2.0
pyotp~=2.6.0
wechatpy~=1.8.18
optionaldict~=0.1.2
dogpile.cache~=1.3.3
cryptography>=3.4.7
lxml~=5.4.0
jinja2~=3.0.0
beautifulsoup4~=4.10.0
python-alipay-sdk~=3.3.0
aliyun-python-sdk-core-v3==2.13.33
redis==5.2.1
opentelemetry-api==1.29.0
opentelemetry-sdk==1.29.0
opentelemetry-exporter-prometheus==0.50b0
opentelemetry-instrumentation-fastapi==0.50b0
opentelemetry-instrumentation-asgi==0.50b0
OpenAI ~= 1.58.1
ZhipuAI ~= 2.1.4
pdfkit ~= 1.0.0
pypandoc==1.11
jsonschema~=4.23.0
markdown_to_json~=2.1.2
iso639-lang~=2.5.1
celery~=5.4.0
sse-starlette~=1.6.1
pypandoc_binary~=1.15
json_repair~=0.39.1
aiofiles~=24.1.0
aiohttp~=3.11.13
alibabacloud_captcha20230305==1.1.3
alibabacloud_credentials==1.0.2
pydantic_settings~=2.9.1
pillow~=11.2.1
email_validator==2.2.0
fastapi-limiter==0.1.6
SQLAlchemy==2.0.41
asyncpg==0.30.0
app-store-server-library==1.9.0
json_repair~=0.39.1
pydantic==2.11.4
starlette~=0.37.2
playhouse~=0.1.0
httpx~=0.28.1
alibabacloud_tea_openapi~=0.3.14
email_validator~=2.2.0
prometheus_client~=0.22.0
wheel~=0.45.1
gmssl==3.2.2
msgpack==1.1.1