#!/bin/bash

# 设置日志文件路径
LOG_FILE="/root/logs/celery_log.log"
# LOG_FILE="celery_log.log"

# 检查 celery 是否安装
if ! command -v celery &>/dev/null; then
    echo "Celery command not found. Exiting..."
    exit 1
fi

# 查找当前运行的 Celery 进程并杀死它
celery_pid=$(ps aux | grep 'celery worker' | grep -v 'grep' | awk '{print $2}')
if [ -n "$celery_pid" ]; then
    echo "Stopping existing Celery process with PID: $celery_pid"
    kill -9 $celery_pid
    echo "Celery process stopped."
else
    echo "No existing Celery process found."
fi

# 启动新的 Celery Worker 并让它在后台运行
echo "Starting a new Celery worker..."
nohup celery -A src.util.celery_util.celery worker --loglevel=info >>$LOG_FILE 2>&1 &

echo "New Celery worker started and logs are being written to $LOG_FILE"
