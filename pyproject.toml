[tool.poetry]
package-mode = false
name = "src"
version = "1.0.0"
description = "web-server for lecture domain"
authors = ["fulln <<EMAIL>>"]

[tool.poetry.dependencies]
python = "^3.10"
urllib3 = "1.26.6"
requests = "^2.25.1"
werobot = "^1.13.1"
fastapi = "^0.110.2"
peewee = "^3.17.3"
uvicorn = { extras = ["standard"], version = "^0.29.0" }
python-dotenv = "^1.0.1"
psycopg2-binary = "^2.9.9"
python-multipart = "^0.0.9"
itsdangerous = "^2.0.1"
pyjwt = "^2.1.0"
qiniu = "^7.3.0"
svix = "^0.1.0"
pytz = "^2024.1"
isodate = "^0.6.1"
apscheduler = "^3.8.0"

[tool.poetry.dev-dependencies]
pytest = "^6.2.2"

[tool.poetry.scripts]
start = "poetry run uvicorn src.main:app --reload"

[tool.ruff]
target-version = "py310"
line-length = 120
indent-width = 4
fixable = ["ALL"]
unfixable = []

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
