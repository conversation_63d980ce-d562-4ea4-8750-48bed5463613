 name: Deploy to Test

 on:
   push:
     branches:
       - test
#
 jobs:
   deploy:
     runs-on: ubuntu-latest

     steps:
       - name: Checkout code
         uses: actions/checkout@v2

       - name: Setup SSH
         uses: webfactory/ssh-agent@v0.5.3
         with:
           ssh-private-key: ${{ secrets.SSH_PRIVATE_TEST_KEY }}

       - name: get .env file
         run: |
           echo "${{ secrets.ENV_TEST_FILE }}" > .env

       - name: Generate timestamped directory and create remote directory
         run: |
           dir=$(date +'%Y%m%d%H%M%S')
           echo "dir=$dir" >> $GITHUB_ENV
           tar -czf build.tar.gz ./* .env
           ssh -o StrictHostKeyChecking=no root@${{ secrets.REMOTE_TEST_IP }} "mkdir -p ~/opt/work/backend/$dir/"

       - name: Copy compiled code to server
         run: |
           scp -o StrictHostKeyChecking=no build.tar.gz root@${{ secrets.REMOTE_TEST_IP }}:~/opt/work/backend/${{ env.dir }}/
           ssh -o StrictHostKeyChecking=no root@${{ secrets.REMOTE_TEST_IP }} "cd ~/opt/work/backend/${{ env.dir }} && tar -xzf build.tar.gz && rm -f build.tar.gz"
           ssh -o StrictHostKeyChecking=no root@${{ secrets.REMOTE_TEST_IP }} "cd ~/opt/work/backend/ && /root/anaconda3/bin/python delete.py"

       - name: Restart backend service
         run: |
           ssh -o StrictHostKeyChecking=no root@${{ secrets.REMOTE_TEST_IP }} "cd  ~/opt/work/backend/${{ env.dir }} && sh test_run.sh"