from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    GREETINGS: str = "Hello AihaoJi!"

    LLM_SECRET_KEY: str

    # Postgres 数据库配置
    DB_NAME: str
    DB_USER: str
    DB_PASSWORD: str
    DB_HOST: str
    DB_PORT: int
    # DB_MAX_CONNECTIONS: int = 64
    DB_MAX_CONNECTIONS: int = 80
    DB_STALE_TIMEOUT: int = 120

    # redis 配置
    REDISHOST: str
    REDISPORT: int = 6379
    REDIS_MAX_CONECTIONS: int = 100
    REDISUSER: str
    REDISPASSWORD: str
    REDIS_EVENT_CHANNEL: str = "event_channel"

    PROMETHEUS_PORT: int = 8010

    # AI model config
    ZHIPU_BASE_URL: str = "https://open.bigmodel.cn/api/paas/v4/"
    ZHIPU_API_KEY: str
    ZHIPU_MODEL: str = "glm-4-plus"
    ZHIPU_R1_MODEL: str = "glm-4-plus"

    DEEPSEEK_BASE_URL: str = "https://api.deepseek.com/v1"
    DEEPSEEK_API_KEY: str
    DEEPSEEK_MODEL: str = "deepseek-chat"

    QIANWEN_BASE_URL: str = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    QIANWEN_API_KEY: str
    QIANWEN_MODEL: str = "qwen-plus"

    DOUBAO_BASE_URL: str = "https://ark.cn-beijing.volces.com/api/v3"
    DOUBAO_API_KEY: str
    DOUBAO_MODEL: str = "ep-20250213213316-v6np4"
    DOUBAO_R1_MODEL: str = "ep-20250309015405-5b76p"

    # mail service config
    STMP_SERVER: str = "smtpdm.aliyun.com"
    STMP_PORT: int = 80
    STMP_SENDER: str = "<EMAIL>"
    STMP_PASSWORD: str

    QINIU_ACCESS_KEY: str = ""
    QINIU_SECRET_KEY: str = ""
    APP_DEBUG: bool = False

    GOOGLE_YOUTUBE_API: str
    LOG_TO_PATH: str = "./log/web-info.log"
    LOG_TO_STREAM: bool = False

    CUSTOMER_SERVICE_SEND: str = "user_2iEk6vhk6YazR8498QLc26g7yM7"
    FREE_ARTICLE: str = "98b2491d-4f39-be77-6266-0c5948bb6bbe,f568dd61-dc86-bad5-afb4-f63f3da264a6,f571ae8e-3eb2-f319-c990-ea74226e84a9,b45d3c35-b179-461b-3d12-79cc80ca087a,6e73a3e8-e370-3843-2af4-8f455b7f24fa,880fdea3-46d9-fca6-abf0-2e7d5e2b39e3,12ffeb7f-0b0d-1172-ac5a-b6725d1f8285,3447813b-5614-a89c-2902-151d3937aa5b,76d278ed-cee7-c904-44ab-49ce7bf18e89,e16a6971-b42c-d0f5-9cf3-77dcb0994fb2,dc30a9ef-2ec9-3ba4-db73-d8087a426c0f,a7d22d6c-30d7-d913-f692-3c1aeb724a0b,77019840-305c-677d-d11d-f82446f92ba4,58123f3b-2fdc-728e-6a8b-6009d978bb4d,e3946346-3a44-f284-8c48-96553cba36a5,27fa127a-afdc-6c5d-8054-51fc14cb7932,ba7fad8d-02ab-34da-72f9-580da867b95a,e6eaf164-5f60-b6b5-0347-061615a5cdb5,cec5db52-609e-20ba-f59e-650634db25ad,8c5fcdfb-2d7e-f846-8d4a-2b95d6bbf031,45d348bc-f67d-9c0d-231c-bb4279e1ee94,0f64feb9-0277-94f9-2229-26dc2b269498,48b7028c-02a5-087f-e508-6d86ea74723e,3daffdad-439e-569d-ebd6-b7de6577670d,3aee4216-7f33-b713-93f4-88cd8efd4f1b"

    COS_SECRET_ID: str
    COS_SECRET_KEY: str

    CACHE_FILE: str = "data/cache_data.json"
    OPEN_CACHE_FILE: str = "data/open_cache_data.json"

    WECHAT_TOKEN: str = "readlecture"
    WECHAT_REDIRECT_URI: str = "https://aihaoji.com/api/v1/wechat/auth"
    WECHAT_REDIRECT_BIZ: str = 'MzkwMjgyOTc5Mg'
    WECHAT_APP_ID: str
    WECHAT_MOBILE_APP_ID:str
    WECHAT_APP_SECRET: str
    WECHAT_MOBILE_APP_SECRET:str
    WECHAT_PAY_APP_ID: str = ""
    WECHAT_PAY_APP_SECRET: str = ""
    WECHAT_API_KEY: str
    DOMAIN: str = "https://aihaoji.com"

    SEM_BAIDU_TOKEN: str

    DOWNLOAD_PREFIX: str = "./downloads"
    AUDIO2BOOK_UPLOAD_DIR: str = "./uploads"

    APPLE_SECRET_KEY_ID :str="K2FBY6A2CU"
    APPLE_ISSUER_ID :str="b8a266d0-655a-468a-be8c-f526111da2df"
    APPLE_BUNDLE_ID:str="com.aihaoji.ios"
    APPLE_APP_ID:int=6744632981



    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        extra="ignore",  # 忽略未知变量
    )


settings = Settings()  # type: ignore[reportCallIssue]

assert settings.GREETINGS == "Hello AihaoJi!"
