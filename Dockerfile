# 使用官方 Python 镜像, 测试环境 smart-burst 也是支持 Dockerfile 部署的
FROM python:3.10-slim

# 设置工作目录
WORKDIR /app

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 这是安装 PDF 相关
RUN apt-get update && \
    apt-get install --yes --no-install-recommends \
    ca-certificates \
    make \
    lmodern \
    texlive-xetex \
    texlive-lang-chinese \
    fonts-noto-cjk \
    texlive-fonts-recommended && \
    apt-get autoclean && apt-get --purge --yes autoremove && \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# 将依赖文件复制到容器中
COPY requirements.txt .

# 安装依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制项目文件到容器
COPY . .

# 暴露应用端口
EXPOSE 8000

# 启动 Celery worker, FastAPI
CMD ["sh", "test_run.sh"]
