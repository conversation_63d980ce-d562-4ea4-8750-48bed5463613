#! /bin/sh
# 停止服务
pkill -f "uvicorn src.main:app"
sleep 2

# 确保进程已停止
if pgrep -f "uvicorn src.main:app" >/dev/null; then
    echo "强制终止进程..."
    pkill -9 -f "uvicorn src.main:app"
fi

mkdir -p /root/logs

# if ps aux | grep 'celery worker' | grep -v 'grep' > /dev/null; then
#     echo "Celery 正在运行。"
# else
#     echo "Celery 未运行，正在启动..."
#     # 设置日志文件路径
#     # 启动 Celery Worker
#     nohup /root/anaconda3/bin/celery -A src.util.celery_util.celery worker --loglevel=info >>/root/logs/celery_log.log 2>&1 &
#     echo "Celery 已启动，日志写入到 /root/logs/celery_log.log"
# fi

# 查找当前运行的 Celery 进程并杀死它
celery_pid=$(ps aux | grep 'celery worker' | grep -v 'grep' | awk '{print $2}')
if [ -n "$celery_pid" ]; then
    echo "Stopping existing Celery process with PID: $celery_pid"
    kill -9 $celery_pid
    echo "Celery process stopped."
else
    echo "No existing Celery process found."
fi

nohup /root/anaconda3/bin/celery -A src.util.celery_util.celery worker --loglevel=info >>/root/logs/celery_log.log 2>&1 &

mkdir -p log
# 启动服务
echo "启动服务..."
/root/anaconda3/bin/pip install -r requirements.txt

nohup /root/anaconda3/bin/uvicorn src.main:app --host 0.0.0.0 --port 8000 --log-level info &> /root/logs/web-info.log &

echo "服务已重启"
