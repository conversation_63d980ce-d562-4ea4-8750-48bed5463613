# ReadLecture-webServer

## 开发依赖

- Python3.10
- FastAPI
- peewee (ORM)
- wechatpy (微信公众号能力)
- pydantic_settings (环境变量管理)
- pre-commit
  - [install pre-commit](https://pre-commit.com/#installation)
  - `pre-commit install`

## 如何本地运行与开发

- create _.env_ file on root (找余帅要这个 .env 文件，放在根目录)
- `docker compose up --build # 运行本地 redis`
- `pip install requirements.txt # 建议使用虚拟环境`
- `uvicorn src.main:app --reload`
- 使用 Insomnia 在 local 环境进行请求调试开发

## 其它

- [前端项目](https://github.com/ReadLecture/readlecture-frondend)

## 📌 TODO(技术债务)

- [ ] 自动化测试
- [ ] 统一使用 UTC 时间
- [ ] 接入 Sentry
- [ ] Grafana 告警规则整理
- [ ] 替换 peewee 为 SQLAlchemy
- [ ] 替换 requests 为 aiohttp
- [ ] 替换 redis 为 aioredis
- [ ] 定时删除 ECS1 /tmp 下面的导出文件夹及图片文件
- [ ] github action 支持在部署的时候同时删除旧的文件夹
