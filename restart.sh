#!/bin/sh

pip install -r requirements.txt


# ---------------------Start Celery Worker ----------------------------------
# 设置日志文件路径
LOG_FILE="/root/logs/celery_log.log"
# LOG_FILE="celery_log.log"

# 检查 celery 是否安装
if ! command -v celery &>/dev/null; then
    echo "Celery command not found. Exiting..."
    exit 1
fi

# 查找当前运行的 Celery 进程并杀死它
celery_pid=$(ps aux | grep 'celery worker' | grep -v 'grep' | awk '{print $2}')
if [ -n "$celery_pid" ]; then
    echo "Stopping existing Celery process with PID: $celery_pid"
    kill -9 $celery_pid
    echo "Celery process stopped."
else
    echo "No existing Celery process found."
fi

# 启动新的 Celery Worker 并让它在后台运行
echo "Starting a new Celery worker..."
nohup celery -A src.util.celery_util.celery worker --loglevel=info >>$LOG_FILE 2>&1 &

echo "New Celery worker started and logs are being written to $LOG_FILE"
# ---------------------End Celery Worker ----------------------------------


# ---------------------Start FastAPI Webserver ----------------------------------
# Find the PID of the running uvicorn process
PID=$(pgrep -f "uvicorn src.main:app")

# If a PID is found, kill the process
if [ -n "$PID" ]; then
  echo "Killing uvicorn process with PID: $PID"
  kill $PID

  # Wait for the process to terminate
  for i in {1..10}; do
    if ! ps -p $PID > /dev/null; then
      echo "Successfully terminated uvicorn process with PID: $PID"
      break
    fi
    sleep 0.5
  done

  # If the process is still running, force kill it
  if ps -p $PID > /dev/null; then
    echo "Force killing uvicorn process with PID: $PID"
    kill -9 $PID
  fi
fi

# Start a new uvicorn process
echo "Starting uvicorn process"
nohup uvicorn src.main:app --host 0.0.0.0 --port 8000  --timeout-keep-alive 10 --log-level info > /dev/null 2>&1 &
# ---------------------End  FastAPI Webserver ----------------------------------
